package shyrcs.Ability;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Quản lý cooldown và hiển thị trên ActionBar
 * Tương tự như MMOItems với hiển thị liên tục
 */
public class CooldownManager {
    
    private static CooldownManager instance;
    private final Plugin plugin;
    
    // Map lưu trữ cooldown của từng player cho từng skill
    private final Map<UUID, Map<String, Long>> playerCooldowns = new ConcurrentHashMap<>();
    
    // Map lưu trữ thời gian cooldown cho từng skill type
    private final Map<String, Integer> skillCooldowns = new HashMap<>();

    // Task hiển thị buff time còn lại
    private BukkitRunnable buffTimeTask;
    
    private CooldownManager(Plugin plugin) {
        this.plugin = plugin;
        // Bắt đầu task theo dõi buff time
        startBuffTimeTask();
    }
    
    public static CooldownManager getInstance(Plugin plugin) {
        if (instance == null) {
            instance = new CooldownManager(plugin);
        }
        return instance;
    }
    
    public static CooldownManager getInstance() {
        return instance;
    }
    
    /**
     * Đặt cooldown cho player với skill cụ thể
     */
    public void setCooldown(Player player, String skillType, int cooldownSeconds) {
        UUID playerId = player.getUniqueId();
        long cooldownEnd = System.currentTimeMillis() + (cooldownSeconds * 1000L);
        
        playerCooldowns.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>())
                      .put(skillType, cooldownEnd);
        
        skillCooldowns.put(skillType, cooldownSeconds);
    }
    
    /**
     * Kiểm tra xem player có đang trong cooldown không
     */
    public boolean isOnCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns == null) {
            return false;
        }
        
        Long cooldownEnd = playerSkillCooldowns.get(skillType);
        if (cooldownEnd == null) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        if (currentTime >= cooldownEnd) {
            // Cooldown đã hết, xóa khỏi map
            playerSkillCooldowns.remove(skillType);
            if (playerSkillCooldowns.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
            return false;
        }
        
        return true;
    }
    
    /**
     * Lấy thời gian cooldown còn lại (giây)
     */
    public long getRemainingCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns == null) {
            return 0;
        }
        
        Long cooldownEnd = playerSkillCooldowns.get(skillType);
        if (cooldownEnd == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long remaining = (cooldownEnd - currentTime) / 1000;
        
        return Math.max(0, remaining);
    }
    
    /**
     * Xóa tất cả cooldown của player
     */
    public void clearCooldowns(Player player) {
        playerCooldowns.remove(player.getUniqueId());
    }
    
    /**
     * Xóa cooldown cụ thể của player
     */
    public void clearCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns != null) {
            playerSkillCooldowns.remove(skillType);
            if (playerSkillCooldowns.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
        }
    }
    

    
    /**
     * Bắt đầu task theo dõi buff time còn lại
     */
    private void startBuffTimeTask() {
        if (buffTimeTask != null) {
            buffTimeTask.cancel();
        }

        buffTimeTask = new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    checkAndDisplayBuffTime(player);
                }
            }
        };

        // Chạy mỗi 10 ticks (0.5 giây) để smooth
        buffTimeTask.runTaskTimer(plugin, 0L, 10L);
    }

    /**
     * Kiểm tra và hiển thị buff time còn lại nếu < 3 giây
     */
    private void checkAndDisplayBuffTime(Player player) {
        StringBuilder actionBarText = new StringBuilder();
        boolean hasShortBuff = false;

        // Kiểm tra tất cả potion effects của player
        for (PotionEffect effect : player.getActivePotionEffects()) {
            int remainingSeconds = effect.getDuration() / 20; // Chuyển từ tick sang giây

            // Chỉ hiển thị buff còn dưới 3 giây và là buff tích cực
            if (remainingSeconds <= 3 && remainingSeconds > 0 && BuffUtils.isPositiveEffect(effect.getType())) {
                if (hasShortBuff) {
                    actionBarText.append(" §7| ");
                }

                String buffNameVi = BuffPlaceholderUtil.getBuffNameVi(effect.getType());
                String levelRoman = BuffUtils.toRoman(effect.getAmplifier() + 1);
                actionBarText.append("§e").append(buffNameVi).append(" ").append(levelRoman)
                           .append(": §c").append(remainingSeconds).append("s");
                hasShortBuff = true;
            }
        }

        if (hasShortBuff) {
            BuffUtils.sendActionBar(player, actionBarText.toString());
        }
    }

    /**
     * Dừng CooldownManager
     */
    public void shutdown() {
        if (buffTimeTask != null) {
            buffTimeTask.cancel();
            buffTimeTask = null;
        }
        playerCooldowns.clear();
        skillCooldowns.clear();
    }
    
    /**
     * Lấy tất cả cooldown hiện tại của player (cho debug)
     */
    public Map<String, Long> getPlayerCooldowns(Player player) {
        Map<String, Long> cooldowns = playerCooldowns.get(player.getUniqueId());
        return cooldowns != null ? new HashMap<>(cooldowns) : new HashMap<>();
    }
}
