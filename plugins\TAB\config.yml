header-footer:
  enabled: true
  header:
  - ''
  - '&aTPS: %tps% Player: %online% Ping: %ping%ms'
  - ''
  - ''
  - '&f%img_logo%'
  - ''
  - '&f'
  - '&f'
  - ''
  - ''
  footer:
  - ''
  - '%animation:Huongdan1%'
  - ''
  - '%animation:Huongdan2%'
  - ''
  disable-condition: '%world%=disabledworld'
  per-world:
    world1:
      header:
      - an example of world with custom
      footer:
      - header/footer and prefix/suffix
    world2;world3:
      header:
      - This is a shared header for
      - world2 and world3
  per-server:
    server1:
      header:
      - an example of server with custom header
tablist-name-formatting:
  enabled: true
  anti-override: true
  disable-condition: '%world%=disabledworld'
scoreboard-teams:
  enabled: true
  enable-collision: true
  invisible-nametags: false
  anti-override: true
  sorting-types:
  - PLACEHOLDER_HIGH_TO_LOW:%luckperms_meta_weight%
  - PLACEHOLDER_A_TO_Z:%player%
  case-sensitive-sorting: true
  can-see-friendly-invisibles: false
  disable-condition: '%world%=disabledworld'
playerlist-objective:
  enabled: false
  value: '%ping%'
  fancy-value: ' %ping%'
  title: Java Edition is better
  render-type: INTEGER
  disable-condition: '%world%=disabledworld'
belowname-objective:
  enabled: true
  value: '%health%'
  title: '&c'
  fancy-value: '&c%img_heart% &8[&c%health%&8] &f%img_shield% &8[&b%mmoitems_stat_armor%&f/&b%mmoitems_stat_defense%&8] &7○&a %ping%ms'
  fancy-value-default: NPC
  disable-condition: '%world%=disabledworld'
prevent-spectator-effect:
  enabled: true
bossbar:
  enabled: false
  toggle-command: /bossbar
  remember-toggle-choice: false
  hidden-by-default: false
  bars:
    ServerInfo:
      style: PROGRESS
      color: '%animation:barcolors%'
      progress: '100'
      text: '&aChủ Đề Build: &fLịch Sử'
scoreboard:
  enabled: true
  toggle-command: /sb
  remember-toggle-choice: false
  hidden-by-default: false
  use-numbers: true
  static-number: 0
  delay-on-join-milliseconds: 0
  scoreboards:
    scoreboard-1.20.3+:
      title: '%img_smalllogo%'
      display-condition: '%player-version-id%>=765;%bedrock%=false'
      lines:
      - ''
      - ''
      - ''
      - '&8| &f👤 &8[&a%player%&8]'
      - '&8| &fʀᴀɴᴋ: %luckperms_prefix%'
      - ''
      - '  &#FBFF41ᴍᴏɴᴇʏ: %vault_eco_balance_commas% &r%img_coins%'
      - '  &#B54BF3ꜱᴏᴜʟ: &r&5%playerpoints_points% &f%img_purple_orb%'
      - '  &#0AF4FFᴄʀʏꜱᴛᴀʟ: &b%coinsengine_balance_crystal%'
      - '  &#2CE041ᴇxᴘ: &a%player_level% &r%img_experience_orb%'
      - ''
      - '&8| &f%img_star% &7%superior_island_top_level_position%'
      - '&8| &f%img_level_up% &7%superior_island_level%'
      - ''
      - '&5sᴏᴜʟᴍᴄ.ᴠɴ'
    scoreboard:
      title: '%img_smalllogo%'
      lines:
      - ''
      - ''
      - '&8| &f👤 &8[&a%player%&8]'
      - '&8| &fʀᴀɴᴋ: %luckperms_prefix%'
      - ''
      - '  &#FBFF41ᴍᴏɴᴇʏ: %vault_eco_balance_commas% &r%img_coins%'
      - '  &#B54BF3ꜱᴏᴜʟ: &r&5%playerpoints_points% &f%img_purple_orb%'
      - '  &#0AF4FFᴄʀʏꜱᴛᴀʟ: &b%coinsengine_balance_crystal%'
      - '  &#2CE041ᴇxᴘ: &a%player_level% &r%img_experience_orb%'
      - ''
      - '&8| &fᴛᴏᴘ: &7%superior_island_top_level_position%'
      - '&8| &fʟᴇᴠᴇʟ: &7%superior_island_level%'
      - ''
      - '&5sᴏᴜʟᴍᴄ.ᴠɴ'
layout:
  enabled: True
  direction: COLUMNS
  default-skin: mineskin:383747683
  enable-remaining-players-text: true
  remaining-players-text: '... and %s more'
  empty-slot-ping-value: 1000
  layouts:
    default:
      fixed-slots:
      - ''
      - 2|&6Sự Kiện Tháng 6
      - '3|&8&m                       '
      - '4|&bKhuyến Mãi Nạp Thẻ'
      - 5|&6[Hạn:]
      - '7|&bĐua Top Build // Đảo // GUILD'
      - '8|&bĐua Top Donate'
      - '9|&bĐua Top Crystal // Money'
      - 10|&6[Hạn:]
      - '12|&eEvent Hè 2025'
      - 13|&6[Hạn:]
      - '15|&aIP Server:'
      - 16|&bSoulmc.vn
      - '17|&8&m                       '
      - '18|&5Discord&f:'
      - 19|&bdiscord.soulmc.vn
      - '20|&8&m                       '
      groups:
        # staff:
        #   condition: permission:tab.staff
        #   slots:
        #   - 21-40
        # players:
        #   slots:
        #   - 41-80
        players:
          slots:
          - 21-80
ping-spoof:
  enabled: false
  value: 0
placeholders:
  date-format: dd.MM.yyyy
  time-format: '[HH:mm:ss / h:mm a]'
  time-offset: 0
  register-tab-expansion: true
placeholder-output-replacements:
  '%essentials_afk%':
    'yes': '&f &c&o&lᴀғᴋ&r'
    'no': ''
  '%essentials_vanished%':
    'yes': '&7| Vanished'
    'no': ''
  '%essentials_nickname%':
    '%essentials_nickname%': '%player%'
conditions:
  nick:
    conditions:
    - '%player%=%essentials_nickname%'
    true: '%essentials_nickname%'
    false: ~%essentials_nickname%
placeholder-refresh-intervals:
  default-refresh-interval: 500
  '%server_uptime%': 1000
  '%server_tps_1_colored%': 1000
  '%server_unique_joins%': 5000
  '%player_health%': 200
  '%player_ping%': 1000
  '%vault_prefix%': 1000
  '%rel_factionsuuid_relation_color%': 1000
assign-groups-by-permissions: false
primary-group-finding-list:
- Owner
- Admin
- Mod
- Helper
- default
permission-refresh-interval: 1000
debug: false
mysql:
  enabled: false
  host: 127.0.0.1
  port: 3306
  database: tab
  username: user
  password: password
  useSSL: true
proxy-support:
  enabled: true
  type: PLUGIN
  plugin:
    name: RedisBungee
  redis:
    url: redis://:password@localhost:6379/0
  rabbitmq:
    exchange: plugin
    url: amqp://guest:guest@localhost:5672/%2F
per-world-playerlist:
  enabled: false
  allow-bypass-permission: false
  ignore-effect-in-worlds:
  - ignoredworld
  - build
  shared-playerlist-world-groups:
    lobby:
    - lobby1
    - lobby2
    minigames:
    - paintball
    - bedwars
compensate-for-packetevents-bug: false
global-playerlist:
  enabled: false
  display-others-as-spectators: false
  display-vanished-players-as-spectators: true
  isolate-unlisted-servers: false
  update-latency: false
  spy-servers:
  - spyserver1
  - spyserver2
  server-groups:
    lobbies:
    - lobby1
    - lobby2
    group2:
    - server1
    - server2
use-bukkit-permissions-manager: false
use-online-uuid-in-tablist: true
