package shyrcs.Ability;

import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.entity.Player;
import org.bukkit.block.Block;

public class ExpMineEvent extends Event {
    private static final HandlerList handlers = new HandlerList();
    private final Player player;
    private final Block block;
    private final double expMineValue;
    private final int bonusExp;
    
    public ExpMineEvent(Player player, Block block, double expMineValue, int bonusExp) {
        this.player = player;
        this.block = block;
        this.expMineValue = expMineValue;
        this.bonusExp = bonusExp;
    }
    
    public Player getPlayer() {
        return player;
    }
    
    public Block getBlock() {
        return block;
    }
    
    public double getExpMineValue() {
        return expMineValue;
    }
    
    public int getBonusExp() {
        return bonusExp;
    }
    
    @Override
    public HandlerList getHandlers() {
        return handlers;
    }
    
    public static HandlerList getHandlerList() {
        return handlers;
    }
}