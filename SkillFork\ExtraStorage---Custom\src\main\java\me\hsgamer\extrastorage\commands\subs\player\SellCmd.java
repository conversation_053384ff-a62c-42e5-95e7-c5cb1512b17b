package me.hsgamer.extrastorage.commands.subs.player;

import java.util.Optional;

import org.bukkit.entity.Player;

import me.hsgamer.extrastorage.api.item.Item;
import me.hsgamer.extrastorage.api.storage.Storage;
import me.hsgamer.extrastorage.api.user.User;
import me.hsgamer.extrastorage.commands.abstraction.Command;
import me.hsgamer.extrastorage.commands.abstraction.CommandContext;
import me.hsgamer.extrastorage.commands.abstraction.CommandListener;
import me.hsgamer.extrastorage.commands.abstraction.CommandTarget;
import me.hsgamer.extrastorage.configs.Message;
import me.hsgamer.extrastorage.data.Constants;
import me.hsgamer.extrastorage.gui.SellGui;
import me.hsgamer.extrastorage.util.Digital;
import me.hsgamer.extrastorage.util.Utils;

@Command(value = "sell", permission = Constants.PLAYER_SELL_PERMISSION, target = CommandTarget.ONLY_PLAYER)
public final class SellCmd
        extends CommandListener {

    @Override
    public void execute(CommandContext context) {
        Player player = context.castToPlayer();

        if (context.getArgsLength() == 0) {
            new SellGui(player, 1).open();
            return;
        }
        User user = instance.getUserManager().getUser(player);
        if (user == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found"));
            return;
        }
        Storage storage = user.getStorage();

        String args0 = context.getArgs(0);
        if (args0.equalsIgnoreCase("all")) {
            sellAll(player, storage, context);
            return;
        }

        Optional<Item> optional = storage.getItem(args0);
        if (!optional.isPresent()) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(Utils.getRegex("player"), player.getName()));
            return;
        }

        Item item = optional.get();
        int quantity = item.getQuantity();
        if (quantity < 1) {
            context.sendMessage(Message.getMessage("FAIL.not-enough-item").replaceAll(Utils.getRegex("item"), instance.getSetting().getNameFormatted(args0, true)));
            return;
        }

        if (context.getArgsLength() == 1) {
            instance.getSetting()
                    .getEconomyProvider()
                    .sellItem(player, item.getItem(), quantity, result -> {
                        if (!result.isSuccess()) {
                            context.sendMessage(Message.getMessage("FAIL.cannot-be-sold"));
                            return;
                        }
                        storage.subtract(args0, quantity);
                        context.sendMessage(Message.getMessage("SUCCESS.item-sold")
                                .replaceAll(Utils.getRegex("amount"), Digital.formatThousands(quantity))
                                .replaceAll(Utils.getRegex("item"), instance.getSetting().getNameFormatted(args0, true))
                                .replaceAll(Utils.getRegex("price"), Digital.formatDouble("###,###.##", result.getPrice())));
                    });
            return;
        }

        String args1 = context.getArgs(1);
        if (args1.equalsIgnoreCase("all")) {
            sellMaterialAll(player, storage, context, args0);
            return;
        }

        int amount;
        try {
            amount = Digital.getBetween(1, quantity, Integer.parseInt(args1));
        } catch (NumberFormatException ignored) {
            context.sendMessage(Message.getMessage("FAIL.not-number").replaceAll(VALUE_REGEX, args1));
            return;
        }

        instance.getSetting()
                .getEconomyProvider()
                .sellItem(player, item.getItem(), amount, result -> {
                    if (!result.isSuccess()) {
                        context.sendMessage(Message.getMessage("FAIL.cannot-be-sold"));
                        return;
                    }
                    storage.subtract(args0, amount);
                    context.sendMessage(Message.getMessage("SUCCESS.item-sold")
                            .replaceAll(Utils.getRegex("amount"), Digital.formatThousands(amount))
                            .replaceAll(Utils.getRegex("item"), instance.getSetting().getNameFormatted(args0, true))
                            .replaceAll(Utils.getRegex("price"), Digital.formatDouble("###,###.##", result.getPrice())));
                });
    }

    private void sellAll(Player player, Storage storage, CommandContext context) {
        final double[] totalSoldPrice = {0};

        for (Item item : new java.util.ArrayList<>(storage.getItems().values())) {
            int quantity = item.getQuantity();
            if (quantity < 1) continue;

            instance.getSetting()
                    .getEconomyProvider()
                    .sellItem(player, item.getItem(), quantity, (result) -> {
                        if (result.isSuccess()) {
                            storage.subtract(item.getKey(), quantity);
                            totalSoldPrice[0] += result.getPrice();
                        }
                    });
        }
        context.sendMessage(Message.getMessage("SUCCESS.sell-all-items")
                .replaceAll(Utils.getRegex("price"), Digital.formatDouble("###,###.##", totalSoldPrice[0])));
    }

    private void sellMaterialAll(Player player, Storage storage, CommandContext context, String materialKey) {
        Optional<Item> optional = storage.getItem(materialKey);
        if (!optional.isPresent()) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(Utils.getRegex("player"), player.getName()));
            return;
        }

        Item item = optional.get();
        int quantity = item.getQuantity();
        if (quantity < 1) {
            context.sendMessage(Message.getMessage("FAIL.not-enough-item").replaceAll(Utils.getRegex("item"), instance.getSetting().getNameFormatted(materialKey, true)));
            return;
        }

        instance.getSetting()
                .getEconomyProvider()
                .sellItem(player, item.getItem(), quantity, result -> {
                    if (!result.isSuccess()) {
                        context.sendMessage(Message.getMessage("FAIL.cannot-be-sold"));
                        return;
                    }
                    storage.subtract(materialKey, quantity);
                    context.sendMessage(Message.getMessage("SUCCESS.item-sold")
                            .replaceAll(Utils.getRegex("amount"), Digital.formatThousands(quantity))
                            .replaceAll(Utils.getRegex("item"), instance.getSetting().getNameFormatted(materialKey, true))
                            .replaceAll(Utils.getRegex("price"), Digital.formatDouble("###,###.##", result.getPrice())));
                });
    }

}
