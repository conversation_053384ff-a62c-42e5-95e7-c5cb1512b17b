menu_title: '&bG<PERSON><PERSON>n <PERSON>'
open_command:
- doiblock
size: 54
open_requirement:
  requirements:
    permission:
      type: has permission
      permission: group.hero
      deny_commands:
      - '[close]'
items:
  #====================================================================#
  #                           KHUNG GIAO DIỆN                          #
  #====================================================================#
  'Fill':
    material: GRAY_STAINED_GLASS_PANE
    display_name: '&f'
    slots:
    - 0-53
  'Fill-B':
    material: WHITE_STAINED_GLASS_PANE
    display_name: '&f'
    slots: [9,18,27,36,17,26,35,44,45] 
  'U-Bar':
    material: BLUE_STAINED_GLASS_PANE
    display_name: '&f'
    slots:
    - 0-8
    - 45-53

  'close':
    material: BARRIER
    slot: 53
    display_name: '&cĐóng'
    left_click_commands:
    - '[close]'

  #====================================================================#
  #                        CÁC VẬT PHẨM RÚT                            #
  #====================================================================#

  # 'test':
  #   material: BONE
  #   slot: 20
  #   display_name: '&7[&fConvert&7] &aBone'
  #   lore:
  #   - ''
  #   - '&7Nhấn để chuyển đổi &fXương&7 thành &fBột Xương'
  #   - ''
  #   - '&a▶ Nhấn để chuyển đổi!'
  #   click_requirement:
  #     requirements:
  #       javascript:
  #         type: 'javascript'
  #         expression: '%exstorage_quantity_BONE% != -1 && %exstorage_quantity_BONE_MEAL% != -1 && %exstorage_quantity_BONE% > 0 && %exstorage_quantity_BONE_MEAL% >= 0'
  #         deny_commands:
  #         - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ &8Khối Than&c hoặc &8Kho bạn không để filter Bột Xương &cđể chuyển đổi!'
  #         - '[sound] BLOCK_ANVIL_LAND'
  #   click_commands:
  #   - '[console] esadmin take BONE 1 %player_name%'
  #   - '[console] esadmin add BONE_MEAL 1 %player_name%'
  #   - '[refresh]'

  'chuyendoi_than':
    material: COAL_BLOCK
    slot: 20
    display_name: '&7[&fChuyển Đổi&7] &8Than'
    lore:
    - ''
    - '&7Nhấn để chuyển đổi toàn bộ &8Than'
    - ''
    - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_COAL_BLOCK%'
    - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
    - ''
    - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_COAL_BLOCK% >= 1'
          deny_commands:
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] chuyendoi coal %player_name%'
    - '[refresh]'


  'chuyendoi_luuly':
    material: LAPIS_BLOCK
    slot: 21
    display_name: '&7[&fChuyển Đổi&7] &9Lưu Ly'
    lore:
      - ''
      - '&7Nhấn để chuyển đổi toàn bộ &9Lưu Ly'
      - ''
      - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_LAPIS_BLOCK%'
      - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
      - ''
      - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_LAPIS_BLOCK% >= 1'
          deny_commands:
            - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
      - '[console] chuyendoi lapis %player_name%'
      - '[refresh]'


  'chuyendoi_dado':
    material: REDSTONE_BLOCK
    slot: 22
    display_name: '&7[&fChuyển Đổi&7] &cĐá Đỏ'
    lore:
      - ''
      - '&7Nhấn để chuyển đổi toàn bộ &cĐá Đỏ'
      - ''
      - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_REDSTONE_BLOCK%'
      - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
      - ''
      - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_REDSTONE_BLOCK% >= 1'
          deny_commands:
            - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
      - '[console] chuyendoi redstone %player_name%'
      - '[refresh]'
  

  'chuyendoi_dong':
    material: COPPER_BLOCK
    slot: 23
    display_name: '&7[&fChuyển Đổi&7] &#A59616Đồng'
    lore:
      - ''
      - '&7Nhấn để chuyển đổi toàn bộ &#A59616Đồng'
      - ''
      - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_COPPER_BLOCK%'
      - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
      - ''
      - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_COPPER_BLOCK% >= 1'
          deny_commands:
            - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
      - '[console] chuyendoi copper %player_name%'
      - '[refresh]'
  

  'chuyendoi_sat':
    material: IRON_BLOCK
    slot: 24
    display_name: '&7[&fChuyển Đổi&7] &fSắt'
    lore:
      - ''
      - '&7Nhấn để chuyển đổi toàn bộ &fSắt'
      - ''
      - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_IRON_BLOCK%'
      - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
      - ''
      - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_IRON_BLOCK% >= 1'
          deny_commands:
            - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
      - '[console] chuyendoi iron %player_name%'
      - '[refresh]'


  'chuyendoi_vang':
    material: GOLD_BLOCK
    slot: 29
    display_name: '&7[&fChuyển Đổi&7] &eVàng'
    lore:
      - ''
      - '&7Nhấn để chuyển đổi toàn bộ &eVàng'
      - ''
      - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_GOLD_BLOCK%'
      - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
      - ''
      - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_GOLD_BLOCK% >= 1'
          deny_commands:
            - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
      - '[console] chuyendoi gold %player_name%'
      - '[refresh]'


  'chuyendoi_kimcuong':
    material: DIAMOND_BLOCK
    slot: 30
    display_name: '&7[&fChuyển Đổi&7] &bKim Cương'
    lore:
      - ''
      - '&7Nhấn để chuyển đổi toàn bộ &bKim Cương'
      - ''
      - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_DIAMOND_BLOCK%'
      - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
      - ''
      - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_DIAMOND_BLOCK% >= 1'
          deny_commands:
            - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
      - '[console] chuyendoi diamond %player_name%'
      - '[refresh]'
  

  'chuyendoi_ngoclucbao':
    material: EMERALD_BLOCK
    slot: 31
    display_name: '&7[&fChuyển Đổi&7] &aLục Bảo'
    lore:
      - ''
      - '&7Nhấn để chuyển đổi toàn bộ &aLục Bảo'
      - ''
      - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_EMERALD_BLOCK%'
      - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
      - ''
      - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_EMERALD_BLOCK% >= 1'
          deny_commands:
            - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
      - '[console] chuyendoi emerald %player_name%'
      - '[refresh]'
  

  'chuyendoi_netherite':
    material: NETHERITE_BLOCK
    slot: 32
    display_name: '&7[&fChuyển Đổi&7] &#5B5A4ANetherite'
    lore:
      - ''
      - '&7Nhấn để chuyển đổi toàn bộ &#5B5A4ANetherite'
      - ''
      - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_NETHERITE_BLOCK%'
      - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
      - ''
      - '&a▶ Nhấn để chuyển đổi toàn bộ!'
    click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_NETHERITE_BLOCK% >= 1'
          deny_commands:
            - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
      - '[console] chuyendoi netherite %player_name%'
      - '[refresh]'


  # 'chuyendoi_thachanh':
  #   material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNjVhZGVhODk4NDUyYmZhNzJkOTE4ZGEyMTVlOTVhYWI2MWNiOWVhM2M4ZTI2NDFjZjA3ZjAzMzY1ZDEzN2M4ZSJ9fX0=
  #   slot: 33
  #   display_name: '&7[&fChuyển Đổi&7] &dThạch Anh'
  #   lore:
  #     - ''
  #     - '&7Nhấn để chuyển đổi toàn bộ &dThạch Anh'
  #     - ''
  #     - '&7Số Khối Hiện Có Trong Kho: &a%exstorage_quantity_AMETHYST_BLOCK%'
  #     - '&7Slot Kho trống: &a%exstorage_free_space_formatted%'
  #     - ''
  #     - '&a▶ Nhấn để chuyển đổi toàn bộ!'
  #   click_requirement:
  #     requirements:
  #       javascript:
  #         type: 'javascript'
  #         expression: '%exstorage_quantity_AMETHYST_BLOCK% >= 1'
  #         deny_commands:
  #           - '[sound] BLOCK_ANVIL_LAND'
  #   left_click_commands:
  #     - '[console] chuyendoi amethyst %player_name%'
  #     - '[refresh]'