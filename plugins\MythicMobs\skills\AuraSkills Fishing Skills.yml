MucTiHonSpawn:
  TargetConditions:
  - stringequals{val1="%auraskills_fishing%";val2=>"1"} orElseCast SpawnFail
  Skills:
  - message{m="&bSpawned!"} @trigger
  - healpercent{multiplier=1} @self
NguoiCaSpawn:
  TargetConditions:
  - stringequals{val1="%auraskills_fishing%";val2=>"1"} orElseCast SpawnFail
  Skills:
  - message{m="&bSpawned!"} @trigger
  - healpercent{multiplier=1} @self
PhuThuyBienSpawn:
  TargetConditions:
  - stringequals{val1="%auraskills_fishing%";val2=>"7"} orElseCast SpawnFail
  Skills:
  - message{m="&bSpawned!"} @trigger
  - healpercent{multiplier=1} @self
XaThuBienSpawn:
  TargetConditions:
  - stringequals{val1="%auraskills_fishing%";val2=>"9"} orElseCast SpawnFail
  Skills:
  - message{m="&bSpawned!"} @trigger
  - healpercent{multiplier=1} @self
SpawnFail:
  Skills:
  - message{m="&cDespawned!"} @trigger
  - remove @self
Legendary_Drop_Common:
  Conditions:
  - chance{c=0.000001}
  Skills:
  - message{m="&eVới tỷ lệ không tưởng &d0.000001% &7(1/1M)&e, người chơi &a<trigger.name> &eđã nhận được vật phẩm &7[&9Fishing&7] <#3508D7>B<#3D0AD8>ả<#450BD8>n <#550FD9>N<#5D10DA>h<#6512DB>ạ<#6D13DB>c <#7D17DC>D<#8518DD>u <#951CDE>D<#9C1DDF>ư<#A41FDF>ơ<#AC21E0>n<#B422E1>g <#C426E2>Đ<#CC27E2>ầ<#D429E3>u <#E42CE4>T<#EC2EE5>i<#F42FE5>ê<#FC31E6>n&f. &eGGs!"} @server
  - command{c="mi give DUNGEON_MOBDROPS FISHING_LEGENDARY_DROP <trigger.name> 1";asop=true} @trigger
Legendary_Drop_Uncommon:
  Conditions:
  - chance{c=0.000002}
  Skills:
  - message{m="&eVới tỷ lệ không tưởng &d0.000002% &7(1/500k)&e, người chơi &a<trigger.name> &eđã nhận được vật phẩm &7[&9Fishing&7] <#3508D7>B<#3D0AD8>ả<#450BD8>n <#550FD9>N<#5D10DA>h<#6512DB>ạ<#6D13DB>c <#7D17DC>D<#8518DD>u <#951CDE>D<#9C1DDF>ư<#A41FDF>ơ<#AC21E0>n<#B422E1>g <#C426E2>Đ<#CC27E2>ầ<#D429E3>u <#E42CE4>T<#EC2EE5>i<#F42FE5>ê<#FC31E6>n&f. &eGGs!"} @server
  - command{c="mi give DUNGEON_MOBDROPS FISHING_LEGENDARY_DROP <trigger.name> 1";asop=true} @trigger