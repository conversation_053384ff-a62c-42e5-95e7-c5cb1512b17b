# Build Instructions - FixBlockBreak Plugin

## Yêu cầu hệ thống
- Java 21 hoặc cao hơn
- Maven 3.6+
- Minecraft Server 1.21.4 (Paper/Spigot)

## Cách build plugin

### 1. Build từ source
```bash
mvn clean package
```

### 2. <PERSON><PERSON><PERSON> tra build thành công
- File `.jar` sẽ được tạo trong thư mục `target/`
- Tên file: `fixblockbreak-1.0-SNAPSHOT.jar`

### 3. Cài đặt plugin
1. Copy file `.jar` vào thư mục `plugins/` của server
2. Restart server
3. Plugin sẽ tự động tạo file `config.yml`

## Kiểm tra plugin hoạt động

### 1. Kiểm tra console
Khi server khởi động, bạn sẽ thấy:
```
[INFO] FixBlockBreak plugin đã được kích hoạt!
[INFO] Plugin này sẽ giúp fix vấn đề kẹt block khi đào ore liên tục.
[INFO] Đã phát hiện AdvancedOreGen plugin! (nếu có)
```

### 2. Test commands
```
/fixblockbreak info     # Xem thông tin plugin
/fixblockbreak status   # Kiểm tra trạng thái
/fixblockbreak reload   # Reload config
```

### 3. Test chức năng
1. Đi đến khu vực farm ore (nước + hàng rào)
2. Đào ore liên tục
3. Plugin sẽ tự động xử lý nếu có block kẹt

## Cấu hình plugin

File `config.yml` sẽ được tạo tự động với các giá trị mặc định:

```yaml
general:
  enabled: true
  debug: false
  notify-player: true

block-breaking:
  break-cooldown: 50
  max-consecutive-breaks: 20
  spam-delay: 200
  same-area-radius: 10.0
  stuck-check-delay: 2
```

## Debug mode

Để bật debug mode:
1. Sửa `debug: true` trong config.yml
2. Chạy `/fixblockbreak reload`
3. Xem console để theo dõi hoạt động của plugin

## Troubleshooting

### Plugin không load
- Kiểm tra Java version (cần Java 21+)
- Kiểm tra console có lỗi không
- Đảm bảo file plugin.yml đúng format

### Commands không hoạt động
- Kiểm tra permission `fixblockbreak.admin`
- Đảm bảo bạn là OP hoặc có quyền

### Plugin không fix được block kẹt
- Bật debug mode
- Kiểm tra config có đúng không
- Xem console logs để debug

## Tương thích

✅ **Đã test với:**
- Paper 1.21.4
- AdvancedOreGen plugin
- Java 21

⚠️ **Chưa test với:**
- Spigot/Bukkit (nhưng should work)
- Các plugin ore gen khác
- Java versions khác

## Performance

Plugin được thiết kế để:
- Minimal impact lên server performance
- Chỉ xử lý khi cần thiết
- Tự động cleanup memory
- Không block main thread
