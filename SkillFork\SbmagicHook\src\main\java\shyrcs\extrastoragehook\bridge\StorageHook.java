package shyrcs.extrastoragehook.bridge;

import shyrcs.extrastoragehook.storage.ConnectionStorage;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Storage Hook để quản lý kết nối giữa Discord ID và Minecraft UUID
 * Sử dụng ConnectionStorage để lưu trữ persistent
 */
public class StorageHook {

    private final Map<String, UUID> connections;
    private ConnectionStorage connectionStorage;

    public StorageHook() {
        this.connections = Collections.synchronizedMap(new HashMap<>());
    }

    /**
     * Thiết lập connection storage
     */
    public void setConnectionStorage(ConnectionStorage connectionStorage) {
        this.connectionStorage = connectionStorage;
        // Load existing connections
        if (connectionStorage != null) {
            connections.putAll(connectionStorage.getAllConnections());
        }
    }
    
    /**
     * Kết nối Discord ID với Minecraft UUID
     */
    public synchronized void connect(String discordId, UUID minecraftUuid) {
        this.connections.put(discordId, minecraftUuid);
        if (connectionStorage != null) {
            connectionStorage.saveConnection(discordId, minecraftUuid);
        }
    }
    
    /**
     * Ngắt kết nối Discord ID
     */
    public synchronized void disconnect(String discordId) {
        this.connections.remove(discordId);
        if (connectionStorage != null) {
            connectionStorage.removeConnection(discordId);
        }
    }
    
    /**
     * Lấy Minecraft UUID từ Discord ID
     */
    public synchronized UUID getMinecraftUUID(String discordId) {
        return this.connections.get(discordId);
    }
    
    /**
     * Lấy Discord ID từ Minecraft UUID
     */
    public synchronized String getDiscordID(UUID minecraftUuid) {
        for (Map.Entry<String, UUID> entry : connections.entrySet()) {
            if (entry.getValue().equals(minecraftUuid)) {
                return entry.getKey();
            }
        }
        return null;
    }
    
    /**
     * Kiểm tra Discord user đã kết nối chưa
     */
    public synchronized boolean userConnected(String discordId) {
        return this.connections.containsKey(discordId);
    }
    
    /**
     * Kiểm tra Minecraft player đã kết nối chưa
     */
    public synchronized boolean playerConnected(UUID minecraftUuid) {
        return this.connections.containsValue(minecraftUuid);
    }
    
    /**
     * Lấy số lượng kết nối hiện tại
     */
    public synchronized int getConnectionCount() {
        return this.connections.size();
    }
    
    /**
     * Xóa tất cả kết nối
     */
    public synchronized void clearAll() {
        this.connections.clear();
    }
    
    /**
     * Lấy tất cả kết nối (chỉ để debug)
     */
    public synchronized Map<String, UUID> getAllConnections() {
        return new HashMap<>(this.connections);
    }
}
