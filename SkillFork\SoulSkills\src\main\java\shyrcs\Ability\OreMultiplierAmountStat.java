package shyrcs.Ability;

import net.Indyuce.mmoitems.api.item.build.ItemStackBuilder;
import net.Indyuce.mmoitems.api.item.mmoitem.ReadMMOItem;
import net.Indyuce.mmoitems.gui.edition.EditionInventory;
import net.Indyuce.mmoitems.stat.data.DoubleData;
import net.Indyuce.mmoitems.stat.type.DoubleStat;
import io.lumine.mythic.lib.api.item.ItemTag;
import io.lumine.mythic.lib.api.item.SupportedNBTTagValues;
import net.Indyuce.mmoitems.api.util.NumericStatFormula;
import org.bukkit.Material;
import org.bukkit.event.inventory.InventoryClickEvent;
import java.util.*;

public class OreMultiplierAmountStat extends DoubleStat {
    
    public OreMultiplierAmountStat() {
        super("ORE_MULTIPLIER_AMOUNT", 
              Material.GOLD_INGOT, 
              "Ore Multiplier Amount", 
              new String[]{"Multiplier Amount cho Ore Multiplier Rate."},
              new String[]{"tool", "all"}
        );
    }
    
    @Override
    public void whenApplied(ItemStackBuilder item, DoubleData data) {
        // Lưu giá trị vào NBT
        item.addItemTag(new ItemTag("MMOITEMS_ORE_MULTIPLIER_AMOUNT", data.getValue()));

        // Thêm lore với giá trị cố định
        item.getLore().insert("ore-multiplier-amount", "§7Ore Multiplier Amount: §a" + String.format("%.1f", data.getValue()) + "x");
    }
    
    @Override
    public ArrayList<ItemTag> getAppliedNBT(DoubleData data) {
        ArrayList<ItemTag> tags = new ArrayList<>();
        tags.add(new ItemTag("MMOITEMS_ORE_MULTIPLIER_AMOUNT", data.getValue()));
        return tags;
    }
    
    @Override
    public void whenDisplayed(List<String> lore, Optional<NumericStatFormula> statData) {
        if (statData.isPresent()) {
            lore.add("§7Ore Multiplier Amount: §a" + statData.get().toString() + "x");
        } else {
            lore.add("§7Ore Multiplier Amount: §c2x");
        }
    }
    
    @Override
    public DoubleData getLoadedNBT(ArrayList<ItemTag> tags) {
        ItemTag tag = ItemTag.getTagAtPath("MMOITEMS_ORE_MULTIPLIER_AMOUNT", tags);
        return tag != null ? new DoubleData((Double) tag.getValue()) : null;
    }
    
    @Override
    public void whenLoaded(ReadMMOItem mmoItem) {
        // Get tags - Fixed approach following the Abilities class pattern
        ArrayList<ItemTag> relevantTags = new ArrayList<>();

        if (mmoItem.getNBT().hasTag("MMOITEMS_ORE_MULTIPLIER_AMOUNT"))
            relevantTags.add(ItemTag.getTagAtPath("MMOITEMS_ORE_MULTIPLIER_AMOUNT", mmoItem.getNBT(), SupportedNBTTagValues.DOUBLE));

        DoubleData data = getLoadedNBT(relevantTags);

        // Valid?
        if (data != null) {
            // Set
            mmoItem.setData(this, data);
        }
    }
    
    @Override
    public void whenInput(EditionInventory inv, String message, Object... info) {
        try {
            double value = Double.parseDouble(message);
            if (value < 1) {
                inv.getPlayer().sendMessage("§cGiá trị phải lớn hơn hoặc bằng 1!");
                return;
            }
            if (value > 10) {
                inv.getPlayer().sendMessage("§cGiá trị không được vượt quá 10x!");
                return;
            }
            
            inv.getEditedSection().set(getPath(), value);
            inv.registerTemplateEdition();
            inv.getPlayer().sendMessage("§aOre Multiplier Amount đã được đặt thành " + value + "x");
        } catch (NumberFormatException e) {
            inv.getPlayer().sendMessage("§cVui lòng nhập một số hợp lệ!");
        }
    }
    
    @Override
    public void whenClicked(EditionInventory inv, InventoryClickEvent event) {
        // Sử dụng hệ thống StatEdition của MMOItems
        new net.Indyuce.mmoitems.api.edition.StatEdition(inv, this).enable("&eNhập số lần nhân (1-10x):");
    }
    
    @Override
    public DoubleData getClearStatData() {
        return new DoubleData(0); // Default là nhân đôi
    }
}
