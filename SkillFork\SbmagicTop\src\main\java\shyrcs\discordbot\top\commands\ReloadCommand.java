package shyrcs.discordbot.top.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.discordbot.top.SbmagicTopPlugin;

public class ReloadCommand implements CommandExecutor {
    
    private final SbmagicTopPlugin plugin;
    
    public ReloadCommand(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("sbmagictop.reload")) {
            sender.sendMessage("§cBạn không có quyền sử dụng lệnh này!");
            return true;
        }
        
        try {
            plugin.getConfigManager().reloadConfigs();
            plugin.getPlaceholderManager().clearCache();
            
            sender.sendMessage("§a[SbmagicTop] Đã reload config thành công!");
            plugin.getLogger().info("Config đã được reload bởi " + sender.getName());
            
        } catch (Exception e) {
            sender.sendMessage("§c[SbmagicTop] Có lỗi khi reload config: " + e.getMessage());
            plugin.getLogger().severe("Lỗi khi reload config: " + e.getMessage());
            e.printStackTrace();
        }
        
        return true;
    }
}
