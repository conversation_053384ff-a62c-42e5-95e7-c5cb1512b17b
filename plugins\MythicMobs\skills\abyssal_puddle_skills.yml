CancelEvent:
  Conditions:
  - incombat true
  Skills:
  - CancelEvent @self
  
puddle_summoned:
  Skills:
  - CancelEvent @self
  - state{model=abyssal_puddle;state=spawn} @self
  - delay 4
  - lockmodel @self
  - aura{auraName=slowing;oT=slow_target;duration=176;interval=5} @self
  - delay 206
  - remove @self
  
slow_target:
  Skills:
  - potion{type=SLOW;duration=20;l=2} @LivingInRadius{r=2;ignore=samefaction}
  - potion{type=WITHER;duration=20;l=2} @LivingInRadius{r=2;ignore=samefaction}