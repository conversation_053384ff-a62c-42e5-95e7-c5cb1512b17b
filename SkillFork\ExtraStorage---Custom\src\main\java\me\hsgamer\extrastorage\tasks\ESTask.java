package me.hsgamer.extrastorage.tasks;

import me.hsgamer.extrastorage.ExtraStorage;
import org.bukkit.scheduler.BukkitRunnable;

public abstract class ESTask
        extends BukkitRunnable {

    protected final ExtraStorage instance;
    private final boolean loop, async;

    protected int time;
    private int countdown;

    ESTask(ExtraStorage instance, int time, boolean loop, boolean async) {
        this.instance = instance;
        this.loop = loop;
        this.async = async;

        this.time = time;
        this.countdown = time;

        if (this.async) this.runTaskTimerAsynchronously(instance, 0L, 20L);
        else this.runTaskTimer(instance, 0L, 20L);
    }

    public void onRun() {
        // Do stuff...
    }

    public abstract void onFinish();

    @Override
    public void run() {
        this.onRun();

        if (--countdown > 0) return;
        this.countdown = time;

        this.onFinish();

        if (!loop) this.cancel();
    }

    public synchronized final void resetTime() {
        this.countdown = time;
    }

    // Manual getter and setter methods
    public boolean isLoop() {
        return loop;
    }

    public boolean isAsync() {
        return async;
    }

    public void setTime(int time) {
        this.time = time;
    }

}
