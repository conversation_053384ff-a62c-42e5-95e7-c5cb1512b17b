package shyrcs.Ability;

import net.Indyuce.mmoitems.api.item.build.ItemStackBuilder;
import net.Indyuce.mmoitems.api.item.mmoitem.ReadMMOItem;
import net.Indyuce.mmoitems.stat.data.StringData;
import net.Indyuce.mmoitems.stat.type.StringStat;
import io.lumine.mythic.lib.api.item.ItemTag;
import io.lumine.mythic.lib.api.item.SupportedNBTTagValues;
import org.bukkit.Material;

import java.util.ArrayList;

/**
 * Custom MMOItems stat cho việc cấu hình buff
 * Cho phép player nhập format buff qua GUI MMOItems
 */
public class BuffConfigStat extends StringStat {
    
    public BuffConfigStat() {
        super("BUFF_CONFIG", 
              Material.DRAGON_BREATH, 
              "Buff Config", 
              new String[]{
                  "Cấu hình buff cho item khi sử dụng.",
                  "Format: <buff_id> <level> <duration> <click_type> <cooldown>",
                  "Ví dụ: absorption 2 10 left_click 5",
                  "§7Click types: left_click, right_click, shift_left_click, shift_right_click"
              }, 
              new String[]{"tool", "weapon", "armor", "accessory", "consumable", "all"}
        );
    }

    @Override
    public void whenApplied(ItemStackBuilder item, StringData data) {
        // Lưu giá trị vào NBT
        item.addItemTag(new ItemTag("MMOITEMS_BUFF_CONFIG", data.toString()));

        // Parse config để hiển thị lore
        BuffConfig config = parseBuffConfig(data.toString());
        if (config != null) {
            String buffNameVi = BuffPlaceholderUtil.getBuffNameVi(config.getPotionEffectType());
            String levelRoman = BuffUtils.toRoman(config.getLevel());
            item.getLore().insert("buff-config",
                "§7Buff: §e" + buffNameVi + " " + levelRoman,
                "§7Duration: §a" + config.getDuration() + "s",
                "§7Click: §b" + config.getClickType(),
                "§7Cooldown: §c" + config.getCooldown() + "s"
            );
        }
    }

    @Override
    public ArrayList<ItemTag> getAppliedNBT(StringData data) {
        ArrayList<ItemTag> tags = new ArrayList<>();
        tags.add(new ItemTag("MMOITEMS_BUFF_CONFIG", data.toString()));
        return tags;
    }

    @Override
    public void whenLoaded(ReadMMOItem mmoItem) {
        ArrayList<ItemTag> relevantTags = new ArrayList<>();

        if (mmoItem.getNBT().hasTag("MMOITEMS_BUFF_CONFIG"))
            relevantTags.add(ItemTag.getTagAtPath("MMOITEMS_BUFF_CONFIG", mmoItem.getNBT(), SupportedNBTTagValues.STRING));

        StringData data = getLoadedNBT(relevantTags);

        if (data != null) {
            mmoItem.setData(this, data);
        }
    }

    @Override
    public StringData getLoadedNBT(ArrayList<ItemTag> storedTags) {
        ItemTag tag = ItemTag.getTagAtPath("MMOITEMS_BUFF_CONFIG", storedTags);
        return tag != null ? new StringData(tag.getValue().toString()) : null;
    }

    /**
     * Validate format buff input
     */
    public boolean isValidInput(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }
        
        String[] parts = input.trim().split(" ");
        if (parts.length != 5) {
            return false;
        }
        
        try {
            // Validate buff_id
            String buffId = parts[0].toUpperCase();
            if (BuffUtils.getEffectTypeByName(buffId) == null) {
                return false;
            }
            
            // Validate level (1-256)
            int level = Integer.parseInt(parts[1]);
            if (!BuffUtils.isValidLevel(level)) {
                return false;
            }
            
            // Validate duration (positive number)
            int duration = Integer.parseInt(parts[2]);
            if (duration <= 0) {
                return false;
            }
            
            // Validate click type
            String clickType = parts[3].toLowerCase();
            if (!BuffUtils.isValidClickType(clickType)) {
                return false;
            }
            
            // Validate cooldown (non-negative)
            int cooldown = Integer.parseInt(parts[4]);
            if (cooldown < 0) {
                return false;
            }
            
            return true;
            
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * Lấy error message khi format không hợp lệ
     */
    public String getInvalidMessage(String input) {
        if (input == null || input.trim().isEmpty()) {
            return "§cFormat không được để trống!";
        }
        
        String[] parts = input.trim().split(" ");
        if (parts.length != 5) {
            return "§cFormat phải có đúng 5 tham số: <buff_id> <level> <duration> <click_type> <cooldown>";
        }
        
        try {
            // Check buff_id
            String buffId = parts[0].toUpperCase();
            if (BuffUtils.getEffectTypeByName(buffId) == null) {
                return "§cBuff ID không hợp lệ: " + buffId + ". Gõ /buffs để xem danh sách!";
            }
            
            // Check level
            int level = Integer.parseInt(parts[1]);
            if (!BuffUtils.isValidLevel(level)) {
                return "§cLevel phải từ 1 đến 256!";
            }
            
            // Check duration
            int duration = Integer.parseInt(parts[2]);
            if (duration <= 0) {
                return "§cThời gian phải là số dương!";
            }
            
            // Check click type
            String clickType = parts[3].toLowerCase();
            if (!BuffUtils.isValidClickType(clickType)) {
                return "§cClick type không hợp lệ! Sử dụng: left_click, right_click, shift_left_click, shift_right_click";
            }
            
            // Check cooldown
            int cooldown = Integer.parseInt(parts[4]);
            if (cooldown < 0) {
                return "§cCooldown không được âm!";
            }
            
        } catch (NumberFormatException e) {
            return "§cLevel, duration và cooldown phải là số nguyên!";
        }
        
        return "§cFormat không hợp lệ!";
    }
    
    /**
     * Parse buff config từ string
     */
    public static BuffConfig parseBuffConfig(String input) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }
        
        String[] parts = input.trim().split(" ");
        if (parts.length != 5) {
            return null;
        }
        
        try {
            String buffId = parts[0].toUpperCase();
            int level = Integer.parseInt(parts[1]);
            int duration = Integer.parseInt(parts[2]);
            String clickType = parts[3].toLowerCase();
            int cooldown = Integer.parseInt(parts[4]);
            
            return new BuffConfig(buffId, level, duration, clickType, cooldown);
            
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * Class để lưu trữ thông tin buff config
     */
    public static class BuffConfig {
        private final String buffId;
        private final int level;
        private final int duration;
        private final String clickType;
        private final int cooldown;
        
        public BuffConfig(String buffId, int level, int duration, String clickType, int cooldown) {
            this.buffId = buffId;
            this.level = level;
            this.duration = duration;
            this.clickType = clickType;
            this.cooldown = cooldown;
        }
        
        public String getBuffId() { return buffId; }
        public int getLevel() { return level; }
        public int getDuration() { return duration; }
        public String getClickType() { return clickType; }
        public int getCooldown() { return cooldown; }
        
        @Override
        public String toString() {
            return buffId + " " + level + " " + duration + " " + clickType + " " + cooldown;
        }
        
        /**
         * Lấy PotionEffectType từ buffId
         */
        public org.bukkit.potion.PotionEffectType getPotionEffectType() {
            return BuffUtils.getEffectTypeByName(buffId);
        }
        
        /**
         * Lấy amplifier (level - 1)
         */
        public int getAmplifier() {
            return BuffUtils.levelToAmplifier(level);
        }
        
        /**
         * Lấy duration trong ticks
         */
        public int getDurationTicks() {
            return BuffUtils.secondsToTicks(duration);
        }
    }
}
