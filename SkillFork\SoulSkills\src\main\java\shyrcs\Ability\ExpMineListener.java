package shyrcs.Ability;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;

import java.util.Arrays;
import java.util.List;

public class ExpMineListener implements Listener {
    
    // Danh sách các ore blocks
    private static final List<Material> ORE_BLOCKS = Arrays.asList(
        Material.COAL_ORE,
        Material.DEEPSLATE_COAL_ORE,
        Material.IRON_ORE,
        Material.DEEPSLATE_IRON_ORE,
        Material.COPPER_ORE,
        Material.DEEPSLATE_COPPER_ORE,
        Material.GOLD_ORE,
        Material.DEEPSLATE_GOLD_ORE,
        Material.REDSTONE_ORE,
        Material.DEEPSLATE_REDSTONE_ORE,
        Material.LAPIS_ORE,
        Material.DEEPSLATE_LAPIS_ORE,
        Material.DIAMOND_ORE,
        Material.DEEPSLATE_DIAMOND_ORE,
        Material.EMERALD_ORE,
        Material.DEEPSLATE_EMERALD_ORE,
        Material.NETHER_GOLD_ORE,
        Material.NETHER_QUARTZ_ORE,
        Material.ANCIENT_DEBRIS
    );
    
    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onBlockBreak(BlockBreakEvent event) {
        // ignoreCancelled = true handles cancelled events automatically
        
        Player player = event.getPlayer();
        Material blockType = event.getBlock().getType();
        
        // Kiểm tra xem block có phải là ore không
        if (!ORE_BLOCKS.contains(blockType)) return;
        
        // Lấy tool trong tay player
        ItemStack tool = player.getInventory().getItemInMainHand();
        if (tool == null || tool.getType() == Material.AIR) return;
        
        // Kiểm tra xem tool có phải là MMOItem không và có ExpMine stat không
        NBTItem nbtItem = NBTItem.get(tool);
        if (!nbtItem.hasTag("MMOITEMS_EXP_MINE")) return;

        double expMineValue = nbtItem.getDouble("MMOITEMS_EXP_MINE");
        if (expMineValue <= 0) return;
        
        // Tính toán bonus exp
        int baseExp = getBaseExpForOre(blockType);
        int bonusExp = (int) Math.round(baseExp * (expMineValue / 100.0));

        if (bonusExp > 0) {
            // Gọi custom event
            ExpMineEvent expMineEvent = new ExpMineEvent(player, event.getBlock(), expMineValue, bonusExp);
            Bukkit.getPluginManager().callEvent(expMineEvent);

            // Thêm exp cho player
            player.giveExp(bonusExp);
        }
    }
    
    /**
     * Lấy base exp cho từng loại ore
     */
    private int getBaseExpForOre(Material ore) {
        switch (ore) {
            case COAL_ORE:
            case DEEPSLATE_COAL_ORE:
                return 1;
            case IRON_ORE:
            case DEEPSLATE_IRON_ORE:
            case COPPER_ORE:
            case DEEPSLATE_COPPER_ORE:
                return 2;
            case GOLD_ORE:
            case DEEPSLATE_GOLD_ORE:
            case REDSTONE_ORE:
            case DEEPSLATE_REDSTONE_ORE:
            case LAPIS_ORE:
            case DEEPSLATE_LAPIS_ORE:
                return 3;
            case DIAMOND_ORE:
            case DEEPSLATE_DIAMOND_ORE:
            case EMERALD_ORE:
            case DEEPSLATE_EMERALD_ORE:
                return 5;
            case NETHER_GOLD_ORE:
            case NETHER_QUARTZ_ORE:
                return 3;
            case ANCIENT_DEBRIS:
                return 10;
            default:
                return 1;
        }
    }
}
