package shyrcs.extrastoragehook.economy;

import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;

/**
 * Manager cho Economy Providers
 * Tự động chọn provider phù hợp dựa trên config và plugins có sẵn
 */
public class EconomyManager {
    
    private EconomyProvider currentProvider;
    
    public EconomyManager() {
        initializeProvider();
    }
    
    /**
     * Khởi tạo economy provider dựa trên config
     */
    private void initializeProvider() {
        String providerName = Library.config.getEconomyProvider();
        
        switch (providerName.toLowerCase()) {
            case "shopguiplus":
            case "shopgui+":
                currentProvider = new ShopGuiPlusHook();
                break;
                
            case "vault":
            default:
                currentProvider = new VaultEconomyHook();
                break;
        }
        
        if (currentProvider == null || !currentProvider.isHooked()) {
            SbMagicHook.warn("Economy provider " + providerName + " không khả dụng, chuy<PERSON>n sang Vault");
            currentProvider = new VaultEconomyHook();
            
            if (!currentProvider.isHooked()) {
                SbMagicHook.error("Không có economy provider nào khả dụng!");
                currentProvider = null;
            }
        }
    }
    
    /**
     * Lấy economy provider hiện tại
     */
    public EconomyProvider getCurrentProvider() {
        return currentProvider;
    }
    
    /**
     * Kiểm tra có economy provider không
     */
    public boolean hasProvider() {
        return currentProvider != null && currentProvider.isHooked();
    }
    
    /**
     * Reload economy provider
     */
    public void reload() {
        SbMagicHook.info("Đang reload economy provider...");
        initializeProvider();
    }
    
    /**
     * Lấy tên provider hiện tại
     */
    public String getCurrentProviderName() {
        if (currentProvider instanceof ShopGuiPlusHook) {
            return "ShopGUI+";
        } else if (currentProvider instanceof VaultEconomyHook) {
            return "Vault";
        } else {
            return "Unknown";
        }
    }
}
