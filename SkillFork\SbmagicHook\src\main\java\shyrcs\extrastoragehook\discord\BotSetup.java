package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.interactions.commands.build.Commands;
import net.dv8tion.jda.api.interactions.commands.OptionType;
import net.dv8tion.jda.api.interactions.commands.build.SlashCommandData;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.discord.commands.CommandConnect;
import shyrcs.extrastoragehook.discord.commands.CommandSell;
import shyrcs.extrastoragehook.discord.commands.CommandStorage;
import shyrcs.extrastoragehook.discord.commands.CommandHelp;
import shyrcs.extrastoragehook.SbMagicHook;

import java.util.ArrayList;
import java.util.List;

/**
 * Setup class cho Discord bot
 */
public class BotSetup {
    
    private static boolean limited = false;
    private static List<String> whitelistedChannels = new ArrayList<>();
    
    /**
     * <PERSON><PERSON>ng ký internal commands
     */
    public static void buildInternalCommands() {
        // Đ<PERSON>ng ký các commands
        Library.manager.registerCommand("connect", new CommandConnect());
        Library.manager.registerCommand("sell", new CommandSell());
        Library.manager.registerCommand("storage", new CommandStorage());
        Library.manager.registerCommand("help", new CommandHelp());
        
        // Thiết lập channel restrictions
        whitelistedChannels = Library.config.getWhitelistedChannels();
        limited = !whitelistedChannels.isEmpty();
        
        SbMagicHook.info("Đã đăng ký " + Library.manager.getCommandCount() + " Discord commands");
    }
    
    /**
     * Đăng ký slash commands với Discord theo JDA v5 best practices
     */
    public static void registerSlashCommands(JDA jda) {
        if (!Library.config.useSlashCommands()) {
            SbMagicHook.info("Slash commands đã bị tắt trong config");
            return;
        }

        try {
            SbMagicHook.info("🔧 Đang xóa và đăng ký lại slash commands...");

            // Tạo commands với type-safe approach
            SlashCommandData connectCmd = Commands.slash("connect", "Tạo mã kết nối để liên kết tài khoản Discord với Minecraft");
            SlashCommandData sellCmd = Commands.slash("sell", "Bán items từ kho ExtraStorage")
                .addOption(net.dv8tion.jda.api.interactions.commands.OptionType.STRING, "item", "Tên item cần bán", true)
                .addOption(net.dv8tion.jda.api.interactions.commands.OptionType.INTEGER, "amount", "Số lượng cần bán", false);
            SlashCommandData storageCmd = Commands.slash("storage", "Xem thông tin kho ExtraStorage");
            SlashCommandData helpCmd = Commands.slash("help", "Hiển thị danh sách lệnh");

            // Lấy guild ID từ config
            String guildId = Library.config.getGuildID();
            if (guildId != null && !guildId.isEmpty()) {
                SbMagicHook.info("📍 Đăng ký GUILD commands cho guild: " + guildId);

                // Đăng ký guild commands (hiển thị ngay lập tức)
                jda.getGuildById(guildId).updateCommands()
                    .addCommands(connectCmd, sellCmd, storageCmd, helpCmd)
                    .queue(
                        success -> {
                            SbMagicHook.info("✅ Đã đăng ký " + success.size() + " GUILD slash commands!");
                            for (var cmd : success) {
                                SbMagicHook.info("  - /" + cmd.getName() + ": " + cmd.getDescription());
                            }
                        },
                        error -> {
                            SbMagicHook.error("❌ Lỗi khi đăng ký GUILD commands: " + error.getMessage());
                            error.printStackTrace();

                            // Fallback to global commands
                            SbMagicHook.info("🔄 Fallback: Đăng ký GLOBAL commands...");
                            registerGlobalCommands(jda, connectCmd, sellCmd, storageCmd, helpCmd);
                        }
                    );
            } else {
                SbMagicHook.info("🌍 Guild ID không có, đăng ký GLOBAL commands...");
                registerGlobalCommands(jda, connectCmd, sellCmd, storageCmd, helpCmd);
            }
        } catch (Exception e) {
            SbMagicHook.error("❌ Lỗi khi đăng ký slash commands: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Đăng ký global commands (fallback)
     */
    private static void registerGlobalCommands(JDA jda, SlashCommandData... commands) {
        jda.updateCommands()
            .addCommands(commands)
            .queue(
                success -> {
                    SbMagicHook.info("✅ Đã đăng ký " + success.size() + " GLOBAL slash commands!");
                    for (var cmd : success) {
                        SbMagicHook.info("  - /" + cmd.getName() + ": " + cmd.getDescription());
                    }
                    SbMagicHook.warn("⚠️ Global commands có thể mất 1 giờ để hiển thị!");
                },
                error -> {
                    SbMagicHook.error("❌ Lỗi khi đăng ký GLOBAL commands: " + error.getMessage());
                    error.printStackTrace();
                }
            );
    }

    /**
     * Đăng ký tất cả slash commands
     */
    public static void registerSlashCommandsSimple(JDA jda) {
        if (!Library.config.useSlashCommands()) {
            SbMagicHook.info("Slash commands đã bị tắt trong config");
            return;
        }

        try {
            // Tạo tất cả commands
            SlashCommandData connectCmd = Commands.slash("connect", "Tạo mã kết nối để liên kết tài khoản Discord với Minecraft");
            SlashCommandData storageCmd = Commands.slash("storage", "Xem thông tin kho ExtraStorage");
            SlashCommandData sellCmd = Commands.slash("sell", "Bán items từ kho ExtraStorage")
                .addOption(OptionType.STRING, "item", "Tên item cần bán", true)
                .addOption(OptionType.INTEGER, "amount", "Số lượng cần bán (để trống = bán tất cả)", false);
            SlashCommandData helpCmd = Commands.slash("help", "Danh sách lệnh SbMagicHook");

            // Lấy guild ID từ config
            String guildId = Library.config.getGuildID();
            if (guildId != null && !guildId.isEmpty()) {
                var guild = jda.getGuildById(guildId);
                if (guild != null) {
                    guild.updateCommands()
                        .addCommands(connectCmd, storageCmd, sellCmd, helpCmd)
                        .queue(
                            success -> {
                                // Commands registered successfully
                            },
                            error -> {
                                SbMagicHook.error("Lỗi khi đăng ký GUILD commands: " + error.getMessage());
                                registerGlobalCommandFallback(jda, connectCmd, storageCmd, sellCmd, helpCmd);
                            }
                        );
                } else {
                    registerGlobalCommandFallback(jda, connectCmd, storageCmd, sellCmd, helpCmd);
                }
            } else {
                registerGlobalCommandFallback(jda, connectCmd, storageCmd, sellCmd, helpCmd);
            }

        } catch (Exception e) {
            SbMagicHook.error("❌ Lỗi khi đăng ký slash commands: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Fallback để đăng ký global commands
     */
    private static void registerGlobalCommandFallback(JDA jda, SlashCommandData... commands) {
        jda.updateCommands()
            .addCommands(commands)
            .queue(
                success -> {
                    // Global commands registered (may take 1 hour to appear)
                },
                error -> {
                    SbMagicHook.error("Lỗi khi đăng ký GLOBAL commands: " + error.getMessage());
                }
            );
    }
    
    /**
     * Kiểm tra có giới hạn channel không
     */
    public static boolean isLimited() {
        return limited;
    }
    
    /**
     * Lấy danh sách whitelisted channels
     */
    public static List<String> getWhitelistedChannels() {
        return whitelistedChannels;
    }
    
    /**
     * Kiểm tra channel có được phép không
     */
    public static boolean isChannelAllowed(String channelId) {
        return !limited || whitelistedChannels.contains(channelId);
    }
}
