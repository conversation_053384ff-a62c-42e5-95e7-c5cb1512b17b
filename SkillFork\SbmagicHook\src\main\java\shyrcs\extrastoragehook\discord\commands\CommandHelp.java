package shyrcs.extrastoragehook.discord.commands;

import net.dv8tion.jda.api.EmbedBuilder;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.MessageEmbed;
import net.dv8tion.jda.api.entities.channel.middleman.MessageChannel;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.executor.DiscordExecutor;

import java.awt.*;

/**
 * Discord command để hiển thị danh sách lệnh
 */
public class CommandHelp extends DiscordExecutor {
    
    public CommandHelp() {
        super("help", Library.config.getCommand("help"));
    }
    
    @Override
    public void onSlashCommand(SlashCommandInteractionEvent event) {
        MessageEmbed embed = buildHelpEmbed();
        // <PERSON><PERSON> dụng editOriginalEmbeds vì đã defer trong SlashListener
        event.getHook().editOriginalEmbeds(embed).queue();
    }
    
    @Override
    public void onChatCommand(MessageReceivedEvent event) {
        final MessageChannel channel = event.getChannel();
        final Message message = event.getMessage();
        
        MessageEmbed embed = buildHelpEmbed();
        channel.sendMessageEmbeds(embed).setMessageReference(message).queue();
    }
    
    /**
     * Tạo embed hiển thị danh sách lệnh
     */
    private MessageEmbed buildHelpEmbed() {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("📚 Danh sách lệnh SbMagicHook");
        embed.setColor(Color.GREEN);
        embed.setDescription("Bot kết nối ExtraStorage với Discord");
        
        // Lệnh kết nối
        embed.addField("🔗 `/connect` hoặc `!connect`", 
            Library.config.getCommand("connect"), false);
        
        // Lệnh bán
        embed.addField("💰 `/sell <item> [amount]` hoặc `!sell <item> [amount]`", 
            Library.config.getCommand("sell") + "\n" +
            "**Ví dụ:** `/sell diamond 64` hoặc `/sell iron_ingot all`", false);
        
        // Lệnh xem kho
        embed.addField("📦 `/storage` hoặc `!storage`", 
            Library.config.getCommand("storage"), false);
        
        // Lệnh help
        embed.addField("❓ `/help` hoặc `!help`", 
            Library.config.getCommand("help"), false);
        
        // Hướng dẫn sử dụng
        embed.addField("📝 Hướng dẫn", 
            "1. Sử dụng `/connect` để tạo mã kết nối\n" +
            "2. Trong Minecraft, dùng `/sbmagichook hook <mã>` để kết nối\n" +
            "3. Sau khi kết nối, bạn có thể sử dụng các lệnh khác", false);
        
        // Lưu ý
        embed.addField("⚠️ Lưu ý", 
            "• Bạn cần kết nối tài khoản trước khi sử dụng các lệnh khác\n" +
            "• Mã kết nối sẽ hết hạn sau " + Library.config.getCodeTimeout() + " giây\n" +
            "• Chỉ có thể bán items có trong kho ExtraStorage", false);
        
        embed.setFooter("SbMagicHook v1.0.0-BETA • by Shyrcs");
        embed.setTimestamp(java.time.Instant.now());
        
        return embed.build();
    }
}
