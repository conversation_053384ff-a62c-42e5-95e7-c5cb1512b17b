package shyrcs.extrastoragehook.extrastorage;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.PluginDescriptionFile;
import shyrcs.extrastoragehook.SbMagicHook;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Hook class để tương tác với ExtraStorage APIs thông qua reflection
 * Thay thế cho PreventHopper trong HyperHook
 */
public class ExtraStorageHook {

    private final Plugin plugin;
    private Object storageAPI;
    private Class<?> storageAPIClass;
    private Class<?> userClass;
    private Class<?> storageClass;
    private Class<?> itemClass;

    public ExtraStorageHook(Plugin plugin) throws Exception {
        PluginDescriptionFile file = plugin.getDescription();
        if (!assertMainClass(file) || !assertName(file)) {
            throw new IllegalStateException("Invalid version of the plugin 'ExtraStorage'!");
        }

        this.plugin = plugin;
        initializeReflection();

        SbMagicHook.info("Đã kết nối thành công với ExtraStorage v" + plugin.getDescription().getVersion());
    }

    /**
     * Khởi tạo reflection classes và API instance
     */
    private void initializeReflection() throws Exception {
        try {
            // Load các class cần thiết
            this.storageAPIClass = Class.forName("me.hsgamer.extrastorage.api.StorageAPI");
            this.userClass = Class.forName("me.hsgamer.extrastorage.api.user.User");
            this.storageClass = Class.forName("me.hsgamer.extrastorage.api.storage.Storage");
            this.itemClass = Class.forName("me.hsgamer.extrastorage.api.item.Item");

            // Lấy StorageAPI instance
            Method getInstanceMethod = storageAPIClass.getMethod("getInstance");
            this.storageAPI = getInstanceMethod.invoke(null);

            SbMagicHook.info("Đã khởi tạo ExtraStorage reflection API thành công!");
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi khởi tạo ExtraStorage reflection: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Lấy plugin ExtraStorage
     */
    public Plugin getPlugin() {
        return this.plugin;
    }
    
    /**
     * Lấy ExtraStorage plugin instance
     */
    public Plugin getExtraStoragePlugin() {
        return this.plugin;
    }
    
    /**
     * Lấy StorageAPI instance
     */
    public Object getStorageAPI() {
        return this.storageAPI;
    }
    
    /**
     * Lấy User từ UUID
     */
    public Object getUser(UUID uuid) {
        try {
            Method getUserMethod = storageAPIClass.getMethod("getUser", OfflinePlayer.class);
            return getUserMethod.invoke(storageAPI, Bukkit.getOfflinePlayer(uuid));
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy User: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy User từ OfflinePlayer
     */
    public Object getUser(OfflinePlayer player) {
        try {
            Method getUserMethod = storageAPIClass.getMethod("getUser", OfflinePlayer.class);
            return getUserMethod.invoke(storageAPI, player);
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy User: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy Storage của người chơi
     */
    public Object getStorage(UUID uuid) {
        try {
            Object user = getUser(uuid);
            if (user == null) return null;

            Method getStorageMethod = userClass.getMethod("getStorage");
            return getStorageMethod.invoke(user);
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy Storage: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy Storage của người chơi
     */
    public Object getStorage(OfflinePlayer player) {
        return getStorage(player.getUniqueId());
    }
    
    /**
     * Kiểm tra xem người chơi có items trong kho không
     */
    public boolean hasItems(UUID uuid) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return false;

            Method getItemsMethod = storageClass.getMethod("getItems");
            @SuppressWarnings("unchecked")
            Map<String, Object> items = (Map<String, Object>) getItemsMethod.invoke(storage);

            if (items == null || items.isEmpty()) return false;

            // Kiểm tra xem có item nào có quantity > 0 không
            for (Object item : items.values()) {
                Method getQuantityMethod = itemClass.getMethod("getQuantity");
                int quantity = (int) getQuantityMethod.invoke(item);
                if (quantity > 0) return true;
            }
            return false;
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi kiểm tra items: " + e.getMessage());
            return false;
        }
    }

    /**
     * Lấy số lượng của một item trong kho
     */
    public long getItemAmount(UUID uuid, String materialKey) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return 0;

            Method getItemMethod = storageClass.getMethod("getItem", Object.class);
            Optional<?> itemOptional = (Optional<?>) getItemMethod.invoke(storage, materialKey);

            if (itemOptional.isPresent()) {
                Object item = itemOptional.get();
                Method getQuantityMethod = itemClass.getMethod("getQuantity");
                return (int) getQuantityMethod.invoke(item);
            }
            return 0;
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy số lượng item: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * Lấy số lượng của một item trong kho
     */
    public long getItemAmount(UUID uuid, Material material) {
        return getItemAmount(uuid, material.name());
    }
    
    /**
     * Lấy số lượng của một item trong kho
     */
    public long getItemAmount(UUID uuid, ItemStack item) {
        String materialKey = item.getType().name();
        return getItemAmount(uuid, materialKey);
    }
    
    /**
     * Thêm item vào kho
     */
    public boolean addItem(UUID uuid, String materialKey, int amount) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return false;

            Method canStoreMethod = storageClass.getMethod("canStore", Object.class);
            boolean canStore = (boolean) canStoreMethod.invoke(storage, materialKey);

            if (canStore) {
                Method addMethod = storageClass.getMethod("add", Object.class, int.class);
                addMethod.invoke(storage, materialKey, amount);
                return true;
            }
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi thêm item vào kho: " + e.getMessage());
        }
        return false;
    }

    /**
     * Trừ item từ kho
     */
    public boolean removeItem(UUID uuid, String materialKey, int amount) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return false;

            long currentAmount = getItemAmount(uuid, materialKey);
            if (currentAmount >= amount) {
                Method subtractMethod = storageClass.getMethod("subtract", Object.class, int.class);
                subtractMethod.invoke(storage, materialKey, amount);
                return true;
            }
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi trừ item từ kho: " + e.getMessage());
        }
        return false;
    }

    /**
     * Kiểm tra xem storage có đang active không
     */
    public boolean isStorageActive(UUID uuid) {
        return getStorageStatus(uuid);
    }
    
    /**
     * Lấy thông tin không gian kho
     */
    public long getStorageSpace(UUID uuid) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return 0;

            Method getSpaceMethod = storageClass.getMethod("getSpace");
            return (long) getSpaceMethod.invoke(storage);
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy storage space: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Lấy không gian đã sử dụng
     */
    public long getUsedSpace(UUID uuid) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return 0;

            Method getUsedSpaceMethod = storageClass.getMethod("getUsedSpace");
            return (long) getUsedSpaceMethod.invoke(storage);
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy used space: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Lấy không gian còn trống
     */
    public long getFreeSpace(UUID uuid) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return 0;

            Method getFreeSpaceMethod = storageClass.getMethod("getFreeSpace");
            return (long) getFreeSpaceMethod.invoke(storage);
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy free space: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Kiểm tra xem kho có đầy không
     */
    public boolean isStorageFull(UUID uuid) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return false;

            Method isMaxSpaceMethod = storageClass.getMethod("isMaxSpace");
            return (boolean) isMaxSpaceMethod.invoke(storage);
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi kiểm tra storage full: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Lấy tất cả items trong kho
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getAllItems(UUID uuid) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return null;

            Method getItemsMethod = storageClass.getMethod("getItems");
            return (Map<String, Object>) getItemsMethod.invoke(storage);
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy tất cả items: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy storage status (boolean)
     */
    public boolean getStorageStatus(UUID uuid) {
        try {
            Object storage = getStorage(uuid);
            if (storage == null) return false;

            Method getStatusMethod = storageClass.getMethod("getStatus");
            return (boolean) getStatusMethod.invoke(storage);
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi lấy storage status: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Kiểm tra main class của ExtraStorage
     */
    private boolean assertMainClass(PluginDescriptionFile description) {
        return description.getMain().equals("me.hsgamer.extrastorage.ExtraStorage");
    }
    
    /**
     * Kiểm tra tên plugin
     */
    private boolean assertName(PluginDescriptionFile description) {
        return description.getName().equals("ExtraStorage");
    }
}
