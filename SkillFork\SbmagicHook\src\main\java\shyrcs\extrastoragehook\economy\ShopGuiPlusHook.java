package shyrcs.extrastoragehook.economy;

import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.RegisteredServiceProvider;
import shyrcs.extrastoragehook.SbMagicHook;

import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.util.function.Consumer;

/**
 * Economy provider cho ShopGUI+
 * Sử dụng reflection để tương thích với nhiều phiên bản
 */
public class ShopGuiPlusHook extends EconomyProvider {

    private boolean setup = false;
    private Economy econ;
    private static final DecimalFormat formatter = new DecimalFormat("###,###.##");

    public ShopGuiPlusHook() {
        if (this.isHooked()) {
            SbMagicHook.info("Đã kết nối với ShopGUI+ làm economy provider.");
        } else {
            SbMagicHook.warn("Không tìm thấy ShopGUI+ plugin!");
        }
    }

    @Override
    public boolean isHooked() {
        if (Bukkit.getPluginManager().getPlugin("ShopGUIPlus") == null) {
            return false;
        }
        if (!setup) {
            RegisteredServiceProvider<Economy> rsp = Bukkit.getServer().getServicesManager().getRegistration(Economy.class);
            econ = (rsp != null) ? rsp.getProvider() : null;
            setup = true;
        }
        return econ != null;
    }

    @Override
    public int getAmount(ItemStack item) {
        if (!this.isHooked()) {
            return item.getAmount();
        }

        try {
            Class<?> apiClass = Class.forName("net.brcdev.shopgui.ShopGuiPlusApi");
            Method getPriceMethod = apiClass.getMethod("getItemStackPriceSell", ItemStack.class);
            double price = (double) getPriceMethod.invoke(null, item);

            if (price == -1) {
                return item.getAmount();
            }

            Method getShopItemMethod = apiClass.getMethod("getItemStackShopItem", ItemStack.class);
            Object shopItem = getShopItemMethod.invoke(null, item);

            if (shopItem != null) {
                Method getItemMethod = shopItem.getClass().getMethod("getItem");
                ItemStack shopItemStack = (ItemStack) getItemMethod.invoke(shopItem);
                return shopItemStack.getAmount();
            }
        } catch (Exception e) {
            SbMagicHook.warn("Lỗi khi lấy amount từ ShopGUI+: " + e.getMessage());
        }

        return item.getAmount();
    }

    @Override
    public String getPrice(Player player, ItemStack item, int amount) {
        if (!this.isHooked()) {
            return "0.0";
        }

        try {
            Class<?> apiClass = Class.forName("net.brcdev.shopgui.ShopGuiPlusApi");
            Method getPriceMethod = apiClass.getMethod("getItemStackPriceSell", ItemStack.class);
            double basePrice = (double) getPriceMethod.invoke(null, item);

            if (basePrice == -1) {
                return "0.0";
            }

            Method getShopItemMethod = apiClass.getMethod("getItemStackShopItem", ItemStack.class);
            Object shopItem = getShopItemMethod.invoke(null, item);

            if (shopItem != null) {
                Method sellPriceMethod = shopItem.getClass().getMethod("getSellPriceForAmount", int.class);
                double totalPrice = (double) sellPriceMethod.invoke(shopItem, amount);

                // Áp dụng price modifier nếu có
                try {
                    Class<?> priceModifierActionType = Class.forName("net.brcdev.shopgui.modifier.PriceModifierActionType");
                    Object sellAction = priceModifierActionType.getField("SELL").get(null);

                    Method getPriceModifierMethod = apiClass.getMethod("getPriceModifier", Player.class, shopItem.getClass(), priceModifierActionType);
                    Object priceModifier = getPriceModifierMethod.invoke(null, player, shopItem, sellAction);

                    if (priceModifier != null) {
                        Method getModifierMethod = priceModifier.getClass().getMethod("getModifier");
                        double modifier = (double) getModifierMethod.invoke(priceModifier);
                        if (modifier > 1.0) {
                            totalPrice *= modifier;
                        }
                    }
                } catch (Exception modifierError) {
                    // Ignore modifier errors, use base price
                }

                return formatter.format(totalPrice);
            }
        } catch (Exception e) {
            SbMagicHook.warn("Lỗi khi lấy giá từ ShopGUI+: " + e.getMessage());
        }

        return "0.0";
    }

    @Override
    public void sellItem(Player player, ItemStack item, int amount, Consumer<Result> callback) {
        if (!this.isHooked()) {
            callback.accept(new Result(0, 0.0, false));
            return;
        }

        try {
            Class<?> apiClass = Class.forName("net.brcdev.shopgui.ShopGuiPlusApi");
            Method getPriceMethod = apiClass.getMethod("getItemStackPriceSell", ItemStack.class);
            double basePrice = (double) getPriceMethod.invoke(null, item);

            if (basePrice == -1) {
                callback.accept(new Result(0, 0.0, false));
                return;
            }

            Method getShopItemMethod = apiClass.getMethod("getItemStackShopItem", Player.class, ItemStack.class);
            Object shopItem = getShopItemMethod.invoke(null, player, item);

            if (shopItem != null) {
                Method sellPriceMethod = shopItem.getClass().getMethod("getSellPriceForAmount", int.class);
                double totalPrice = (double) sellPriceMethod.invoke(shopItem, amount);

                // Áp dụng price modifier nếu có
                try {
                    Class<?> priceModifierActionType = Class.forName("net.brcdev.shopgui.modifier.PriceModifierActionType");
                    Object sellAction = priceModifierActionType.getField("SELL").get(null);

                    Method getPriceModifierMethod = apiClass.getMethod("getPriceModifier", Player.class, shopItem.getClass(), priceModifierActionType);
                    Object priceModifier = getPriceModifierMethod.invoke(null, player, shopItem, sellAction);

                    if (priceModifier != null) {
                        Method getModifierMethod = priceModifier.getClass().getMethod("getModifier");
                        double modifier = (double) getModifierMethod.invoke(priceModifier);
                        if (modifier > 1.0) {
                            totalPrice *= modifier;
                        }
                    }
                } catch (Exception modifierError) {
                    // Ignore modifier errors, use base price
                }

                // Thực hiện giao dịch
                boolean success = econ.depositPlayer(player, totalPrice).transactionSuccess();
                callback.accept(new Result(amount, totalPrice, success));

                if (success) {
                    SbMagicHook.info("Đã bán " + amount + " " + item.getType() + " cho " + player.getName() + " với giá " + formatter.format(totalPrice));
                }
            } else {
                callback.accept(new Result(0, 0.0, false));
            }
        } catch (Exception e) {
            SbMagicHook.warn("Lỗi khi bán item qua ShopGUI+: " + e.getMessage());
            callback.accept(new Result(0, 0.0, false));
        }
    }
}