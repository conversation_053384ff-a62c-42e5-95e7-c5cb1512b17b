CancelEvent:
  Conditions:
  - incombat true
  Skills:
  - CancelEvent @self

abyssal_knight_slim_kill:
  Conditions:
  - playerwithin{d=20} true
  - hasaura{n=killing} false
  Skills:
  - CancelEvent @self
  - state{model=abyssal_knight_spawner_slim;state=kill} @self
  - setAI{ai=false} @self
  - aura{auraName=killing;duration=180} @self
  - delay 41
  - sound{s=custom.metal_impact3;v=3;p=1} @self
  - sound{s=entity.iron_golem.damage;v=10;p=1.2} @self
  - sound{s=entity.player.attack.crit;v=10;p=2} @self
  - delay 2
  - sound{s=custom.rock_impact;v=3;p=1} @self
  - delay 52
  - sound{s=custom.monster_scream1;v=3;p=1} @self
  - sound{s=custom.monster_scream1;v=3;p=1.} @self
  - setAI{ai=true} @self
  - delay 39
  - look{headOnly=false;immediately=true} @self
  - delay 13
  - sound{s=custom.slash2;v=3;p=1} @self
  - delay 2
  - skill{s=[ - projectile{bulletType=MOB;mob=slim_player_projectile;oH=[ - damage{a=15} ];bmd=true;sE=false;syo=2;tyo=2;d=15;v=30;hnp=true} @targetlocation ]} @ModelPart{mid=abyssal_knight_spawner_slim;pid=body3}
  - delay 31
  - summon{type=abyssal_knight;amount=1} @self
  - delay 1
  - remove @self
  