package shyrcs.extrastoragehook.application;

import shyrcs.extrastoragehook.bridge.BridgeHook;
import shyrcs.extrastoragehook.bridge.StorageHook;
import shyrcs.extrastoragehook.config.DataConfig;
import shyrcs.extrastoragehook.discord.BotImpl;
import shyrcs.extrastoragehook.economy.EconomyManager;
import shyrcs.extrastoragehook.executor.CommandManager;
import shyrcs.extrastoragehook.extrastorage.ExtraStorageHook;
import net.milkbowl.vault.economy.Economy;

/**
 * Library class chứa tất cả các component chính của plugin
 * Tương tự như Library trong HyperHook nhưng sử dụng ExtraStorage
 */
public class Library {
    
    /**
     * Config chính của plugin
     */
    public static DataConfig config;
    
    /**
     * Bridge hook để kết nối Discord với Minecraft
     */
    public static BridgeHook bridge;
    
    /**
     * Storage hook để quản lý kết nối người dùng
     */
    public static StorageHook storage;
    
    /**
     * Command manager cho Discord commands
     */
    public static CommandManager manager;
    
    /**
     * Discord bot application
     */
    public static BotImpl application;
    
    /**
     * ExtraStorage hook thay vì PreventHopper
     */
    public static ExtraStorageHook extraStorageHook;
    
    /**
     * Economy Manager
     */
    public static EconomyManager economyManager;

    /**
     * Vault Economy provider
     */
    public static Economy economy;
}
