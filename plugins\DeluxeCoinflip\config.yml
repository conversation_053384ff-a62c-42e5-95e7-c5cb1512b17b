# DeluxeCoinflip - Configuration
# Author: Zithium Studios
# Copyright Zithium Studios 2021 - 2025. All rights reserved.
#
# Developer API
#   https://github.com/itzsave/DeluxeCoinflipAPI

# Should the plugin report stats to https://bstats.org/
# All usage statistics are anonymous and has zero impact on performance.
metrics: true

storage:
  # Types available:
  # - SQLITE
  type: SQLITE

settings:
  # Min/max must be between 0 and 9,223,372,036,854,775,808
  minimum-bet: 1000
  maximum-bet: *********

  # Aliases to the '/coinflip' command
  command_aliases:
    - cf

  # Should we broadcast when a coinflip is created?
  broadcast-coinflip-creation: false

  # How much needs to be won to broadcast to everyone? Set to 0 to broadcast all winnings.
  minimum-broadcast-winnings: 1000

  tax:
    enabled: true
    rate: 5.0 # 5% tax

  # Economy providers
  #   If you are using custom provider (from our API),
  #   ensure to add the section with the provider identifier
  #   being the main key (e.g., VAULT).
  providers:
    # This economy provider will be used if the player does /cf create <amount>
    # without specifying a currency provider after the amount to use if multiple
    # providers are enabled.
    default_provider: "VAULT"

    # Vault Economy (requires Vault + economy plugin)
    # https://dev.bukkit.org/projects/vault
    VAULT:
      enabled: true
      display_currency_name: "Money"

    # TokenEnchant Economy (requires TokenEnchant plugin)
    # https://polymart.org/resource/155
    TOKENENCHANT:
      enabled: true
      display_currency_name: "TokenEnchant"

    # PlayerPoints Economy (requires PlayerPoints plugin)
    # https://www.spigotmc.org/resources/playerpoints.80745/
    PLAYERPOINTS:
      enabled: false
      display_currency_name: "PlayerPoints"

    # TokenManager Economy (requires TokenManager plugin)
    # https://www.spigotmc.org/resources/8610/
    TOKENMANAGER:
      enabled: true
      display_currency_name: "TokenManager"

    # ZithiumMobcoins integration (requires ZithiumMobcoins plugin)
    # https://www.spigotmc.org/resources/113153/
    ZITHIUMMOBCOINS:
      enabled: true
      display_currency_name: "Mobcoins"

    # BeastTokens Economy (requires BeastTokens plugin)
    # https://www.spigotmc.org/resources/20806/
    BEASTTOKENS:
      enabled: true
      display_currency_name: "Tokens"

    CUSTOM_CURRENCY:
      # Should the custom currency be enabled?
      enabled: true
      display_currency_name: "Custom"
      # This is used to check the balance of the user. BE SURE THIS DOES NOT FORMAT THE BALANCE
      raw_balance_placeholder: '%vault_eco_balance%'
      # These are the commands used to withdraw and deposit winnings to the user. They are ran by console.
      commands:
        withdraw: 'eco take {player} {amount}'
        deposit: 'eco give {player} {amount}'

coinflip-gui:
  title: "&lFLIPPING COIN..."
  # There can only be two items here. Both must be present!
  animation:
    1:
      material: YELLOW_STAINED_GLASS_PANE
      display_name: '&r'
    2:
      material: GRAY_STAINED_GLASS_PANE
      display_name: '&r'

games-gui:
  title: "&lCOINFLIP GAMES"
  rows: 4

  # You can add more items here
  filler-items:
    border:
      material: GRAY_STAINED_GLASS_PANE
      display_name: "&r"
      slots: [ 27, 28, 29, 33, 34 ]
    stats:
      slot: 31
      material: BOOK
      display_name: "&6&l&nSTATISTICS"
      lore:
        - ""
        - "&fHere you can view your statistics"
        - "&ffrom all your Coinflip games."
        - ""
        - " &ePlayer: &f{PLAYER}"
        - ""
        - " &eWins: &f{WINS}"
        - " &eLosses: &f{LOSSES}"
        - " &eProfit: &f${PROFIT}"
        - " &eTotal Lost: &f${TOTAL_LOSSES}"
        - " &eTotal Gambled: &f${TOTAL_GAMBLED}"
        - " &eWin Percentage: &f{WIN_PERCENTAGE}%"

  # Items below here are fixed, and the ID should not be removed/changed.
  create-new-game:
    # Set false to disable the game builder gui
    enabled: true
    slot: 35
    material: NETHER_STAR
    display_name: "&a&lCreate Game"
    lore:
      - "&7Click here to create a new game!"
  coinflip-game:
    display_name: "&e{PLAYER}"
    material: PLAYER_HEAD
    lore:
      - ""
      - "&e&lWager"
      - "  &f{AMOUNT}"
      - ""
      - "&e&lCurrency"
      - "  &f{CURRENCY}"
      - ""
      - "&7&oClick here to start a Coinflip game"
  no-games:
    slot: 13
    material: BARRIER
    display_name: "&cNo games found!"
    lore:
      - ""
      - "&fThere are no active coinflip games."
      - "&fStart one with &e/coinflip create <amount>"
  error-no-funds:
    material: BARRIER
    display_name: "&cInsufficient funds!"
    lore:
      - ""
      - "&fYou do not have enough funds to play this coinflip"
  next-page:
    slot: 32
    material: PAPER
    display_name: "&2&lNext Page"
    lore:
      - "&7Click to go to the next page."
  previous-page:
    slot: 30
    material: PAPER
    display_name: "&c&lPrevious Page"
    lore:
      - "&7Click to go to the previous page."

gamebuilder-gui:
  title: "&lGAME BUILDER"
  rows: 3

  filler-items:
    border:
      material: GRAY_STAINED_GLASS_PANE
      display_name: "&r"
      slots: [ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 17, 19, 20, 21, 22, 23, 24, 25 ]

  amount-items:
    plus_100:
      slot: 14
      material: PLAYER_HEAD
      base64: eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNWZmMzE0MzFkNjQ1ODdmZjZlZjk4YzA2NzU4MTA2ODFmOGMxM2JmOTZmNTFkOWNiMDdlZDc4NTJiMmZmZDEifX19
      display_name: '&a&l+100'
      set_amount: '+100'
    plus_500:
      slot: 15
      material: PLAYER_HEAD
      base64: eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNWZmMzE0MzFkNjQ1ODdmZjZlZjk4YzA2NzU4MTA2ODFmOGMxM2JmOTZmNTFkOWNiMDdlZDc4NTJiMmZmZDEifX19
      display_name: '&a&l+500'
      set_amount: '+500'
    plus_1000:
      slot: 16
      material: PLAYER_HEAD
      base64: eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNWZmMzE0MzFkNjQ1ODdmZjZlZjk4YzA2NzU4MTA2ODFmOGMxM2JmOTZmNTFkOWNiMDdlZDc4NTJiMmZmZDEifX19
      display_name: '&a&l+1,000'
      set_amount: '+1000'
    minus_1000:
      slot: 10
      material: PLAYER_HEAD
      base64: eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNGU0YjhiOGQyMzYyYzg2NGUwNjIzMDE0ODdkOTRkMzI3MmE2YjU3MGFmYmY4MGMyYzViMTQ4Yzk1NDU3OWQ0NiJ9fX0=
      display_name: '&c&l-1,000'
      set_amount: '-1000'
    minus_500:
      slot: 11
      material: PLAYER_HEAD
      base64: eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNGU0YjhiOGQyMzYyYzg2NGUwNjIzMDE0ODdkOTRkMzI3MmE2YjU3MGFmYmY4MGMyYzViMTQ4Yzk1NDU3OWQ0NiJ9fX0=
      display_name: '&c&l-500'
      set_amount: '-500'
    minus_100:
      slot: 12
      material: PLAYER_HEAD
      base64: eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNGU0YjhiOGQyMzYyYzg2NGUwNjIzMDE0ODdkOTRkMzI3MmE2YjU3MGFmYmY4MGMyYzViMTQ4Yzk1NDU3OWQ0NiJ9fX0=
      display_name: '&c&l-100'
      set_amount: '-100'

  currency-select:
    slot: 13
    material: OAK_SIGN
    display_name: "&f&lCurrency"
    lore-header:
      - "&r"
      - "&7Amount Bet: &f{BET_AMOUNT}"
      - "&r"
      - "&eSelected Currency"
    currency_lore_selected: "&a&l{CURRENCY}"
    currency_lore_unselected: "&7{CURRENCY}"
    lore-footer:
      - "&r"
  custom-amount:
    slot: 18
    material: ANVIL
    display_name: "&6&lSet Custom Amount"
    lore:
      - "&7Click to enter a value in chat!"
  create-game:
    slot: 26
    material: LIME_DYE
    display_name: "&a&lCreate Game"
    lore:
      - "&7Click to create your game"
  error-limits:
    material: BARRIER
    display_name: "&cOut of betting limits!"
    lore:
      - ""
      - "&fYou must keep between the minimum"
      - "&fand maximum betting limits."
  error-game-exists:
    material: BARRIER
    display_name: "&cYou already have a game active!"
    lore:
      - ""
      - "&fYou must &l/cf delete &fyour current"
      - "&fgame before creating a new one."
  error-no-funds:
    material: BARRIER
    display_name: "&cInsufficient funds!"
    lore:
      - ""
      - "&fYou do not have enough to create this game"