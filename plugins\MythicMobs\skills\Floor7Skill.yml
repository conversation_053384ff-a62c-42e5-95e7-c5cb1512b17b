# For more information, check out the plugin manual or join us on Discord:
# http://www.mythicmobs.net
#  

FloridPlayerKill:
  Skills:
  - healpercent{m=0.05} @self
  - modifytargetscore{obj=KillCount;action=ADD;v=1} @self ~onPlayerKill
  - potion{type=HARM;lvl=10;duration=20} @PIR{r=100}
  - potion{type=HUNGER;lvl=3;duration=6000} @PIR{r=100}
  - potion{type=POISON;lvl=5;duration=1000} @PIR{r=100}
  - potion{type=WITHER;lvl=5;duration=1000} @PIR{r=100}
  - message{m="&f[&cCảnh báo&f] &3Cường hóa của &a&l<mob.name> &3đã đạt bậc &d<mob.score.KillCount>&f."} @PIR{r=100}
FloridPassive:
  Conditions:
  - stance Combat
  Skills:
  - heal{a=10000} @self
  - damage{a=100;pkb=false} @PIR{r=100}
  - potion{type=BLINDNESS;lvl=2;duration=6000} @PIR{r=100}
  - potion{type=SLOW;lvl=2;duration=6000} @PIR{r=100}
  - potion{type=SLOW_DIGGING;lvl=2;duration=6000} @PIR{r=100}
  - potion{type=WEAKNESS;lvl=2;duration=6000} @PIR{r=100}
FloridOnSpawn:
  Skills:
  - prison{m=barrier;d=300;b=false} @PIR{r=100}
  - message{m="&f[&a<mob.name>&f] &bA! Cuối cùng cũng thấy cậu rồi!"} @PIR{r=100}
  - delay 60
  - message{m="&f[&a<mob.name>&f] &bSẵn sàng chưa nhỉ? Tớ sẽ bung hết sức đấy nhé!"} @PIR{r=100}
  - delay 60
  - message{m="&f[&a<mob.name>&f] &bĐược rồi! Quẩy thôi nào! Hehe....."} @PIR{r=100}
  - delay 60
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @PIR{r=100}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @PIR{r=100}
  - potion{type=BLINDNESS;lvl=2;duration=6000} @PIR{r=100}
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @PIR{r=100}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @PIR{r=100}
  - potion{type=SLOW;lvl=2;duration=6000} @PIR{r=100}
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @PIR{r=100}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @PIR{r=100}
  - potion{type=SLOW_DIGGING;lvl=2;duration=6000} @PIR{r=100}
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @PIR{r=100}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @PIR{r=100}
  - potion{type=WEAKNESS;lvl=2;duration=6000} @PIR{r=100}
  - effect:particles{p=instantSpell;a=100;y=1;points=10;s=0.1;hS=0.3;vS=0.3} @self
  - delay 60
  - setstance{s=Combat} @self
  - message{m="&f[&3Floor 7&f] &3Đã cung cấp đủ hiệu ứng."} @PIR{r=100}
FloridFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - sendtitle{t="&c&l10";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l9";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l8";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l7";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l6";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&c&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=FloridSuccessSkill}
FloridSuccessSkill:
  TargetConditions:
  - onBlock{b=BEDROCK} orElseCast FloridFailSkill
  Skills:
  - sendtitle{t="&a&lSafe";d=20} 
  - effect:sound{s=entity.experience_orb.pickup;p=0.5;v=1}
  - message{m="&f&l[&a&l<mob.name>&f&l] &bCũng đỉnh đó. Chắc hong có lần sau đâu nhỉ."} 
  - heal{a=100} @PIR{r=100}
FloridFailSkill:
  Skills:
  - sendtitle{t="&c&lFail";d=20}
  - effect:sound{s=entity.player.death;p=2;v=1}
  - message{m="&f&l[&a&l<mob.name>&f&l] &bHoy, cố lần sau nhe."} 
  - damagepercent{p=0.8;ia=true} @PIR{r=100}
#FloridSecondSkill:
#  Skills:
#  - projectile{onTick=FSSOnTick;onHit=FSSOnHit;onEnd=FSSOnEnd;b=arrow;i=1;v=12;hR=2;vR=2} @PIR{r=100}
#FSSOnTick:
#  Skills:
#  - effect:particles{p=totem;amount=20;speed=0;hs=.3;vs=.3} @origin
#  - effect:particles{p=happyvillager;amount=100;speed=0.1;hs=.3;vs=.3} @origin
#  - sound{s=entity.enderdragon.growl;v=1} @origin
#FSSOnHit:
#  Skills:
#  - damage{a=1000}
#  - teleport
#  - throw{velocitiy=35;velocityY=15} @PIR{r=3}
#FSSOnEnd:
#  Skills:
#  - effect:particles{p=instantSpell;a=100;hS=1;vS=1}
#  - effect:sound{s=entity.generic.explode;v=1;p=0}
#  - damage{a=1500;ia=false} @PIR{r=5}
FloridSecondSkill:
  Conditions:
  - stance Combat
  Skills:
  - shield{a=10000;ma=50000} @self
FloridThirdSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&f&l[&a&l<mob.name>&f&l] &bĐược òi. Núp lẹ đi mấy cưng. Hong là đi đóa."}
  - delay 60
  - message{m="&f&l[&a&l<mob.name>&f&l] &bHun cái nà."}
  - potion{type=SLOW;lvl=50;duration=100} @PIR{r=100}
  - pull{v=5} @PIR{r=100}
  - delay 3
  - pull{v=5} @PIR{r=100}
  - delay 3
  - pull{v=5} @PIR{r=100}
  - delay 3
  - pull{v=5} @PIR{r=100}
  - delay 40
  - skill{s=FloridThirdFailSkill}
FloridThirdFailSkill:
  TargetConditions:
  - distance{d=<5} true
  Skills:
  - forcepull{s=2}
  - prison{m=barrier;d=200;b=false}
  - message{m="&f&l[&a&l<mob.name>&f&l] &bHoy xong ròi. Bái bai nha. Hẹn hong gặp lại~~"}
  - delay 40
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - damagepercent{p=1;ia=true}
FloridFourthSkill:
  Cooldown: 300
  Skills:
  - teleport @origin
  - prison{m=barrier;d=150;b=false} @PIR{r=100}
  - message{m="&f&l[&a&l<mob.name>&f&l] &bOaaa. Ghê dữ vậy trời."} @PIR{r=100}
  - delay 20
  - message{m="&f&l[&a&l<mob.name>&f&l] &bThôi thì không đùa giỡn nữa nhỉ."} @PIR{r=100}
  - delay 20
  - message{m="&f&l[&a&l<mob.name>&f&l] &bVào công chuyện thôi nào."} @PIR{r=100}
  - delay 20
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=INCREASE_DAMAGE;lvl=4;duration=6000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=SPEED;lvl=4;duration=6000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=ABSORPTION;lvl=4;duration=6000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=PROTECTION;lvl=4;duration=6000} @self
  - message{m="&f&l[&a&l<mob.name>&f&l] &a&lMode &c&lTận Diệt&b, khởi động."} @PIR{r=100}
  - delay 20
  - healpercent{m=100} @self
  - effect:particlering{p=totem;a=20;points=25;r=10;hS=1;vS=0} @self
  - effect:bloodyscreen{d=40} @PIR{r=100}
  - forcepull{s=1} @PIR{r=100}
  - effect:lightning{repeat=5;repeatInterval=2} @PIR{r=100}
  - lightning{d=100} @PIR{r=100}
FloridOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&a&l<mob.name>&f&l] &aChúc..... mừng..... cậu....!"} @PIR{r=25} ~onDeath
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
MisterimOnSpawn:
  Skills:
  - prison{m=barrier;d=300;b=false} @PIR{r=100}
  - message{m="&f[&c<mob.name>&f] &bỦa, ai đấy?"} @PIR{r=100}
  - delay 60
  - message{m="&f[&c<mob.name>&f] &bÀ... Các nhà thử thách mới à?"} @PIR{r=100}
  - delay 60
  - message{m="&f[&c<mob.name>&f] &bOke! Muốn thì chiến nà."} @PIR{r=100}
  - delay 60
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=INCREASE_DAMAGE;lvl=2;duration=60000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=SPEED;lvl=2;duration=60000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=ABSORPTION;lvl=2;duration=60000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=PROTECTION;lvl=2;duration=60000} @self
  - effect:particles{p=instantSpell;a=100;y=1;points=10;s=0.1;hS=0.3;vS=0.3} @self
  - message{m="&f[&a<mob.name>&f] &bBản sao... Trỗi dậy!!"} @PIR{r=100}
  - summon{t=CloneMisterim;a=4;r=5;os=false;sip=true}
  - delay 60
  - message{m="&f[&3Floor 7&f] &3Đã cung cấp đủ hiệu ứng."} @PIR{r=100}
MisterimPassive:
  Skills:
  - heal{a=2000} @self
MisterimFirstSkill:
  Skills:
  - shootfireball{y=8;v=5;i=true;ft=40;sml=true} @PIR{r=100}
  - delay 5
  - shootfireball{y=8;v=5;i=true;ft=40;sml=true} @PIR{r=100}
  - delay 5
  - shootfireball{y=8;v=5;i=true;ft=40;sml=true} @PIR{r=100}
  - delay 5
  - shootfireball{y=8;v=5;i=true;ft=40;sml=true} @PIR{r=100}
  - delay 5
  - shootfireball{y=8;v=5;i=true;ft=40;sml=true} @PIR{r=100}
  - delay 5
  - shootfireball{y=8;v=5;i=true;ft=40;sml=true} @PIR{r=100}
  - delay 5
  - shootfireball{y=8;v=5;i=true;ft=40;sml=true} @PIR{r=100}
  - delay 5
  - shootfireball{y=8;v=5;i=true;ft=40;sml=true} @PIR{r=100}
  - delay 5
MisterimSecondSkill:
  Skills:
  - sendtitle{t="&d&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=MisterimSuccessSkill}
MisterimSuccessSkill:
  TargetConditions:
  - inside orElseCast MisterimFailSkill
  Skills:
  - message{m="&f[&c<mob.name>&f] &bAiss... Chán òm."} @PIR{r=100}
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} 
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} 
  - potion{type=INCREASE_DAMAGE;lvl=2;duration=300}
MisterimFailSkill:
  Skills:
  - message{m="&f[&c<mob.name>&f] &bMọi thứ trở nên thú vị hơn rồi đó."} @PIR{r=100}
  - effect:explosion
  - damage{a=350}
MisterimThirdSkill:
  Skills:
  - randomskill{s=MisterimCrouchCheckSkill,MisterimJumpCheckSkill} @PIR{r=100}
MisterimCrouchCheckSkill:
  Skills:
  - message{m="&f[&c<mob.name>&f] &bCúi xuống! Lẹ lên!"} @PIR{r=100}
  - delay 20
  - skill{s=MisterimCrouchSkill}
MisterimJumpCheckSkill:
  Skills:
  - message{m="&f[&c<mob.name>&f] &bNhảy lên! Lẹ lên!"} @PIR{r=100}
  - delay 20
  - skill{s=MisterimJumpSkill}
MisterimCrouchSkill:
  TargetConditions:
  - crouching orElseCast MisterimThirdFailSkill 
  Skills:
  - message{m="&f[&c<mob.name>&f] &bẦy, phản xạ tốt đó."} @PIR{r=100}
MisterimJumpSkill:
  TargetConditions:
  - heightabove{h=35} orElseCast MisterimThirdFailSkill
  Skills:
  - message{m="&f[&c<mob.name>&f] &bẦy, phản xạ tốt đó."} @PIR{r=100}
MisterimThirdFailSkill:
  Skills:
  - message{m="&f[&c<mob.name>&f] &bHoy, có lần sau mà."} @PIR{r=100}
  - effect:bloodyscreen{d=40} @PIR{r=100}
  - damagepercent{p=0.1} @PIR{r=100}
MisterimOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&c&l<mob.name>&f&l] &bChúc may mắn với chị em toy nhé!"} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
  - command{c="mm m kill CloneMisterim";op=true}
CloneOnDeath:
  Skills:
  - message{m="&f[&c<mob.name>&f] &bHa! Nhầm người rồi cưng!"} @PIR{r=100}
  - healpercent{m=5} @MIR{r=20;t=Misterim;limit=1;sort=NEAREST}
AstellaOnSpawn:
  Skills:
  - prison{m=barrier;d=300;b=false} @PIR{r=100}
  - message{m="&f[&b<mob.name>&f] &dHè lố! Đợi cũng lâu ròi đó. "} @PIR{r=100}
  - delay 60
  - message{m="&f[&b<mob.name>&f] &dMún tiến tiếp thì phải bước qua xác toy."} @PIR{r=100}
  - delay 60
  - message{m="&f[&b<mob.name>&f] &dCháy lên thôi nào!!"} @PIR{r=100}
  - delay 60
  - message{m="&f[&b<mob.name>&f] &dMode &3&lLiên Kích&d, khởi động."} @PIR{r=100}
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=INCREASE_DAMAGE;lvl=6;duration=60000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=SPEED;lvl=4;duration=60000} @self
  - delay 10
  - setstance{s=Combat} @self
  - message{m="&f[&3Floor 7&f] &3Đã cung cấp đủ hiệu ứng."} @PIR{r=100}
AstellaOnKill:
  Skills:
  - message{m="&f[&b<mob.name>&f] &dWoo wee!! Dễ quá đó. Buff cái nhẹ nà. "} @PIR{r=100}
  - modifytargetscore{obj=AstellaKillCount;action=ADD;v=1} @self ~onPlayerKill
  - potion{type=HARM;lvl=10;duration=20} @PIR{r=100}
  - potion{type=HUNGER;lvl=3;duration=6000} @PIR{r=100}
  - potion{type=POISON;lvl=5;duration=1000} @PIR{r=100}
  - potion{type=WITHER;lvl=5;duration=1000} @PIR{r=100}
  - message{m="&f[&cCảnh báo&f] &3Cường hóa của &b&l<mob.name> &3đã đạt bậc &d<mob.score.AstellaKillCount>&f."} @PIR{r=100}
AstellaOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&b&l<mob.name>&f&l] &dVui vẻ với 2 người còn lại nhé!"} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
AstellaPassive:
  Skills:
  - damage{a=10;ia=true;ir=true;ieff=true} @PIR{r=100}
AstellaFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&f[&b<mob.name>&f] &dÚ òa!"} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damage{a=500} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - stun{d=40;f=true} @NearestPlayer{r=100}
AstellaSecondEngage:
  Conditions:
  - stance Combat
  - health{h=<3333333}
  Cooldown: 100000
  Skills:
  - message{m="&f[&b<mob.name>&f] &dAida.. Bắt đầu đau rồi đó."} @PIR{r=100}
AstellaSecondSkill:
  Conditions:
  - stance Combat
  - health{h=<3333333}
  Skills:
  - effect:bloodyscreen{d=20} @PIR{r=100}
  - lightning{d=80} @PIR{r=100}
  - aura{auraname=xoetxoet;os=xoetxoet_start;ot=xoetxoet_tick;oe=xoetxoet_end;i=2;d=120}
xoetxoet_start:
  Skills:
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self
  - damage{a=150} @target
  - delay 20
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self
  - damage{a=150} @PIR{r=5}
  - delay 10
  - effect:particles{particle=reddust;color=#7D26CD;amount=200;hS=0;vS=1.5;speed=0;dir=0,0.5,2} @self
  - damage{a=140;ia=true} @PIR{r=5}
xoetxoet_tick:
  Skills:
  - damage{a=40} @target ~onAttack
  - damage{a=10;ia=true} @target ~onAttack
  - teleport @target
xoetxoet_end:
  Skills:
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
  - damage{a=130} @target
  - throw{velocity=18;velocityY=5} @target
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
  - damage{a=130} @target
  - throw{velocity=18;velocityY=5} @target
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
  - damage{a=130} @target
  - throw{velocity=18;velocityY=5} @target  
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
  - damage{a=130} @target
  - throw{velocity=18;velocityY=5} @target
AstellaThirdSkill:
  Conditions:
  - stance Combat
  - health{h=<1666667}
  Skills:
  - teleport @origin
  - stun{d=160;f=false} @self
  - setstance{s=Charging} @self
  - sendtitle{t="&b&l8";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&b&l7";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&b&l6";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&b&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&b&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&b&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&b&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&b&l1";d=20} @PIR{r=100}
  - delay 20
  - setstance{s=Combat} @self
  - skill{s=AstellaThirdSuccessSkill}
AstellaThirdSuccessSkill:
  TargetConditions:
  - lineofsight castInstead AstellaThirdFailSkill
  Skills:
  - message{m="&f[&b<mob.name>&f] &dNúp lẹ quá dậy... Haizz."} @PIR{r=100}
  - heal{a=200} @PIR{r=100}
AstellaThirdFailSkill:
  Skills:
  - message{m="&f[&b<mob.name>&f] &dBắt được rồi nhé."} @PIR{r=100}
  - damagepercent{p=0.8;ia=true}
  - heal{a=10000} @self
  - delay 20
  - message{m="&f[&b<mob.name>&f] &dChúc may mắn lần sau."} @PIR{r=100}
DolcyllisOnSpawn:
  Skills:
  - prison{m=barrier;d=200;b=false} @PIR{r=100}
  - message{m="&d[&d<mob.name>&f] &aÀ rế? Chúng ta có khách nè."} @PIR{r=100}
  - delay 60
  - message{m="&f[&d<mob.name>&f] &aGì cơ? Bạn muốn gặp Florid à?"} @PIR{r=100}
  - delay 60
  - message{m="&f[&d<mob.name>&f] &aOke. Toàn mạng bước khỏi đây trước đã."} @PIR{r=100}
  - delay 60
  - message{m="&r&f&l[&d<mob.name>&f&l] &aChỉ có 5 phút thôi nha!"} @PIR{r=100}
  - delay 10
  - setstance{s=Combat} @self
  - message{m="&f[&3Floor 7&f] &3Đã cung cấp đủ hiệu ứng."} @PIR{r=100}
DolcyllisPassive:
  Skills:
  - heal{a=10000} @self
DolcyllisOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&d&l<mob.name>&f&l] &aCố lên nhé!"} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
DolcyllisRandomSkill:
  Skills:
  - randomskill{s=DolcyllisFirstSkill,DolcyllisSecondSkill,DolcyllisRandomOneSkill,DolcyllisRandomTwoSkill,DolcyllisRandomThreeSkill,DolcyllisRandomFourSkill,DolcyllisRandomFiveSkill} @PIR{r=100}
DolcyllisFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&r&f[&d<mob.name>&f] &aChặn khiên đi nèo."} @PIR{r=100}
  - sendtitle{t="&d&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=DolcyllisFirstSuccessSkill}
DolcyllisSecondSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&r&f[&d<mob.name>&f] &aCúi xuống cái nhẹ nà."} @PIR{r=100}
  - sendtitle{t="&d&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=DolcyllisSecondSuccessSkill}
DolcyllisRandomOneSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&r&f[&d<mob.name>&f] &aĐứng trên &dBedrock &ađi nào."} @PIR{r=100}
  - sendtitle{t="&d&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=DolcyllisRandomOneSuccessSkill}
DolcyllisRandomTwoSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&r&f[&d<mob.name>&f] &aĐứng trên &dDiamond Block &ađi nào."} @PIR{r=100}
  - sendtitle{t="&d&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=DolcyllisRandomTwoSuccessSkill}
DolcyllisRandomThreeSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&r&f[&d<mob.name>&f] &aĐứng trên &dGold Block &ađi nào."} @PIR{r=100}
  - sendtitle{t="&d&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=DolcyllisRandomThreeSuccessSkill}
DolcyllisRandomFourSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&r&f[&d<mob.name>&f] &aĐứng trên &dEmerald Block &ađi nào."} @PIR{r=100}
  - sendtitle{t="&d&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=DolcyllisRandomFourSuccessSkill}
DolcyllisRandomFiveSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&r&f[&d<mob.name>&f] &aĐứng trên &dIron Block &ađi nào."} @PIR{r=100}
  - sendtitle{t="&d&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&d&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=DolcyllisRandomFiveSuccessSkill}
DolcyllisFirstSuccessSkill:
  TargetConditions:
  - blocking orElseCast DolcyllisFailSkill
  Skills:
  - message{m="&f[&d<mob.name>&f] &aAida. Đau đó :<"} @PIR{r=100}
  - damage{a=60000;pkb=true;ii=true} @self
DolcyllisSecondSuccessSkill:
  TargetConditions:
  - crouching orElseCast DolcyllisFailSkill
  Skills:
  - message{m="&f[&d<mob.name>&f] &aAida. Đau đó :<"} @PIR{r=100}
  - damage{a=300000;pkb=true;ii=true} @self
DolcyllisRandomOneSuccessSkill:
  TargetConditions:
  - onBlock{b=BEDROCK} orElseCast DolcyllisFailSkill
  Skills:
  - message{m="&f[&d<mob.name>&f] &aAida. Đau đó :<"} @PIR{r=100}
  - damage{a=300000;pkb=true;ii=true} @self
DolcyllisRandomTwoSuccessSkill:
  TargetConditions:
  - onBlock{b=DIAMOND_BLOCK} orElseCast DolcyllisFailSkill
  Skills:
  - message{m="&f[&d<mob.name>&f] &aAida. Đau đó :<"} @PIR{r=100}
  - damage{a=300000;pkb=true;ii=true} @self
DolcyllisRandomThreeSuccessSkill:
  TargetConditions:
  - onBlock{b=GOLD_BLOCK} orElseCast DolcyllisFailSkill
  Skills:
  - message{m="&f[&d<mob.name>&f] &aAida. Đau đó :<"} @PIR{r=100}
  - damage{a=300000;pkb=true;ii=true} @self
DolcyllisRandomFourSuccessSkill:
  TargetConditions:
  - onBlock{b=EMERALD_BLOCK} orElseCast DolcyllisFailSkill
  Skills:
  - message{m="&f[&d<mob.name>&f] &aAida. Đau đó :<"} @PIR{r=100}
  - damage{a=300000;pkb=true;ii=true} @self
DolcyllisRandomFiveSuccessSkill:
  TargetConditions:
  - onBlock{b=IRON_BLOCK} orElseCast DolcyllisFailSkill
  Skills:
  - message{m="&f[&d<mob.name>&f] &aAida. Đau đó :<"} @PIR{r=100}
  - damage{a=300000;pkb=true;ii=true} @self
DolcyllisFailSkill:
  Skills:
  - message{m="&f[&d<mob.name>&f] &aAww... Cẩn thận nha. Thời gian và mạng sống đang trôi đó!"} @PIR{r=100}
  - heal{a=60000} @self
  - damagepercent{p=0.6;ia=true} @PIR{r=100}
DolcyllisTimerBegin:
  Conditions:
  - stance Combat
  Cooldown: 10000
  Skills:
  - message{m="&r&f&l[&d<mob.name>&f&l] &aBắt đầu tính giờ!!"} @PIR{r=100}
  - delay 1200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a1 phút đã trôi qua!"} @PIR{r=100}
  - delay 1200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a2 phút đã trôi qua!"} @PIR{r=100}
  - delay 1200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a3 phút đã trôi qua!"} @PIR{r=100}
  - delay 1200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a4 phút đã trôi qua!"} @PIR{r=100}
  - delay 1200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a5 phút ròi. Bái bai nhé."} @PIR{r=100}
  - damage{a=100000;ia=true} @PIR{r=200}
  - delay 5
  - damage{a=100000;ia=true} @PIR{r=200}
  - delay 5
  - damage{a=100000;ia=true} @PIR{r=200}
  - delay 5
  - damage{a=100000;ia=true} @PIR{r=200}
  - delay 5
  - damage{a=100000;ia=true} @PIR{r=200}
  - delay 5
  - damage{a=100000;ia=true} @PIR{r=200}
  - delay 5
DolcyllisFourthSkill:
  Conditions:
  - stance Combat
  Skills:
  - randomskill{s=RadiusFour,RadiusSix,RadiusNine,RadiusEleven}
DolcyllisHealthCheck:
  Conditions:
  - stance Combat
  - health{h=<375000}
  Skills:
  - message{m="&r&f&l[&d<mob.name>&f&l] &aHaida... Thôi, giúp cho nè."} @PIR{r=100}
  - potion{type=INCREASE_DAMAGE;lvl=6;duration=1000} @PIR{r=100}
  - message{m="&r&f&l[&d<mob.name>&f&l] &aCòn 60s nữa trước khi bay màu nhé :3"} @PIR{r=100}
  - delay 20
  - message{m="&r&f&l[&d<mob.name>&f&l] &aĐếm ngược nà."} @PIR{r=100}
  - setstance{s=Weaken} @self
  - skill{s=DolcyllisLastEffort}
DolcyllisLastEffort:
  Conditions:
  - stance Weaken
  Skills:
  - message{m="&r&f&l[&d<mob.name>&f&l] &a60."} @PIR{r=100}
  - delay 200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a50."} @PIR{r=100}
  - delay 200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a40."} @PIR{r=100}
  - delay 200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a30."} @PIR{r=100}
  - delay 200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a20."} @PIR{r=100}
  - delay 200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a10."} @PIR{r=100}
  - delay 200
  - message{m="&r&f&l[&d<mob.name>&f&l] &a0. Bái bai. Tiếc quá nhỉ."} @PIR{r=100}
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
RadiusFour:
  Conditions:
  - stance Combat
  Skills:
  - teleport @origin
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=4;p=4600;y=0;exact=true}
  - message{m="&d[&e<mob.name>&d] &a3."} @PIR{r=100}
  - delay 20
  - message{m="&d[&e<mob.name>&d] &a2."} @PIR{r=100}
  - delay 20
  - message{m="&d[&e<mob.name>&d] &a1."} @PIR{r=100}
  - delay 20
  - skill{s=RadiusSuccessFour}
RadiusSix:
  Conditions:
  - stance Combat
  Skills:
  - teleport @origin 
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=6;p=4600;y=0;exact=true}
  - message{m="&d[&e<mob.name>&d] &a3."} @PIR{r=100}
  - delay 20
  - message{m="&d[&e<mob.name>&d] &a2."} @PIR{r=100}
  - delay 20
  - message{m="&d[&e<mob.name>&d] &a1."} @PIR{r=100}
  - delay 20
  - skill{s=RadiusSuccessSix}
RadiusNine:
  Conditions:
  - stance Combat
  Skills:
  - teleport @origin
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=9;p=4600;y=0;exact=true}
  - message{m="&d[&e<mob.name>&d] &a3."} @PIR{r=100}
  - delay 20
  - message{m="&d[&e<mob.name>&d] &a2."} @PIR{r=100}
  - delay 20
  - message{m="&d[&e<mob.name>&d] &a1."} @PIR{r=100}
  - delay 20
  - skill{s=RadiusSuccessNine}
RadiusEleven:
  Conditions:
  - stance Combat
  Skills:
  - teleport @origin
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=11;p=4600;y=0;exact=true}
  - message{m="&d[&e<mob.name>&d] &a3."} @PIR{r=100}
  - delay 20
  - message{m="&d[&e<mob.name>&d] &a2."} @PIR{r=100}
  - delay 20
  - message{m="&d[&e<mob.name>&d] &a1."} @PIR{r=100}
  - delay 20
  - skill{s=RadiusSuccessEleven}
RadiusSuccessFour:
  TargetConditions:
  - distance{d=>4} orElseCast LuxThirdFailSkill
  Skills:
  - message{m="&d[&e<mob.name>&d] &aĐược đó. Tiếp tục như vậy nhoa :3"} @PIR{r=100}
RadiusSuccessSix:
  TargetConditions:
  - distance{d=>6} orElseCast LuxThirdFailSkill
  Skills:
  - message{m="&d[&e<mob.name>&d] &aĐược đó. Tiếp tục như vậy nhoa :3"} @PIR{r=100}
RadiusSuccessNine:
  TargetConditions:
  - distance{d=>9} orElseCast LuxThirdFailSkill
  Skills:
  - message{m="&d[&e<mob.name>&d] &aĐược đó. Tiếp tục như vậy nhoa :3"} @PIR{r=100}
RadiusSuccessEleven:
  TargetConditions:
  - distance{d=>11} orElseCast LuxThirdFailSkill
  Skills:
  - message{m="&d[&e<mob.name>&d] &aĐược đó. Tiếp tục như vậy nhoa :3"} @PIR{r=100}
LuxThirdFailSkill:
  Skills:
  - message{m="&d[&e<mob.name>&d] &aHoy hì mất xíu máu cho nhớ nhoa."} @PIR{r=100}
  - heal{a=25000} @self
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
AllayOnSpawn:
  Skills:
  - prison{m=barrier;d=200;b=false} @PIR{r=100}
  - message{m="&f[&9<mob.name>&f] &aNgươi dám đến thử thách ta?"} @PIR{r=100}
  - delay 60
  - message{m="&f[&9<mob.name>&f] &aĐược lắm! Chúc người toàn thây bước ra khỏi đây!"} @PIR{r=100}
  - delay 60
  - message{m="&f[&9<mob.name>&f] &aMuahahahhah!"} @PIR{r=100}
  - delay 60
  - setstance{s=Combat} @self
AllayPassive:
  Skills:
  - heal{a=1000} @self
AllayFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - shootskull{y=3;v=6;ps=true} @target
  - delay 5
  - shootskull{y=3;v=6;ps=true} @target
  - delay 5
  - shootskull{y=3;v=6;ps=true} @target
  - delay 5
AllaySecondSkill:
  Conditions:
  - stance Combat
  Skills:
  - consume{d=100;h=100} @PIR{r=10}
  - disengage @target
AllayRespawn:
  Conditions:
  - stance Combat
  Cooldowns: 300
  Skills:
  - prison{m=barrier;d=200;b=false} @PIR{r=100}
  - message{m="&f[&9<mob.name>&f] &aNgươi nghĩ ta chỉ có 1 mạng thôi sao?"} @PIR{r=100}
  - delay 60
  - message{m="&f[&9<mob.name>&f] &aHa! Còn ngây thơ lắm!"} @PIR{r=100}
  - delay 60
  - message{m="&f[&9<mob.name>&f] &aChuẩn bị đi nào!! Muaahah!"} @PIR{r=100}
  - delay 20
  - heal{a=250000} @self
AllayOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&9&l<mob.name>&f&l] &aTa đã... bị khuất phục..."} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
FarfrontOnSpawn:
  Skills:
  - prison{m=barrier;d=200;b=false} @PIR{r=100}
  - message{m="&f[&7<mob.name>&f] &8Lạc vào đây là một sai lầm rồi đó."} @PIR{r=100}
  - delay 60
  - message{m="&f[&7<mob.name>&f] &8Ra khỏi đây được cũng mừng."} @PIR{r=100}
  - delay 60
  - message{m="&f[&7<mob.name>&f] &8Thử xem"} @PIR{r=100}
  - delay 60
  - setstance{s=Combat} @self
FarfrontPassive:
  Skills:
  - damage{d=10} @PIR{r=100}
FarfrontFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - sendtitle{t="&7&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&7&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&7&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&7&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&7&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=FarfrontFirstSuccessSkill}
FarfrontFirstSuccessSkill:
  Conditions:
  - onBlock{b=OBSIDIAN} orElseCast FarfrontFirstFailSkill
  Skills:
  - sendtitle{t="&a&lSafe";d=20} 
  - effect:sound{s=entity.experience_orb.pickup;p=0.5;v=1}
  - message{m="&f&l[&7&l<mob.name>&f&l] &8Đáng mừng."}
FarfrontFirstFailSkill:
  Skills:
  - sendtitle{t="&c&lFail";d=20}
  - effect:sound{s=entity.player.death;p=2;v=1}
  - message{m="&f&l[&7&l<mob.name>&f&l] &8Đáng trách."} 
  - damagepercent{p=0.05;ia=true} @PIR{r=100}
FarfrontSecondSkill:
  Conditions:
  - stance Combat
  Skills:
  - shield{a=1000;ma=5000} @self
FarfrontOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&7&l<mob.name>&f&l] &8Mừng cho... ngươi..."} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
WardenOnSpawn:
  Skills:
  - prison{m=barrier;d=200;b=false} @PIR{r=100}
  - message{m="&f[&2<mob.name>&f] &a&kNguoi&r &a&kdam&r &a&ksao??&r"} @PIR{r=100}
  - delay 60
  - message{m="&a(Bạn nghe không hiểu vì không cùng loài.)"} @PIR{r=100}
  - delay 60
  - message{m="&a(Nhưng bạn biết hắn ta đang rất giận dữ."} @PIR{r=100}
  - delay 60
  - setstance{s=Combat} @self
WardenPassive:
  Skills:
  - shield{a=500;ma=2500} @self
WardenFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - forcepull{r=1} @PIR{r=100}
  - throw{v=5;vy=25} @PIR{r=25}
  - damagepercent{p=0.2;ia=true} @PIR{r=25}
WardenSecondSkill:
  Conditions:
  - stance Combat
  Skills:
  - effect:explosion @target
  - explosion{y=4} @target
  - ignite{t=60} @target
WardenOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&2&l<mob.name>&f&l] &a&kCon me no. May dua nay!!"} @PIR{r=100}
  - message{m="&a(Bạn nghe vẫn hong hiểu gì cả, có vẻ hắn đang rất cay cú.)"} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
EmperyOnSpawn:
  Skills:
  - prison{m=barrier;d=200;b=false} @PIR{r=100}
  - message{m="&f[&6<mob.name>&f] &eChào mừng ngươi đến với ngày tàn của mình!"} @PIR{r=100}
  - delay 60
  - message{m="&f[&6<mob.name>&f] &eKhông sao đâu. Ta sẽ là một người nhân từ."} @PIR{r=100}
  - delay 60
  - message{m="&f[&6<mob.name>&f] &eTa sẽ ban cho ngươi vĩnh hằng một cách nhẹ nhàng thôi."} @PIR{r=100}
  - delay 60
  - setstance{s=Combat} @self
EmperyPassive:
  Skills:
  - shield{a=2500;ma=25000} @self
EmperyFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&f[&6<mob.name>&f] &eTrỗi dậy!!!"} @PIR{r=5}
  - summon{m=EmperyClone;a=8;r=10;os=false;sip=true}
EmperySecondSkill:
  Conditions:
  - stance Combat
  TargetConditions:
  - distance{d=<5}
  Skills:
  - message{m="&f[&6<mob.name>&f] &eNé xa ta ra. Thứ hạ đẳng."} @PIR{r=5}
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=4;p=4600;y=0;exact=true}
  - delay 5
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=4;p=4600;y=0;exact=true}
  - delay 5
  - throw{v=20;vy=13} @PIR{r=7}
  - damagepercent{p=0.1;ia=true} @PIR{r=7}
EmperyOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&6&l<mob.name>&f&l] &eNơi chín suối.... thật đẹp...."} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
EmperyCloneCheck:
  Skills:
  - message{m="&r&f&l[&6&lBinh Sĩ&f&l] &e3."} @PIR{r=100}
  - delay 20
  - message{m="&r&f&l[&6&lBinh Sĩ&f&l] &e2."} @PIR{r=100}
  - delay 20
  - message{m="&r&f&l[&6&lBinh Sĩ&f&l] &e1."} @PIR{r=100}
  - delay 20
  - message{m="&r&f&l[&6&lBinh Sĩ&f&l] &e0."} @PIR{r=100}
  - damagepercent{p=1;ia=true} @PIR{r=100}
  - suicide @self
EsclipsesOnSpawn:
  Skills:
  - prison{m=barrier;d=200;b=false} @PIR{r=100}
  - message{m="&f[&5<mob.name>&f] &7Tối mù... Lạnh lẽo... Cô đơn..."} @PIR{r=100}
  - delay 60
  - message{m="&f[&5<mob.name>&f] &7Nơi mà ánh sáng không có bất cứ lối thoát nào."} @PIR{r=100}
  - delay 60
  - message{m="&f[&5<mob.name>&f] &7Và ngươi sẽ là một phần trong đó."} @PIR{r=100}
  - delay 60
  - setstance{s=Combat} @self
EsclipsesPassive:
  Conditions:
  - stance Combat
  Skills:
  - potion{type=BLINDNESS;lvl=4;duration=6000} @PIR{r=100}
EsclipsesFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&f[&5<mob.name>&f] &7Biến mất khỏi mắt ta đi."} @PIR{r=100}
  - sendtitle{t="&7&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&7&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&7&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&7&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&7&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=EsclipsesFirstSuccessSkill}
EsclipsesFirstSuccessSkill:
  TargetConditions:
  - lineofsight castInstead EsclipsesFirstFailSkill
  Skills:
  - message{m="&f[&5<mob.name>&f] &7Tốt lắm. Khá cho ngươi."} @PIR{r=100}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=ABSORPTION;lvl=2;duration=200}
EsclipsesFirstFailSkill:
  Skills:
  - message{m="&f[&5<mob.name>&f] &7Các ngươi cũng chỉ là một phần của bóng tối này thôi."} @PIR{r=100}
  - prison{m=barrier;d=100;b=false} @PIR{r=100}
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 10
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 10
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 10
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 10
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 10
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 10
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - delay 5
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 5
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 5
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - delay 5
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
  - message{m="&f[&5<mob.name>&f] &7Vĩnh biệt, người phàm."} @NearestPlayer{r=100}
  - teleport @NearestPlayer{r=100}
  - effect:bloodyscreen{d=40} @NearestPlayer{r=100}
  - damagepercent{p=1} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - damage{d=100000} @NearestPlayer{r=100}
  - delay 5
  - lightning{d=100000} @NearestPlayer{r=100}
  - sound{s=entity.player.hurt;v=5} @NearestPlayer{r=100}
EsclipsesSecondSkill:
  Conditions:
  - stance Combat
  Skills:
  - missile{oT=Moontrace_Tick;oH=Moontrace_Hit;v=4;i=1;hR=1.5;vR=1.5;in=2.5;ham=PLAYERS_ONLY}
Moontrace_Tick:
  Skills:
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @origin
Moontrace_Hit:
  Skills:
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=3;p=4600;y=0;exact=true} 
  - effect:sound{s=entity.shulker_bullet.hit;v=1;p=0}
  - potion{type=LEVITATION;lvl=2;duration=60} @target
  - damage{d=100} @target
EsclipsesThirdSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&r&f&l[&6&l<mob.name>&f&l] &dÁm Thi - Tử Đoạt Mệnh! Tan biến đi!"} @PIR{r=100}
  - delay 10
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=1;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=1;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2
  - damagepercent{p=0.1;ia=true} @PIR{r=1}
  - throw{v=8;vy=2} @PIR{r=1}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=2;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=2;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=2}
  - throw{v=8;vy=2} @PIR{r=2}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=3;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=3;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=3}
  - throw{v=8;vy=2} @PIR{r=3}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=4;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=4;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=4}
  - throw{v=8;vy=2} @PIR{r=4}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=5;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=5;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=5}
  - throw{v=8;vy=2} @PIR{r=5}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=6;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=6;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=6}
  - throw{v=8;vy=2} @PIR{r=6}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=7;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=7;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=7}
  - throw{v=8;vy=2} @PIR{r=7}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=8;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=8;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=8}
  - throw{v=8;vy=2} @PIR{r=8}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=9;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=9;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=9}
  - throw{v=8;vy=2} @PIR{r=9}
  - delay 5
  - effect:particles{p=reddust;c=#181718;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=10;p=100;oy=2.25}
  - effect:particles{p=largeexplosion;a=1;xs=0.1;zs=0.1;s=0.0125} @Ring{r=10;p=100;oy=2.25}
  - sound{s=entity.tnt.primed;p=0.5;v=2}
  - damagepercent{p=0.1;ia=true} @PIR{r=10}
  - throw{v=8;vy=2} @PIR{r=10}
EsclipsesOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&6&l<mob.name>&f&l] &7Cám ơn vì đã giúp ta nhìn ra ánh sáng..."} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
SeraphineOnSpawn:
  Skills:
  - prison{m=barrier;d=840;b=false} @PIR{r=100}
  - stun{d=1440;f=true} @self
  - summon{m=FakeMisterim;a=1;r=4;os=false;sip=true}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eMấy người nào cơ?"} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eHaizz. Thôi, rút lui đi Mis. Còn lại để tớ."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eMấy đứa này... Mệt thật đó."} @PIR{r=100}
  - summon{m=FakeAstella;a=1;r=4;os=false;sip=true}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eSao á. Nghe nè."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eBiết òi. Dol báo tớ rồi. Nghỉ đi Ella."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eKhông sao. Tớ lo được. Nghỉ ngơi đi. Cậu vất vả rồi."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eCó vẻ mấy người này thích kiếm chuyện phết nhờ."} @PIR{r=100}
  - summon{m=FakeDolcyllis;a=1;r=4;os=false;sip=true}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eSao nữa đây hẻ Dol? Nếu là kẹo thì tớ hết rồi nhé."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eMột đống người đang tiến về phía này chứ gì? 2 người báo tớ rồi."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eHoy. Nghỉ đi. Còn lại để tớ."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &e*cười* Ước gì mình vẫn hồn nhiên được như vậy. Tốt biết mấy."} @PIR{r=100}
  - summon{m=FakeFlorid;a=1;r=4;os=false;sip=true}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eNghe đây Bông."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eYup."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eĐó là 2 câu rồi. Sợ cậu mệt á."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eNgười như nhím thế kia mà đòi à? Nghỉ đi Bông."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eIght. Đừng lo, tất cả bọn họ sẽ tan biến hết thou."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eVẫn như ngày nào."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eMí bạn cũng ghê lắm mới dám đến đây gặp tớ đấy."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eGiờ thì... Sẵn sàng chưa?"} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eCùng trình diễn thôi nhỉ."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eNói trước. Tớ sẽ không giữ mình đâu."} @PIR{r=100}
  - delay 40
  - message{m="&f[&d<mob.name>&f] &eChiến thôi."} @PIR{r=100}
  - delay 40
  - setstance{s=OnStage} @self
FMisterimSpawn:
  Skills:
  - delay 5
  - stun{d=1000;f=true} @self
  - delay 5
  - message{m="&f[&c<mob.name>&f] &bSera! Sera! Mấy người kia đang kiếm tới bạn kìa."} @PIR{r=100}
  - delay 40
  - message{m="&f[&c<mob.name>&f] &bKhông biết. Nhưng mà, họ đánh bại được tớ rồi."} @PIR{r=100}
  - delay 40
  - message{m="&f[&c<mob.name>&f] &bOk. Chúc may mắn. Tớ tin cậu đó."} @PIR{r=100}
  - suicide @self
FAstellaSpawn:
  Skills:
  - delay 5
  - stun{d=1000;f=true} @self
  - delay 5
  - message{m="&f[&b<mob.name>&f] &dAluuu! Seraaaa. Có chuyện gấp nèeeee."} @PIR{r=100}
  - delay 40
  - message{m="&f[&b<mob.name>&f] &dMột đống người đang kéo về phía này ấyyyyy."} @PIR{r=100}
  - delay 40
  - message{m="&f[&b<mob.name>&f] &dHong shao. Tớ còn giúp được mà."} @PIR{r=100}
  - delay 40
  - message{m="&f[&b<mob.name>&f] &dUghhh... Fine...."} @PIR{r=100}
  - suicide @self
FDolcyllisSpawn:
  Skills:
  - delay 5
  - stun{d=1000;f=true} @self
  - delay 5
  - message{m="&r&f[&d<mob.name>&f] &aAaaaaaa. Hẹp miiiii!!!"} @PIR{r=100}
  - delay 40
  - message{m="&r&f[&d<mob.name>&f] &aHong!! Có một đống ng...."} @PIR{r=100}
  - delay 40
  - message{m="&r&f[&d<mob.name>&f] &aA! Yeee! Tớ... sợ... Họ hạ được cả tớ nữa."} @PIR{r=100}
  - delay 40
  - message{m="&r&f[&d<mob.name>&f] &aYayyy! Merciii!!"} @PIR{r=100}
  - suicide @self
FFloridSpawn:
  Skills:
  - delay 5
  - stun{d=1000;f=false} @self
  - delay 5
  - message{m="&f[&a<mob.name>&f] &bSeraphine."} @PIR{r=100}
  - delay 40
  - message{m="&f[&a<mob.name>&f] &bChắc 3 đứa trước nó nói cậu rồi nhỉ?"} @PIR{r=100}
  - delay 40
  - message{m="&f[&a<mob.name>&f] &b1 câu thôi. Để tớ phụ cậu."} @PIR{r=100}
  - delay 40
  - message{m="&f[&a<mob.name>&f] &bTớ còn sung sức lắm. Để tớ."} @PIR{r=100}
  - delay 40
  - message{m="&f[&a<mob.name>&f] &b.... Ok. Cẩn thận nhé."} @PIR{r=100}
  - delay 40
  - message{m="&f[&a<mob.name>&f] &bMay mắn. Và bình an nhé."} @PIR{r=100}
  - suicide @self
OnStagePassive:
  Conditions:
  - stance OnStage
  Skills:
  - stun{d=20;f=true} @self
  - lightning{d=100} @PIR{r=100}
  - heal{a=25000} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @self
OnStageLengthenPas:
  Conditions:
  - stance OnStage
  Skills:
  - message{m="&f[&d<mob.name>&f] &eTới lượt bạn nè."} @PIR{r=100}
  - damage{d=100;ia=true} @PIR{r=100}
CelebratePassive:
  Conditions:
  - stance Celebrate
  Skills:
  - message{m="&f[&d<mob.name>&f] &eNgồi xuống và lắng nghe nhé."} @PIR{r=4}
  - stun{d=10;f=false} @PIR{r=4}
CelebrateLengthenPas:
  Conditions:
  - stance Celebrate
  Skills:
  - effect:explosion @PIR{r=100}
  - explosion{y=5} @PIR{r=100}
PassiveConversion:
  Cooldown: 120
  Conditions:
  - stance OnStage
  Skills:
  - delay 1200
  - setstance{s=Celebrate} @self
  - delay 1200
  - setstance{s=OnStage} @self
OSLastCall:
  Conditions:
  - stance OnStage
  - health{h=<1000000}
  Skills:
  - setstance{s=Danger} @self
  - stun{d=400;f=true} @self
  - message{m="&f[&d<mob.name>&f] &eHết hơi rồi... Từ từ được hong..."} @PIR{r=100}
  - potionclear @self
  - potion{type=INCREASE_DAMAGE;lvl=8;duration=400} @PIR{r=100}
  - message{m="&f[&d<mob.name>&f] &e20."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e19."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e18."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e17."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e16."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e15."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e14."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e13."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e12."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e11."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e10."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e9."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e8."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e7."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e6."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e5."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e4."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e0."} @PIR{r=100}
  - message{m="&f[&d<mob.name>&f] &eKhán giả khó tỉnh nhể? Đổi giai điệu thôi nào!"} @PIR{r=100}
  - delay 3
  - stun{d=300;f=false} @PIR{r=100}
  - delay 3
  - healpercent{m=1} @self
  - delay 3
  - heal{a=50000000} @self
  - delay 3
  - setstance{s=OnStage} @self
CLastCall:
  Conditions:
  - stance Celebrate
  - health{h=<1000000}
  Skills:
  - setstance{s=Danger} @self
  - teleport @CasterSpawnLocation
  - stun{d=400;f=true} @self
  - message{m="&f[&d<mob.name>&f] &eHết hơi rồi... Từ từ được hong..."} @PIR{r=100}
  - potionclear @self
  - potion{type=INCREASE_DAMAGE;lvl=8;duration=400} @PIR{r=100}
  - message{m="&f[&d<mob.name>&f] &e20."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e19."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e18."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e17."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e16."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e15."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e14."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e13."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e12."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e11."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e10."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e9."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e8."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e7."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e6."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e5."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e4."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e0."} @PIR{r=100}
  - message{m="&f[&d<mob.name>&f] &eKhán giả khó tỉnh nhể? Đổi giai điệu thôi nào!"} @PIR{r=100}
  - delay 3
  - stun{d=300;f=false} @PIR{r=100}
  - delay 3
  - healpercent{m=1} @self
  - delay 3
  - heal{a=50000000} @self
  - delay 3
  - setstance{s=OnStage} @self
HighNote:
  Conditions:
  - stance OnStage
  Skills:
  - teleport @CasterSpawnLocation
  - randomskill{s=BranchOne,BranchTwo,BranchThree,BranchFour}
BranchOne:
  Skills:
  - randomskill{s=ROne,RTwo,RThree,RFour,RFive,RSeven,RNine}
BranchTwo:
  Skills:
  - randomskill{s=RTen,REleven,RTwelve,RThirteen,RFourteen,RFifteen,RSixteen,RSeventeen,REighteen}
BranchThree:
  Skills:
  - randomskill{s=RNineteen,RTwenty,RTwentyOne,RTwentyTwo,RTwentyThree,RTwentyFour,RTwentyFive,RTwentySix,RTwentySeven,RTwentyEight}
BranchFour:
  Skills:
  - randomskill{s=RTwentyNine,RThirty,RThirtyOne,RThirtyTwo,RThirtyThree,RThirtyFour,REight}
ROne:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCúi đi nào mọi người. Chào tớ nè."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=ROneSuccess}
ROneSuccess:
  TargetConditions:
  - crouching orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCám ơn. Để show bắt đầu nào!"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwo:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eÚi! Cà chua kìa! Chặn đi! Hong ăn đạn đó!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwoSuccess}
RTwoSuccess:
  TargetConditions:
  - blocking orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCó bị dính hong? Có sao hong?"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RThree:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eNúp đi. Mưa tới ròi kìa."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RThreeSuccess}
RThreeSuccess:
  TargetConditions:
  - inside orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eTạnh òi. Ra chơi nèo."} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RFour:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCổ vũ mạnh lên nào. C'mon. Nhảy đi nàooo. "} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RFourSuccess}
RFourSuccess:
  TargetConditions:
  - heightabove{h=} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eYeaaa! Phải vậy chứ!! Woooo!!"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RFive:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eÍ! Đừng có nhìn! Xấu hổ lắm >.<"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RFiveSuccess}
RFiveSuccess:
  TargetConditions:
  - lineofsight castInstead HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCám ơn vì đã hong nhìn :3."} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RSix:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCần thêm &d5 &engười trong ver &d4 &enè. Đứng sát dô."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RSixSuccess}
RSixSuccess:
  TargetConditions:
  - playerinradius{a=5;r=4} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RSeven:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eChạy đi nèo. Đừng đứng im nựa!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RSevenSuccess}
RSevenSuccess:
  TargetConditions:
  - sprinting orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eYeeee. Tinh thần phải dậy đó!"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
REight:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCần thêm &d3 &engười trong ver &d7 &enè. Đứng sát dô."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=REightSuccess}
REightSuccess:
  TargetConditions:
  - playerinradius{a=3;r=7} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RNine:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Đói bụng kìa. Ăn cái gì đỡ đi."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RNineSuccess}
RNineSuccess:
  TargetConditions:
  - foodlevel{a=>19.5} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eĂn gì dạ? Ké miếng iii :3"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTen:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eNhảy đi nèo! Đứng trên sàn &dBedrock &enha."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTenSuccess}
RTenSuccess:
  TargetConditions:
  - onBlock{b=BEDROCK} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eBiết nhảy ròi đóa."} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
REleven:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eNhảy đi nèo! Đứng trên sàn &dDiamond &enha."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RElevenSuccess}
RElevenSuccess:
  TargetConditions:
  - onBlock{b=DIAMOND_BLOCK} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eBiết nhảy ròi đóa."} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwelve:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eNhảy đi nèo! Đứng trên sàn &dEmerald &enha."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwelveSuccess}
RTwelveSuccess:
  TargetConditions:
  - onBlock{b=EMERALD_BLOCK} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eBiết nhảy ròi đóa."} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RThirteen:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eNhảy đi nèo! Đứng trên sàn &dGold &enha."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RThirteenSuccess}
RThirteenSuccess:
  TargetConditions:
  - onBlock{b=GOLD_BLOCK} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eBiết nhảy ròi đóa."} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RFourteen:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eNhảy đi nèo! Đứng trên sàn &dIron &enha."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RFourteenSuccess}
RFourteenSuccess:
  TargetConditions:
  - onBlock{b=IRON_BLOCK} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eBiết nhảy ròi đóa."} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RFifteen:
  Skills:
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RFifteenSuccess}
RFifteenSuccess:
  TargetConditions:
  - onBlock{b=RED_NETHER_BRICK} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eBiết nhảy ròi đóa."} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RSixteen:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dLa Bàn &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RSixteenSuccess}
RSixteenSuccess:
  TargetConditions:
  - holding{m=COMPASS} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RSeventeen:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dĐồng Hồ &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RSeventeenSuccess}
RSeventeenSuccess:
  TargetConditions:
  - holding{m=WATCH} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
REighteen:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dLa Bàn &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=REighteenSuccess}
REighteenSuccess:
  TargetConditions:
  - holding{m=COMPASS} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RNineteen:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dCần Câu &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RSixteenSuccess}
RNineteenSuccess:
  TargetConditions:
  - holding{m=FISHING_ROD} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwenty:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dKiếm Sắt &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentySuccess}
RTwentySuccess:
  TargetConditions:
  - holding{m=IRON_SWORD} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentyOne:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dXẻng Gỗ &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentyOneSuccess}
RTwentyOneSuccess:
  TargetConditions:
  - holding{m=WOOD_SPADE} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentyTwo:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dRìu Vàng &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentyTwoSuccess}
RTwentyTwoSuccess:
  TargetConditions:
  - holding{m=GOLD_AXE} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentyThree:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dCuốc Kim Cương &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentyThreeSuccess}
RTwentyThreeSuccess:
  TargetConditions:
  - holding{m=DIAMOND_HOE} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentyFour:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eGiơ tay lên nào! Cầm &dCúp Đá &enha!"} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentyFourSuccess}
RTwentyFourSuccess:
  TargetConditions:
  - holding{m=STONE_PICKAXE} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentyFive:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eLên đồ đi. &dGiày Da &enhe."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentyFiveSuccess}
RTwentyFiveSuccess:
  TargetConditions:
  - wearing{s=FEET;m=LEATHER_BOOTS} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentySix:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eLên đồ đi. &dMũ Kim Cương &enhe."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentySixSuccess}
RTwentySixSuccess:
  TargetConditions:
  - wearing{s=HEAD;m=DIAMOND_HELMET} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentySeven:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eLên đồ đi. &dÁo Vàng &enhe."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentySevenSuccess}
RTwentySevenSuccess:
  TargetConditions:
  - wearing{s=CHEST;m=GOLD_CHESTPLATE} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentyEight:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eLên đồ đi. &dQuần Sắt &enhe."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentyEightSuccess}
RTwentyEightSuccess:
  TargetConditions:
  - wearing{s=LEGS;m=IRON_LEGGINGS} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RTwentyNine:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCần thêm &d1 &engười trong ver &d2 &enè. Đứng sát dô."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RTwentyNineSuccess}
RTwentyNineSuccess:
  TargetConditions:
  - playerinradius{a=1;r=2} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eOaaaa. Cám ơn :3"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RThirty:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCần thêm &d2 &engười trong ver &d6 &enè. Đứng sát dô."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RThirtySuccess}
RThirtySuccess:
  TargetConditions:
  - playerinradius{a=2;r=6} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eOaaaa. Cám ơn :3"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RThirtyOne:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCần thêm &d3 &engười trong ver &d4 &enè. Đứng sát dô."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RThirtyOneSuccess}
RThirtyOneSuccess:
  TargetConditions:
  - playerinradius{a=3;r=4} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eOaaaa. Cám ơn :3"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RThirtyTwo:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCần thêm &d4 &engười trong ver &d10 &enè. Đứng sát dô."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RThirtyTwoSuccess}
RThirtyTwoSuccess:
  TargetConditions:
  - playerinradius{a=4;r=10} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eOaaaa. Cám ơn :3"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RThirtyThree:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCần thêm &d5 &engười trong ver &d5 &enè. Đứng sát dô."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RThirtyThreeSuccess}
RThirtyThreeSuccess:
  TargetConditions:
  - playerinradius{a=5;r=5} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eOaaaa. Cám ơn :3"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
RThirtyFour:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCần thêm &d6 &engười trong ver &d3 &enè. Đứng sát dô."} @PIR{r=100}
  - sendtitle{t="&e&l5";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l4";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l2";d=20} @PIR{r=100}
  - delay 20
  - sendtitle{t="&e&l1";d=20} @PIR{r=100}
  - delay 20
  - skill{s=RThirtyFourSuccess}
RThirtyFourSuccess:
  TargetConditions:
  - playerinradius{a=6;r=3} orElseCast HigherNote
  Skills:
  - message{m="&f[&d<mob.name>&f] &eẦy. Có thật à :>"} @PIR{r=100}
  - damage{a=500000;pkb=true;ii=true} @self
HigherNote:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eHoy hì... Tạm biệt."} @PIR{r=100}
  - lightning{d=100000}
  - delay 5
  - lightning{d=100000}
  - delay 5
  - lightning{d=100000}
  - delay 5
SurroundSound:
  Conditions:
  - stance OnStage
  Skills:
  - randomskill{s=ProtectiveSound,DestructiveSound}
ProtectiveSound:
  Skills:
  - randomskill{s=ProtectiveNine,ProtectiveSeven,ProtectiveSix,ProtectiveFour,ProtectiveTwo}
DestructiveSound:
  Skills:
  - randomskill{s=DestructiveThree,DestructiveFive,DestructiveEight,DestructiveTen,DestructiveFourteen}
ProtectiveNine:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - throw{v=19;vy=8} @PIR{r=10}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=9;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e5."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=9;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e4."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=9;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=9;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=9;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=ProtectiveNineSuccess}
ProtectiveNineSuccess:
  TargetConditions:
  - distance{d=<9} orElseCast ProtectiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &eYe ye. An toàn chứ?"} @PIR{r=100}
ProtectiveSeven:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - throw{v=19;vy=8} @PIR{r=10}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=7;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e5."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=7;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e4."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=7;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=7;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=7;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=ProtectiveSevenSuccess}
ProtectiveSevenSuccess:
  TargetConditions:
  - distance{d=<7} orElseCast ProtectiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &eYe ye. An toàn chứ?"} @PIR{r=100}
ProtectiveSix:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - throw{v=19;vy=8} @PIR{r=10}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=6;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e5."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=6;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e4."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=6;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=6;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=6;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=ProtectiveSixSuccess}
ProtectiveSixSuccess:
  TargetConditions:
  - distance{d=<6} orElseCast ProtectiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &eYe ye. An toàn chứ?"} @PIR{r=100}
ProtectiveFour:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - throw{v=19;vy=8} @PIR{r=10}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=4;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e5."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=4;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e4."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=4;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=4;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=4;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=ProtectiveFourSuccess}
ProtectiveFourSuccess:
  TargetConditions:
  - distance{d=<4} orElseCast ProtectiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &eYe ye. An toàn chứ?"} @PIR{r=100}
ProtectiveTwo:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - throw{v=19;vy=8} @PIR{r=10}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=2;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e5."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=2;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e4."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=2;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=2;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125} @Sphere{r=2;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=ProtectiveTwoSuccess}
ProtectiveTwoSuccess:
  TargetConditions:
  - distance{d=<2} orElseCast ProtectiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &eYe ye. An toàn chứ?"} @PIR{r=100}
ProtectiveSoundFail:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eHoy hong kịp òi. Lần sau nha."} @PIR{r=100}
  - heal{a=25000} @self
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
DestructiveThree:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=3;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=DestructiveThreeSuccess}
DestructiveFive:
  Skills:
  - teleport @CasterSpawnLocation 
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=5;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=DestructiveFiveSuccess}
DestructiveEight:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=8;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=DestructiveEightSuccess}
DestructiveTen:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=10;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=DestructiveTenSuccess}
DestructiveFourteen:
  Skills:
  - teleport @CasterSpawnLocation
  - forcepull{s=2} @PIR{r=100}
  - stun{d=80;f=true;kb=true} @self
  - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=14;p=4600;y=0;exact=true}
  - message{m="&f[&d<mob.name>&f] &e3."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e2."} @PIR{r=100}
  - delay 20
  - message{m="&f[&d<mob.name>&f] &e1."} @PIR{r=100}
  - delay 20
  - skill{s=DestructiveFourteenSuccess}
DestructiveThreeSuccess:
  TargetConditions:
  - distance{d=>3} orElseCast DestructiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &ePhew. Cám ơn vì không ở quá gần nha."} @PIR{r=100}
DestructiveFiveSuccess:
  TargetConditions:
  - distance{d=>5} orElseCast DestructiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &ePhew. Cám ơn vì không ở quá gần nha."} @PIR{r=100}
DestructiveEightSuccess:
  TargetConditions:
  - distance{d=>8} orElseCast DestructiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &ePhew. Cám ơn vì không ở quá gần nha."} @PIR{r=100}
DestructiveTenSuccess:
  TargetConditions:
  - distance{d=>10} orElseCast DestructiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &ePhew. Cám ơn vì không ở quá gần nha."} @PIR{r=100}
DestructiveFourteenSuccess:
  TargetConditions:
  - distance{d=>14} orElseCast DestructiveSoundFail
  Skills:
  - message{m="&f[&d<mob.name>&f] &ePhew. Cám ơn vì không ở quá gần nha."} @PIR{r=100}
DestructiveSoundFail:
  Skills:
  - message{m="&f[&d<mob.name>&f] &eAaaa. Làm gì dậy >:l"} @PIR{r=100}
  - heal{a=25000} @self
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
BeatDrop:
  Conditions:
  - stance Celebrate
  Skills:
  - message{m="&f[&d<mob.name>&f] &eCùng nhau nhảy thôi nhỉ?"} @PIR{r=100}
  - aura{auraname=Dropping;os=FirstDrop;ot=ContinousDrop;oe=EndDrop;i=5;d=120}
FirstDrop:
  Skills:
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self
  - damage{a=250} @target
  - delay 20
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self
  - damage{a=250} @PIR{r=5}
  - delay 10
  - effect:particles{particle=reddust;color=#7D26CD;amount=200;hS=0;vS=1.5;speed=0;dir=0,0.5,2} @self
  - damage{a=240;ia=true} @PIR{r=5}
ContinousDrop:
  Skills:
  - damage{a=90} @target 
  - damage{a=40;ia=true} @target
  - teleport @target
EndDrop:
  Skills:
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
  - damage{a=330} @target
  - throw{velocity=18;velocityY=5} @target
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
  - damage{a=330} @target
  - throw{velocity=18;velocityY=5} @target
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
  - damage{a=330} @target
  - throw{velocity=18;velocityY=5} @target  
  - teleport @target
  - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
  - damage{a=330} @target
  - throw{velocity=18;velocityY=5} @target
Encore:
  Conditions:
  - stance Celebrate
  Cooldown: 60
  Skills:
  - summon{m=SeraphineFan;a=4;r=5;os=false;sip=true}
SeraphineOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&d&l<mob.name>&f&l] &eGiai điệu của cậu... hay lắm..."} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
SeraphinePlayerKill:
  Skills:
  - healpercent{m=0.05} @self
  - modifytargetscore{obj=KillCount;action=ADD;v=1} @self ~onPlayerKill
  - potion{type=HARM;lvl=10;duration=20} @PIR{r=100}
  - potion{type=HUNGER;lvl=3;duration=6000} @PIR{r=100}
  - potion{type=POISON;lvl=5;duration=1000} @PIR{r=100}
  - potion{type=WITHER;lvl=5;duration=1000} @PIR{r=100}
  - message{m="&f[&cCảnh báo&f] &3Cường hóa của &a&l<mob.name> &3đã đạt bậc &d<mob.score.KillCount>&f."} @PIR{r=100}
SeraphineFanCheck:
  Skills:
  - message{m="&f[&d<mob.name>&f] &e90."} @PIR{r=100}
  - delay 200
  - message{m="&f[&d<mob.name>&f] &e60."} @PIR{r=100}
  - message{m="&f[&d<mob.name>&f] &eOaaaaa. &dSeraphine &egiỏi quá điii."} @PIR{r=100}
  - delay 200
  - message{m="&f[&d<mob.name>&f] &e30."} @PIR{r=100}
  - delay 200
  - message{m="&f[&d<mob.name>&f] &e0."} @PIR{r=100}
  - message{m="&f[&d<mob.name>&f] &eBái bai. Tớ yêu tất cả mọi người."} @PIR{r=100}
  - damagepercent{p=1;ia=true} @PIR{r=100}
  - delay 2
  - damagepercent{p=1;ia=true} @PIR{r=100}
  - delay 2
  - damagepercent{p=1;ia=true} @PIR{r=100}
  - delay 2
  - damagepercent{p=1;ia=true} @PIR{r=100}
  - delay 2
  - damagepercent{p=1;ia=true} @PIR{r=100}
  - delay 2
  - lightning{d=10000}
  - delay 2
  - lightning{d=10000}
  - delay 2
  - lightning{d=10000}
  - delay 2
  - lightning{d=10000}
  - delay 2
  - lightning{d=10000}
  - delay 2
  - lightning{d=10000}
  - delay 2
  - suicide @self

CaressingMoonlight:
  Cooldown: 5
  Skills:
  - hide{d=100} @server 
  - delay 100
  - teleport @target
  - damage{amount=100;ignorearmor=true} @target 1
  - potion{type=HARM;lvl=10;duration=100} @target 1
SilentAndSharpMockery:
  Cooldown: 6
  Skills:
    - delay 60
    - explosion{yield=4} @PlayersỈnRadius{r=100} 1
    - damage{amount=100;ignorearmor=true} @PlayersInRadius{r=100]
    - potion{type=HARM;lvl=10;duration=100} @PlayersInTarget{r=100} 
    - delay 45
    - spin{d=100,v=20}@PlayersInRadius{r=100}
    - knockup
    - swap @target
SpiritWhisper:
  Cooldown: 7
  Skills:
  - delay 20
  - damage{amount=50;ignorearmor=true} @PlayersInRadius{r=100} 
  - potion{type=BLINDNESS;lvl=10;duration=100} @PlayersInTarget{r=100} 
  - potion{type=WEAK;lvl=10;duration=100} @PlayersInTarget{r=100} 
  - potion{type=WITHER;lvl=10;duration=100} @PlayersInTarget{r=100} 
  - potion{type=SLOW;lvl=10;duration=100} @PlayersInTarget{r=100} 
  - prison{material=GLASS;duration=200;breakable=false} @PlayersInRadius{r=100} 
  - stun{d=100,f=false}
  - lightning{d=200} @PlayersInRadius{r=100} 1
  - effect:particles{p=portal;amount=1000;hSpread=15;ySpread=1;speed=0;yOFfset=0.5} @self
SeethingWhisperOfTheFallen:
  Cooldown: 8
  Skills:
  - equip{item=iron_horse_armor:hand} @self
  - equip{item=iron_horse_armor:offhand} @self
  - delay 60
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - delay 10
  - arrowvolley{a=8;s=1;v=10;f=50;rd=200} @Target
  - damage{amount=30;ignorearmor=true} @target 1
  - potion{type=SLOW;lvl=10;duration=200}
KafkaSpecial:
  Skills:
  - damagepercent{percent=0.12} @PlayersInRadius{r=100}
  - consume{d=10000;h=65000} @PlayersInRadius{r=100} 
  