package soulmc.skymagic.Event;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.block.Action;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import soulmc.skymagic.utils.PerformanceMonitor;

import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.EnumSet;

public class BlockBreakListener implements Listener {
    
    private final Plugin plugin;
    private final PerformanceMonitor performanceMonitor;

    // Thread-safe maps cho server đông người
    private final Map<UUID, Long> lastBreakTime = new ConcurrentHashMap<>();
    private final Map<UUID, Location> lastBreakLocation = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> consecutiveBreaks = new ConcurrentHashMap<>();

    // Cache ore types để tối ưu performance
    private static final Set<Material> ORE_TYPES = EnumSet.of(
        Material.COAL_ORE, Material.IRON_ORE, Material.GOLD_ORE,
        Material.DIAMOND_ORE, Material.EMERALD_ORE, Material.LAPIS_ORE,
        Material.REDSTONE_ORE, Material.COPPER_ORE,
        Material.DEEPSLATE_COAL_ORE, Material.DEEPSLATE_IRON_ORE,
        Material.DEEPSLATE_GOLD_ORE, Material.DEEPSLATE_DIAMOND_ORE,
        Material.DEEPSLATE_EMERALD_ORE, Material.DEEPSLATE_LAPIS_ORE,
        Material.DEEPSLATE_REDSTONE_ORE, Material.DEEPSLATE_COPPER_ORE,
        Material.NETHER_GOLD_ORE, Material.NETHER_QUARTZ_ORE,
        Material.ANCIENT_DEBRIS
    );
    
    // Các giá trị sẽ được load từ config
    private double sameAreaRadius;
    private boolean debugMode;
    private boolean notifyPlayer;

    // Performance settings
    private long cleanupInterval;
    private long cleanupThreshold;
    
    public BlockBreakListener(Plugin plugin) {
        this.plugin = plugin;
        this.performanceMonitor = new PerformanceMonitor(plugin);
        loadConfig();
        startCleanupTask();
    }

    private void loadConfig() {
        // Load config values - chỉ load những gì cần thiết
        this.sameAreaRadius = plugin.getConfig().getDouble("block-breaking.same-area-radius", 10.0);
        this.debugMode = plugin.getConfig().getBoolean("general.debug", false);
        this.notifyPlayer = plugin.getConfig().getBoolean("general.notify-player", true);

        // Load performance settings
        this.cleanupInterval = plugin.getConfig().getLong("performance.cleanup-interval", 60) * 20L; // Convert to ticks
        this.cleanupThreshold = plugin.getConfig().getLong("performance.cleanup-threshold", 60) * 1000L; // Convert to ms
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onBlockBreak(BlockBreakEvent event) {
        performanceMonitor.incrementEventsHandled();

        Player player = event.getPlayer();
        Block block = event.getBlock();
        UUID playerId = player.getUniqueId();
        Location blockLocation = block.getLocation();
        long currentTime = System.currentTimeMillis();

        // Chỉ track ore blocks, KHÔNG can thiệp vào quá trình mining bình thường
        if (isOreBlock(block.getType())) {
            performanceMonitor.incrementBlocksProcessed();

            // Cập nhật tracking data để monitor
            lastBreakTime.put(playerId, currentTime);
            lastBreakLocation.put(playerId, blockLocation.clone());

            // Reset consecutive counter nếu đào ở khu vực khác
            Location lastLocation = lastBreakLocation.get(playerId);
            if (lastLocation == null || !isSameArea(blockLocation, lastLocation)) {
                consecutiveBreaks.put(playerId, 0);
            } else {
                consecutiveBreaks.put(playerId, consecutiveBreaks.getOrDefault(playerId, 0) + 1);
            }

            // CHỈ track - để Minecraft xử lý mining bình thường
            // Monitor event sẽ check nếu có vấn đề
        }
    }

    // Monitor để phát hiện blocks thực sự bị stuck
    @EventHandler(priority = EventPriority.MONITOR)
    public void onBlockBreakMonitor(BlockBreakEvent event) {
        // CHỈ monitor events mà player có quyền break
        if (event.isCancelled()) return;

        Player player = event.getPlayer();
        Block block = event.getBlock();

        if (isOreBlock(block.getType())) {
            // Check sau 2 ticks để đảm bảo event đã được xử lý hoàn toàn
            new BukkitRunnable() {
                @Override
                public void run() {
                    // CHỈ can thiệp nếu block THỰC SỰ vẫn còn (stuck)
                    if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                        if (debugMode) {
                            plugin.getLogger().warning("Detected REAL stuck block at " +
                                block.getLocation() + " for player " + player.getName());
                        }

                        // forceBreakAndSync sẽ tự check permissions
                        forceBreakAndSync(player, block);
                        performanceMonitor.incrementForceBreaks();
                    }
                }
            }.runTaskLater(plugin, 2L); // Check sau 2 ticks để chắc chắn
        }
    }
    
    private boolean isOreBlock(Material material) {
        // O(1) lookup thay vì 19 comparisons
        return ORE_TYPES.contains(material);
    }
    
    private boolean isSameArea(Location loc1, Location loc2) {
        if (!loc1.getWorld().equals(loc2.getWorld())) {
            return false;
        }

        // Tối ưu: sử dụng distanceSquared để tránh sqrt calculation
        double radiusSquared = sameAreaRadius * sameAreaRadius;
        return loc1.distanceSquared(loc2) <= radiusSquared;
    }
    

    


    // Method để force break - CHỈ khi player có quyền break
    private void forceBreakAndSync(Player player, Block block) {
        if (block.getType() == Material.AIR || !isOreBlock(block.getType())) {
            return;
        }

        // KIỂM TRA QUYỀN: Tạo fake BlockBreakEvent để test permissions
        BlockBreakEvent testEvent = new BlockBreakEvent(block, player);
        plugin.getServer().getPluginManager().callEvent(testEvent);

        // CHỈ break nếu event KHÔNG bị cancel bởi protection plugins
        if (!testEvent.isCancelled()) {
            // Lưu block type trước khi break
            Material originalType = block.getType();

            // An toàn: Break block với proper permission check
            block.breakNaturally(player.getInventory().getItemInMainHand());

            // Thông báo player nếu được bật
            if (notifyPlayer) {
                player.sendMessage("§aĐã sửa block kẹt!");
            }

            if (debugMode) {
                plugin.getLogger().info("Force broke stuck " + originalType + " at " +
                    block.getLocation() + " for player " + player.getName());
            }
        } else {
            // Player không có quyền - chỉ sync client để fix visual desync
            player.sendBlockChange(block.getLocation(), block.getBlockData());

            if (debugMode) {
                plugin.getLogger().info("Cannot force break " + block.getType() + " at " +
                    block.getLocation() + " - player " + player.getName() + " has no permission");
            }

            if (notifyPlayer) {
                player.sendMessage("§cKhông thể sửa block kẹt - bạn không có quyền đào ở đây!");
            }
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        cleanupPlayerData(event.getPlayer().getUniqueId());
    }

    // Reload config
    public void reloadConfig() {
        loadConfig();
    }

    // Periodic cleanup task để tránh memory leaks
    private void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                long currentTime = System.currentTimeMillis();

                // Cleanup old entries using configured threshold
                lastBreakTime.entrySet().removeIf(entry ->
                    currentTime - entry.getValue() > cleanupThreshold);

                // Cleanup corresponding entries
                lastBreakTime.keySet().retainAll(lastBreakTime.keySet());
                lastBreakLocation.keySet().retainAll(lastBreakTime.keySet());
                consecutiveBreaks.keySet().retainAll(lastBreakTime.keySet());

                if (debugMode) {
                    plugin.getLogger().info("Cleaned up old player data. Active players: " + lastBreakTime.size());
                }
            }
        }.runTaskTimerAsynchronously(plugin, cleanupInterval, cleanupInterval);
    }

    // Listener để detect stuck mining animation (với permission check)
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.LEFT_CLICK_BLOCK) return;
        if (event.getClickedBlock() == null) return;

        Block block = event.getClickedBlock();
        Player player = event.getPlayer();

        if (isOreBlock(block.getType())) {
            // CHỉ schedule check nếu player đang hold mining tool
            if (player.getInventory().getItemInMainHand().getType().name().contains("PICKAXE") ||
                player.getInventory().getItemInMainHand().getType().name().contains("SHOVEL")) {

                // Check ít aggressive hơn - chỉ sau 5 giây
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        // CHỈ can thiệp nếu block vẫn còn VÀ player vẫn đang target nó
                        if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                            Block targetBlock = player.getTargetBlockExact(6);

                            if (targetBlock != null && targetBlock.equals(block)) {
                                if (debugMode) {
                                    plugin.getLogger().warning("Detected long-term stuck mining for " +
                                        player.getName() + " at " + block.getLocation());
                                }

                                // forceBreakAndSync sẽ double-check permissions
                                forceBreakAndSync(player, block);
                                performanceMonitor.incrementForceBreaks();
                            }
                        }
                    }
                }.runTaskLater(plugin, 100L); // Check sau 5 giây (100 ticks)
            }
        }
    }

    // Cleanup data khi player logout
    public void cleanupPlayerData(UUID playerId) {
        lastBreakTime.remove(playerId);
        lastBreakLocation.remove(playerId);
        consecutiveBreaks.remove(playerId);
    }
}
