package me.hsgamer.extrastorage.commands.subs.player;

import me.hsgamer.extrastorage.api.item.Item;
import me.hsgamer.extrastorage.api.storage.Storage;
import me.hsgamer.extrastorage.api.user.User;
import me.hsgamer.extrastorage.commands.abstraction.Command;
import me.hsgamer.extrastorage.commands.abstraction.CommandContext;
import me.hsgamer.extrastorage.commands.abstraction.CommandListener;
import me.hsgamer.extrastorage.commands.abstraction.CommandTarget;
import me.hsgamer.extrastorage.configs.Message;
import me.hsgamer.extrastorage.configs.Setting;
import me.hsgamer.extrastorage.data.Constants;
import me.hsgamer.extrastorage.util.Digital;
import me.hsgamer.extrastorage.util.Utils;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Command(value = "send", usage = "/{label} send <material-key|all> [amount|all] <player>", permission = Constants.PLAYER_SEND_PERMISSION, target = CommandTarget.ONLY_PLAYER)
public final class SendCmd extends CommandListener {

    private final Setting setting;
    private final String QUANTITY_REGEX, ITEM_REGEX, PLAYER_REGEX;

    public SendCmd() {
        this.setting = instance.getSetting();
        this.QUANTITY_REGEX = Utils.getRegex("quantity");
        this.ITEM_REGEX = Utils.getRegex("item");
        this.PLAYER_REGEX = Utils.getRegex("player");
    }

    @Override
    public void execute(CommandContext context) {
        Player sender = context.castToPlayer();
        User senderUser = instance.getUserManager().getUser(sender);
        if (senderUser == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found"));
            return;
        }
        Storage senderStorage = senderUser.getStorage();

        String materialKey = context.getArgs(0);
        String amountStr = context.getArgs(1);
        String targetName;

        if (materialKey.equalsIgnoreCase("all")) {
            if (context.getArgsLength() < 2) {
                context.sendMessage(Message.getMessage("FAIL.must-enter-player"));
                return;
            }
            targetName = context.getArgs(1);
            sendAll(sender, senderUser, senderStorage, targetName, context);
            return;
        }

        if (context.getArgsLength() < 3) {
            context.sendMessage(Message.getMessage("FAIL.must-enter-player"));
            return;
        }
        targetName = context.getArgs(2);

        if (amountStr.equalsIgnoreCase("all")) {
            sendMaterialAll(sender, senderUser, senderStorage, materialKey, targetName, context);
            return;
        }

        int amount;
        try {
            amount = Digital.getBetween(1, Integer.MAX_VALUE, Integer.parseInt(amountStr));
        } catch (NumberFormatException ignored) {
            context.sendMessage(Message.getMessage("FAIL.not-number").replaceAll(Utils.getRegex("value"), amountStr));
            return;
        }

        sendSpecificAmount(sender, senderUser, senderStorage, materialKey, amount, targetName, context);
    }

    private void sendSpecificAmount(Player sender, User senderUser, Storage senderStorage, String materialKey, int amount, String targetName, CommandContext context) {
        Optional<Item> optionalItem = senderStorage.getItem(materialKey);
        if (!optionalItem.isPresent()) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(PLAYER_REGEX, sender.getName()));
            return;
        }

        Item item = optionalItem.get();
        int currentQuantity = item.getQuantity();
        if (currentQuantity < amount) {
            context.sendMessage(Message.getMessage("FAIL.not-enough-item")
                    .replaceAll(ITEM_REGEX, setting.getNameFormatted(materialKey, true))
                    .replaceAll(Utils.getRegex("current"), Digital.formatThousands(currentQuantity))
                    .replaceAll(Utils.getRegex("required"), Digital.formatThousands(amount)));
            return;
        }

        Player targetPlayer = Bukkit.getPlayerExact(targetName);
        if (targetPlayer == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found").replaceAll(PLAYER_REGEX, targetName));
            return;
        }

        if (targetPlayer.getUniqueId().equals(sender.getUniqueId())) {
            context.sendMessage(Message.getMessage("FAIL.not-yourself"));
            return;
        }

        if (!senderUser.isPartner(targetPlayer.getUniqueId())) {
            context.sendMessage(Message.getMessage("FAIL.not-partner").replaceAll(PLAYER_REGEX, targetName));
            return;
        }

        User targetUser = instance.getUserManager().getUser(targetPlayer);
        if (targetUser == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found").replaceAll(PLAYER_REGEX, targetName));
            return;
        }
        Storage targetStorage = targetUser.getStorage();

        if (!targetStorage.canStore(materialKey)) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(PLAYER_REGEX, targetPlayer.getName()));
            return;
        }

        long freeSpace = targetStorage.getFreeSpace();
        if (freeSpace != -1 && freeSpace < amount) {
            amount = (int) freeSpace;
        }

        senderStorage.subtract(materialKey, amount);
        targetStorage.add(materialKey, amount);

        senderUser.save();
        targetUser.save();

        context.sendMessage(Message.getMessage("SUCCESS.sent-items")
                .replaceAll(QUANTITY_REGEX, Digital.formatThousands(amount))
                .replaceAll(ITEM_REGEX, setting.getNameFormatted(materialKey, true))
                .replaceAll(PLAYER_REGEX, targetPlayer.getName()));

        targetPlayer.sendMessage(Message.getMessage("SUCCESS.received-items")
                .replaceAll(QUANTITY_REGEX, Digital.formatThousands(amount))
                .replaceAll(ITEM_REGEX, setting.getNameFormatted(materialKey, true))
                .replaceAll(PLAYER_REGEX, sender.getName()));
    }

    private void sendAll(Player sender, User senderUser, Storage senderStorage, String targetName, CommandContext context) {
        Player targetPlayer = Bukkit.getPlayerExact(targetName);
        if (targetPlayer == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found").replaceAll(PLAYER_REGEX, targetName));
            return;
        }

        if (targetPlayer.getUniqueId().equals(sender.getUniqueId())) {
            context.sendMessage(Message.getMessage("FAIL.not-yourself"));
            return;
        }

        if (!senderUser.isPartner(targetPlayer.getUniqueId())) {
            context.sendMessage(Message.getMessage("FAIL.not-partner").replaceAll(PLAYER_REGEX, targetName));
            return;
        }

        User targetUser = instance.getUserManager().getUser(targetPlayer);
        if (targetUser == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found").replaceAll(PLAYER_REGEX, targetName));
            return;
        }
        Storage targetStorage = targetUser.getStorage();

        int totalSent = 0;
        for (Item item : new ArrayList<>(senderStorage.getItems().values())) { // Create a copy to avoid ConcurrentModificationException
            String materialKey = item.getKey();
            int quantity = item.getQuantity();

            if (quantity < 1) continue;

            if (!targetStorage.canStore(materialKey)) {
                context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(PLAYER_REGEX, targetPlayer.getName()));
                continue;
            }

            long freeSpace = targetStorage.getFreeSpace();
            int amountToSend = quantity;
            if (freeSpace != -1 && freeSpace < quantity) {
                amountToSend = (int) freeSpace;
            }

            if (amountToSend > 0) {
                senderStorage.subtract(materialKey, amountToSend);
                targetStorage.add(materialKey, amountToSend);
                totalSent += amountToSend;

                sender.sendMessage(Message.getMessage("SUCCESS.sent-items")
                        .replaceAll(QUANTITY_REGEX, Digital.formatThousands(amountToSend))
                        .replaceAll(ITEM_REGEX, setting.getNameFormatted(materialKey, true))
                        .replaceAll(PLAYER_REGEX, targetPlayer.getName()));

                targetPlayer.sendMessage(Message.getMessage("SUCCESS.received-items")
                        .replaceAll(QUANTITY_REGEX, Digital.formatThousands(amountToSend))
                        .replaceAll(ITEM_REGEX, setting.getNameFormatted(materialKey, true))
                        .replaceAll(PLAYER_REGEX, sender.getName()));
            }
        }

        senderUser.save();
        targetUser.save();

        if (totalSent > 0) {
            context.sendMessage(Message.getMessage("SUCCESS.sent-all-items").replaceAll(PLAYER_REGEX, targetPlayer.getName()));
        } else {
            context.sendMessage(Message.getMessage("FAIL.no-items-to-send"));
        }
    }

    private void sendMaterialAll(Player sender, User senderUser, Storage senderStorage, String materialKey, String targetName, CommandContext context) {
        Optional<Item> optionalItem = senderStorage.getItem(materialKey);
        if (!optionalItem.isPresent()) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(PLAYER_REGEX, sender.getName()));
            return;
        }

        Item item = optionalItem.get();
        int quantity = item.getQuantity();
        if (quantity < 1) {
            context.sendMessage(Message.getMessage("FAIL.not-enough-item")
                    .replaceAll(ITEM_REGEX, setting.getNameFormatted(materialKey, true)));
            return;
        }

        Player targetPlayer = Bukkit.getPlayerExact(targetName);
        if (targetPlayer == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found").replaceAll(PLAYER_REGEX, targetName));
            return;
        }

        if (targetPlayer.getUniqueId().equals(sender.getUniqueId())) {
            context.sendMessage(Message.getMessage("FAIL.not-yourself"));
            return;
        }

        if (!senderUser.isPartner(targetPlayer.getUniqueId())) {
            context.sendMessage(Message.getMessage("FAIL.not-partner").replaceAll(PLAYER_REGEX, targetName));
            return;
        }

        User targetUser = instance.getUserManager().getUser(targetPlayer);
        if (targetUser == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found").replaceAll(PLAYER_REGEX, targetName));
            return;
        }
        Storage targetStorage = targetUser.getStorage();

        if (!targetStorage.canStore(materialKey)) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(PLAYER_REGEX, targetPlayer.getName()));
            return;
        }

        long freeSpace = targetStorage.getFreeSpace();
        int amountToSend = quantity;
        if (freeSpace != -1 && freeSpace < quantity) {
            amountToSend = (int) freeSpace;
        }

        if (amountToSend > 0) {
            senderStorage.subtract(materialKey, amountToSend);
            targetStorage.add(materialKey, amountToSend);

            senderUser.save();
            targetUser.save();

            context.sendMessage(Message.getMessage("SUCCESS.sent-items")
                    .replaceAll(QUANTITY_REGEX, Digital.formatThousands(amountToSend))
                    .replaceAll(ITEM_REGEX, setting.getNameFormatted(materialKey, true))
                    .replaceAll(PLAYER_REGEX, targetPlayer.getName()));

            targetPlayer.sendMessage(Message.getMessage("SUCCESS.received-items")
                    .replaceAll(QUANTITY_REGEX, Digital.formatThousands(amountToSend))
                    .replaceAll(ITEM_REGEX, setting.getNameFormatted(materialKey, true))
                    .replaceAll(PLAYER_REGEX, sender.getName()));
        } else {
            context.sendMessage(Message.getMessage("FAIL.no-items-to-send"));
        }
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, org.bukkit.command.Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            return new ArrayList<>();
        }

        Player player = (Player) sender;

        switch (args.length) {
            case 1 -> {
                // material-key or "all"
                User senderUser = instance.getUserManager().getUser(player);
                if (senderUser == null) return new ArrayList<>();
                
                Storage storage = senderUser.getStorage();
                String input = args[0].toLowerCase();
                
                List<String> suggestions = new ArrayList<>();
                suggestions.add("all");
                suggestions.addAll(storage.getItems().keySet().stream()
                        .filter(key -> storage.getItem(key).isPresent() && storage.getItem(key).get().getQuantity() > 0)
                        .filter(key -> key.toLowerCase().startsWith(input))
                        .sorted()
                        .collect(Collectors.toList()));
                return suggestions.stream().filter(s -> s.startsWith(input)).collect(Collectors.toList());
            }
            case 2 -> {
                // amount or "all" or player name if args[0] was "all"
                String firstArg = args[0].toLowerCase();
                String secondArg = args[1].toLowerCase();

                if (firstArg.equals("all")) {
                    // Expecting player name
                    return Bukkit.getOnlinePlayers().stream()
                            .map(Player::getName)
                            .filter(name -> !name.equals(player.getName())) // Exclude sender's name
                            .filter(name -> name.toLowerCase().startsWith(secondArg))
                            .sorted()
                            .collect(Collectors.toList());
                } else {
                    // Expecting amount or "all"
                    List<String> suggestions = new ArrayList<>();
                    suggestions.add("all");
                    suggestions.addAll(Arrays.asList("1", "10", "64", "1000", "10000", "100000", "1000000"));
                    return suggestions.stream()
                            .filter(amount -> amount.startsWith(secondArg))
                            .collect(Collectors.toList());
                }
            }
            case 3 -> {
                // player name (only if args[0] was material-key and args[1] was amount/all)
                String firstArg = args[0].toLowerCase();
                String secondArg = args[1].toLowerCase();
                String thirdArg = args[2].toLowerCase();

                if (!firstArg.equals("all") && (secondArg.equals("all") || thirdArg.matches("\\d+"))) {
                    return Bukkit.getOnlinePlayers().stream()
                            .map(Player::getName)
                            .filter(name -> !name.equals(player.getName())) // Exclude sender's name
                            .filter(name -> name.toLowerCase().startsWith(thirdArg))
                            .sorted()
                            .collect(Collectors.toList());
                }
            }
        }
        return new ArrayList<>();
    }
}