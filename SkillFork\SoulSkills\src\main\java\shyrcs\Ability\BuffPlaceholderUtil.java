package shyrcs.Ability;

import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffectType;

import java.util.HashMap;
import java.util.Map;

public class BuffPlaceholderUtil {
    
    // Map để lưu trữ tên buff tiếng Việt
    private static final Map<PotionEffectType, String> BUFF_NAMES_VI = new HashMap<>();
    
    static {
        // Khởi tạo tên buff tiếng Việt - Tất cả status effects từ Minecraft 1.21

        // Positive Effects (Buff tích cực)
        BUFF_NAMES_VI.put(PotionEffectType.ABSORPTION, "Hấp Thụ");
        BUFF_NAMES_VI.put(PotionEffectType.SPEED, "Tăng Tốc");
        BUFF_NAMES_VI.put(PotionEffectType.STRENGTH, "Sức Mạnh");
        BUFF_NAMES_VI.put(PotionEffectType.REGENERATION, "<PERSON><PERSON><PERSON>");
        BUFF_NAMES_VI.put(PotionEffectType.RESISTANCE, "Kháng <PERSON>");
        BUFF_NAMES_VI.put(PotionEffectType.FIRE_RESISTANCE, "Kháng Lửa");
        BUFF_NAMES_VI.put(PotionEffectType.WATER_BREATHING, "Thở Dưới Nước");
        BUFF_NAMES_VI.put(PotionEffectType.INVISIBILITY, "Tàng Hình");
        BUFF_NAMES_VI.put(PotionEffectType.NIGHT_VISION, "Nhìn Trong Bóng Tối");
        BUFF_NAMES_VI.put(PotionEffectType.HEALTH_BOOST, "Tăng Sinh Lực");
        BUFF_NAMES_VI.put(PotionEffectType.SATURATION, "Bão Hòa");
        BUFF_NAMES_VI.put(PotionEffectType.LUCK, "May Mắn");
        BUFF_NAMES_VI.put(PotionEffectType.SLOW_FALLING, "Rơi Chậm");
        BUFF_NAMES_VI.put(PotionEffectType.CONDUIT_POWER, "Sức Mạnh Thuỷ Triều");
        BUFF_NAMES_VI.put(PotionEffectType.DOLPHINS_GRACE, "Ân Huệ Của Cá Heo");
        BUFF_NAMES_VI.put(PotionEffectType.HERO_OF_THE_VILLAGE, "Người Hùng Dân Làng");
        BUFF_NAMES_VI.put(PotionEffectType.HASTE, "Đào Nhanh");
        BUFF_NAMES_VI.put(PotionEffectType.JUMP_BOOST, "Nhảy Cao");
        BUFF_NAMES_VI.put(PotionEffectType.INSTANT_HEALTH, "Sinh Lực Tức Thì");
        BUFF_NAMES_VI.put(PotionEffectType.GLOWING, "Phát Sáng");

        // Negative Effects (Debuff)
        BUFF_NAMES_VI.put(PotionEffectType.SLOWNESS, "Chậm Rãi");
        BUFF_NAMES_VI.put(PotionEffectType.MINING_FATIGUE, "Đào Chậm");
        BUFF_NAMES_VI.put(PotionEffectType.INSTANT_DAMAGE, "Sát Thương Tức Thì");
        BUFF_NAMES_VI.put(PotionEffectType.NAUSEA, "Choáng");
        BUFF_NAMES_VI.put(PotionEffectType.BLINDNESS, "Mù Quáng");
        BUFF_NAMES_VI.put(PotionEffectType.HUNGER, "Đói");
        BUFF_NAMES_VI.put(PotionEffectType.WEAKNESS, "Yếu Đuối");
        BUFF_NAMES_VI.put(PotionEffectType.POISON, "Độc");
        BUFF_NAMES_VI.put(PotionEffectType.WITHER, "Khô Héo");
        BUFF_NAMES_VI.put(PotionEffectType.LEVITATION, "Bay");
        BUFF_NAMES_VI.put(PotionEffectType.UNLUCK, "Rủi Ro");
        BUFF_NAMES_VI.put(PotionEffectType.BAD_OMEN, "Điềm Xấu");
        BUFF_NAMES_VI.put(PotionEffectType.DARKNESS, "Bóng Tối");
        BUFF_NAMES_VI.put(PotionEffectType.INFESTED, "Nhiễm Khuẩn");
        BUFF_NAMES_VI.put(PotionEffectType.OOZING, "Rò Rỉ");
        BUFF_NAMES_VI.put(PotionEffectType.WEAVING, "Thiêu Dệt");
        BUFF_NAMES_VI.put(PotionEffectType.WIND_CHARGED, "Nạp Gió");
        BUFF_NAMES_VI.put(PotionEffectType.RAID_OMEN, "Điềm Xâm Lược");
        BUFF_NAMES_VI.put(PotionEffectType.TRIAL_OMEN, "Điềm Thử Thách");
    }
    
    /**
     * Lấy tên buff bằng tiếng Việt
     */
    public static String getBuffNameVi(PotionEffectType buffType) {
        return BUFF_NAMES_VI.getOrDefault(buffType, buffType.getKey().getKey());
    }
    
    /**
     * Xử lý placeholder cho buff
     * Format: %soulskills_buff_<type>_<info>%
     * Ví dụ: %soulskills_buff_absorption_time%
     */
    public static String processBuffPlaceholder(Player player, String identifier) {
        if (!identifier.startsWith("buff_")) {
            return null;
        }
        
        String[] parts = identifier.split("_");
        if (parts.length < 3) {
            return null;
        }
        
        String buffName = parts[1].toUpperCase();
        String info = parts[2].toLowerCase();

        PotionEffectType buffType = BuffUtils.getEffectTypeByName(buffName);
        if (buffType == null) {
            return "0";
        }
        
        switch (info) {
            case "time":
            case "duration":
                return String.valueOf(BuffPlaceholderListener.getBuffTimeRemaining(player, buffName));
                
            case "level":
            case "amplifier":
                return String.valueOf(BuffPlaceholderListener.getBuffLevel(player, buffName));
                
            case "has":
            case "active":
                return BuffPlaceholderListener.hasBuff(player, buffName) ? "true" : "false";
                
            case "name":
                return getBuffNameVi(buffType);
                
            case "formatted":
                if (BuffPlaceholderListener.hasBuff(player, buffName)) {
                    int time = BuffPlaceholderListener.getBuffTimeRemaining(player, buffName);
                    int level = BuffPlaceholderListener.getBuffLevel(player, buffName);
                    return String.format("%s %s (%ds)", 
                            getBuffNameVi(buffType), 
                            toRoman(level), 
                            time);
                } else {
                    return "Không có";
                }
                
            default:
                return null;
        }
    }
    
    /**
     * Chuyển số thành số La Mã
     */
    private static String toRoman(int number) {
        if (number <= 0) return "";
        if (number == 1) return "I";
        if (number == 2) return "II";
        if (number == 3) return "III";
        if (number == 4) return "IV";
        if (number == 5) return "V";
        if (number == 6) return "VI";
        if (number == 7) return "VII";
        if (number == 8) return "VIII";
        if (number == 9) return "IX";
        if (number == 10) return "X";
        return String.valueOf(number);
    }
    
    /**
     * Lấy danh sách tất cả buff ID có sẵn (cập nhật đến Minecraft 1.21)
     */
    public static String[] getAllBuffIds() {
        return new String[]{
            // Positive Effects (Buff tích cực)
            "ABSORPTION", "SPEED", "STRENGTH", "REGENERATION", "RESISTANCE",
            "FIRE_RESISTANCE", "WATER_BREATHING", "INVISIBILITY", "NIGHT_VISION",
            "HEALTH_BOOST", "SATURATION", "LUCK", "SLOW_FALLING", "CONDUIT_POWER",
            "DOLPHINS_GRACE", "HERO_OF_THE_VILLAGE", "HASTE", "JUMP_BOOST",
            "INSTANT_HEALTH", "GLOWING",

            // Negative Effects (Debuff)
            "SLOWNESS", "MINING_FATIGUE", "INSTANT_DAMAGE", "NAUSEA", "BLINDNESS",
            "HUNGER", "WEAKNESS", "POISON", "WITHER", "LEVITATION", "UNLUCK",
            "BAD_OMEN", "DARKNESS", "INFESTED", "OOZING", "WEAVING", "WIND_CHARGED", "RAID_OMEN", "TRIAL_OMEN"
        };
    }
    
    /**
     * Kiểm tra buff ID có hợp lệ không
     */
    public static boolean isValidBuffId(String buffId) {
        return BuffUtils.isValidBuffId(buffId);
    }
}
