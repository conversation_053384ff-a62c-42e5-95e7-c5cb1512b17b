package shyrcs.extrastoragehook.minecraft;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;

/**
 * Listener cho các sự kiện của người chơi
 */
public class PlayerListener implements Listener {
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Kiểm tra xem người chơi đã kết nối Discord chưa
        if (Library.storage.playerConnected(player.getUniqueId())) {
            String discordId = Library.storage.getDiscordID(player.getUniqueId());
            SbMagicHook.info("Người chơi " + player.getName() + " đã tham gia (Discord ID: " + discordId + ")");
            
            // Gửi thông báo đến Discord (tù<PERSON> chọn)
            if (Library.application != null && discordId != null) {
                String message = "Người chơi **" + player.getName() + "** đã tham gia server!";
                Library.application.sendPrivateMessage(discordId, message);
            }
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // Kiểm tra xem người chơi đã kết nối Discord chưa
        if (Library.storage.playerConnected(player.getUniqueId())) {
            String discordId = Library.storage.getDiscordID(player.getUniqueId());
            SbMagicHook.info("Người chơi " + player.getName() + " đã rời khỏi server (Discord ID: " + discordId + ")");
            
            // Gửi thông báo đến Discord (tùy chọn)
            if (Library.application != null && discordId != null) {
                String message = "Người chơi **" + player.getName() + "** đã rời khỏi server!";
                Library.application.sendPrivateMessage(discordId, message);
            }
        }
    }
}
