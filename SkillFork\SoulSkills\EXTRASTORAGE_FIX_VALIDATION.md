# ExtraStorage API Fix Validation Guide

## Tóm tắt các thay đổi đã thực hiện

### 1. C<PERSON>p nhật ExtraStorage Integration (OreMultiplierListener.java)
- **Cải thiện error handling**: Thêm specific exception handling cho ClassNotFoundException và NoSuchMethodException
- **Thêm item registration**: Tự động thêm item vào filter nếu chưa tồn tại bằng `addNewItem()`
- **Cải thiện logging**: Thêm detailed logging cho từng bước của quá trình integration
- **Fix API compatibility**: C<PERSON><PERSON> nhật reflection calls để tương thích với ExtraStorage API mới

### 2. Điều chỉnh Event Priorities
- **OreMultiplierListener**: Thay đổi từ `HIGH` sang `NORMAL` với `ignoreCancelled = true`
- **ExpMineListener**: Thay đổi từ `HIGH` sang `LOW` với `ignoreCancelled = true`
- **Lý do**: Tránh conflict với AuraSkills và các plugin mining khác

### 3. Thêm Debug Mode
- **Kích hoạt**: Thêm JVM argument `-Dsoulskills.debug=true`
- **Logging**: Chi tiết từng bước xử lý ore multiplier
- **Error tracking**: Detailed error messages cho ExtraStorage integration

## Cách test fix

### 1. Chuẩn bị môi trường test
```bash
# Kích hoạt debug mode
java -Dsoulskills.debug=true -jar server.jar

# Hoặc thêm vào start script
echo "-Dsoulskills.debug=true" >> jvm_args.txt
```

### 2. Test cases cần thực hiện

#### Test Case 1: ExtraStorage Integration
1. **Setup**: Đảm bảo ExtraStorage plugin đã được cài đặt và hoạt động
2. **Tool**: Tạo pickaxe với:
   - `ORE_MULTIPLIER: 100.0` (100% chance để dễ test)
   - `ORE_MULTIPLIER_AMOUNT: 3.0` (nhân 3 lần)
3. **Test**: Đào diamond ore
4. **Expected**: 
   - Console log hiển thị debug messages
   - 2x diamond được thêm vào ExtraStorage (3-1=2 extra diamonds)
   - Player nhận message: "Đã thêm 3.0x 2x Diamond vào storage!"

#### Test Case 2: Fallback khi ExtraStorage không khả dụng
1. **Setup**: Tắt ExtraStorage plugin hoặc test trên server không có ExtraStorage
2. **Tool**: Cùng pickaxe như Test Case 1
3. **Test**: Đào diamond ore
4. **Expected**:
   - Console log: "ExtraStorage plugin không tìm thấy!"
   - 2x diamond drop ra ground
   - Player nhận message: "Đã nhân 3.0x 2x Diamond!"

#### Test Case 3: Storage đầy hoặc item không thể store
1. **Setup**: ExtraStorage có sẵn nhưng storage đầy hoặc diamond không trong filter
2. **Tool**: Cùng pickaxe như Test Case 1
3. **Test**: Đào diamond ore
4. **Expected**:
   - Console log: "Storage đã đầy" hoặc "Item không thể store sau khi thêm vào filter"
   - 2x diamond drop ra ground

#### Test Case 4: Conflict với AuraSkills
1. **Setup**: Cài đặt AuraSkills với mining skill
2. **Tool**: Pickaxe có cả Ore Multiplier và AuraSkills mining bonus
3. **Test**: Đào ore và kiểm tra cả hai bonus có hoạt động không
4. **Expected**: Cả hai bonus hoạt động độc lập, không conflict

### 3. Debug Log Examples

#### Successful ExtraStorage Integration:
```
[INFO] [OreMultiplier] Activated! Random 45.23 <= chance 100.0
[INFO] [OreMultiplier] Base amount: 1, Multiplier: 3.0x, Extra amount: 2
[INFO] [OreMultiplier] Successfully added 2x DIAMOND to storage
```

#### ExtraStorage Not Available:
```
[WARNING] [OreMultiplier] ExtraStorage plugin không tìm thấy!
[INFO] [OreMultiplier] Dropped 2x DIAMOND on ground
```

#### Storage Full:
```
[INFO] [OreMultiplier] Storage đã đầy cho player: PlayerName
[INFO] [OreMultiplier] Dropped 2x DIAMOND on ground
```

### 4. Performance Test
1. **Setup**: Nhiều players đào ore cùng lúc
2. **Monitor**: CPU usage và memory consumption
3. **Expected**: Không có lag đáng kể, debug logs không spam

### 5. Compatibility Test
1. **AuraSkills**: Test với mining skill bonuses
2. **Other mining plugins**: Test với các plugin khác có BlockBreakEvent listeners
3. **Expected**: Không có conflicts, tất cả plugins hoạt động bình thường

## Troubleshooting

### Nếu ExtraStorage vẫn không hoạt động:
1. Kiểm tra console logs với debug mode
2. Xác nhận ExtraStorage plugin version và API compatibility
3. Test với ExtraStorage commands để đảm bảo plugin hoạt động bình thường

### Nếu có conflicts với plugins khác:
1. Kiểm tra event priorities của các plugins khác
2. Có thể cần điều chỉnh priority của OreMultiplierListener
3. Sử dụng `/timings` để phân tích performance

### Nếu debug logs không xuất hiện:
1. Đảm bảo JVM argument `-Dsoulskills.debug=true` được set đúng
2. Restart server sau khi thêm argument
3. Kiểm tra server console, không phải game chat

## Kết luận
Fix này giải quyết các vấn đề chính:
- ✅ ExtraStorage API compatibility
- ✅ Event priority conflicts
- ✅ Error handling và debugging
- ✅ Fallback mechanisms

Sau khi test thành công, có thể remove debug mode bằng cách bỏ JVM argument.
