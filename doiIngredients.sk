command /chetao <text> [<player>]:
    permission: chetao.use
    trigger:
        set {_type} to arg-1
        
        # determine target player
        if arg-2 is set:
            # command run from console
            set {_target} to arg-2
        else:
            # command run by player
            set {_target} to player
        
        if {_target} is not set:
            send "&cPlayer not found or command must be run by a player!" to console
            stop

        if {_type} is "COAL_TINH_KHIET":
            set {_material} to "COAL"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "&8Than <PERSON><PERSON>"
        else if {_type} is "LAPIS_TINH_KHIET":
            set {_material} to "LAPIS_LAZULI"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "&9Lư<PERSON>"
        else if {_type} is "REDSTONE_TINH_KHIET":
            set {_material} to "REDSTONE"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "&c<PERSON><PERSON> Đỏ <PERSON>"
        else if {_type} is "COPPER_TINH_KHIET":
            set {_material} to "COPPER_INGOT"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "<#A59616>Đồng Tinh Khiết"
        else if {_type} is "IRON_TINH_KHIET":
            set {_material} to "IRON_INGOT"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "&fSắt Tinh Khiết"
        else if {_type} is "GOLD_TINH_KHIET":
            set {_material} to "GOLD_INGOT"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "&eVàng Tinh Khiết"
        else if {_type} is "DIAMOND_TINH_KHIET":
            set {_material} to "DIAMOND"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "&bKim Cương Tinh Khiết"
        else if {_type} is "EMERALD_TINH_KHIET":
            set {_material} to "EMERALD"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "&aLục Bảo Tinh Khiết"
        else if {_type} is "NETHERITE_TINH_KHIET":
            set {_material} to "NETHERITE_INGOT"
            set {_cost} to 32
            set {_max_stack} to 64
            set {_display_name} to "<#5B5A4A>Netherite Tinh Khiết"
        else if {_type} is "AMETHYST_TINH_KHIET":
            set {_material} to "AMETHYST_BLOCK"
            set {_cost} to 128
            set {_max_stack} to 64
            set {_display_name} to "&dThạch Anh Tinh Khiết"
        else:
            send "&cLoại khoáng sản không hợp lệ!"
            stop

        set {_placeholder} to "exstorage_quantity_%{_material}%"
        set {_amount} to placeholder {_placeholder} for {_target} parsed as number
        
        if {_amount} < {_cost}:
            send "&c%{_target}% không đủ %{_material}% để chế tạo %{_display_name}%!" to console
            send "&cBạn không đủ %{_material}% để chế tạo %{_display_name}%!" to {_target}
            stop

        set {_slot_trong} to 0
        loop 36 times:
            set {_item} to slot (loop-number - 1) of {_target}'s inventory
            if {_item} is air:
                add 1 to {_slot_trong}
        
        # Debug: Show how many empty slots found
        # send "&7Debug: %{_target}% có %{_slot_trong}% slot trống" to console

        set {_max_craft_material} to floor({_amount} / {_cost})
        
        set {_max_items_fit} to {_slot_trong} * {_max_stack}
        
        if {_max_items_fit} >= {_max_craft_material}:
            set {_max_craft} to {_max_craft_material}
        else:
            set {_max_craft} to {_max_items_fit}

        if {_max_craft} < 1:
            send "&c%{_target}% không đủ chỗ trống để nhận %{_display_name}%!" to console
            send "&cKho đồ bạn không đủ chỗ trống để nhận %{_display_name}%!" to {_target}
            stop

        # Debug info
        # send "&7Debug: %{_target}% có thể chế tạo %{_max_craft_material}% cái từ nguyên liệu" to console
        # send "&7Debug: %{_slot_trong}% slot trống có thể chứa %{_max_items_fit}% cái" to console
        # send "&7Debug: Chế tạo %{_max_craft}% cái cho %{_target}%" to console

        set {_total_cost} to {_max_craft} * {_cost}
        
        execute console command "esadmin take %{_material}% %{_total_cost}% %{_target}%"
        
        execute console command "mi give MATERIAL %{_type}% %{_target}% %{_max_craft}%"
        send "&a%{_target}% đã chế tạo thành công %{_max_craft}% &7[&fIngredients&7] %{_display_name}%" to console
        send "&aBạn đã chế tạo thành công %{_max_craft}% &7[&fIngredients&7] %{_display_name}%" to {_target}