RoomDmgRun:
  Skills:
    - 'effect:particlesphere{p=reddust;color=128,0,128;radius=<modifier.range>;amount=60;points=40;hollow=true;half=true;interval=5} @self'
    - 'repeat{interval=20;times=<modifier.duration>;onRepeat=RoomDmg}'

RoomDmg:
  Skills:
    - 'mmodamage{amount=<modifier.damage>;types=SKILL,MAGIC;ignoreArmor=true} @LivingEntitiesInRadius{radius=<modifier.range>;conditions=[- mmoCanTarget{interaction=OFFENSE_ACTION} true]}'