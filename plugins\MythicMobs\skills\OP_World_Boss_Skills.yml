SonettoOnSpawn:
  Skills:
  - prison{m=barrier;d=70;b=false} @PIR{r=100}
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name><&sp>&7[<&sp>&cHoàn<&sp>Chỉnh<&sp>&7]<&sp>&f&l<<”;subtitle=“&f[<&sp>&b&lĐã<&sp><PERSON><PERSON><PERSON><PERSON><&sp><PERSON><PERSON><PERSON><&sp><PERSON><PERSON><PERSON><&sp>&f]”}
  - message{m="&7[&f<mob.name>&7] &aOh? Timekeeper? You're here&a."} @PIR{r=100}
  - delay 20
  - message{m="&7[&f<mob.name>&7] &aAlright... Let's bring on a show."} @PIR{r=100}
  - delay 20
  - message{m="&7[&f<mob.name>&7] &aMay the peace be with us."} @PIR{r=100}
  - effect:smokeswirl{d=100;i=10} @Self
  - delay 40
  - throw{velocity=50;velocityY=10} @PlayersInRadius{r=6}
  - sound{s=entity.lightning_bolt.thunder;v=5} @PIR{r=30}
  - setstance{s=Combat} @self
SonettoPassive:
  Conditions:
  - stance Combat
  Skills:
  - particlering{p=endRod;a=25;pts=55;r=4;s=0.01;y=0.42;vd=30} @self
  - damage{a=100} @PIR{r=4}
  - heal{a=1500} @self
SonettoFirstSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&7[&f<mob.name>&7] &aIlluminate."} @PIR{r=100}
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}  @Sphere{r=2;p=4600;y=0;exact=true}
  - throw{v=5;vy=10} @PIR{r=2}
  - damagepercent{a=0.1} @PIR{r=2}
  - delay 5
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}  @Sphere{r=3;p=4600;y=0;exact=true}
  - throw{v=5;vy=10} @PIR{r=3}
  - damagepercent{a=0.1} @PIR{r=3}
  - delay 5
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}  @Sphere{r=4;p=4600;y=0;exact=true}
  - throw{v=5;vy=10} @PIR{r=4}
  - damagepercent{a=0.1} @PIR{r=4}
  - delay 5
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}  @Sphere{r=5;p=4600;y=0;exact=true}
  - throw{v=5;vy=10} @PIR{r=5}
  - damagepercent{a=0.1} @PIR{r=5}
  - delay 5
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}  @Sphere{r=6;p=4600;y=0;exact=true}
  - throw{v=5;vy=10} @PIR{r=6}
  - damagepercent{a=0.1} @PIR{r=6}
  - delay 5
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}  @Sphere{r=7;p=4600;y=0;exact=true}
  - throw{v=5;vy=10} @PIR{r=7}
  - damagepercent{a=0.1} @PIR{r=7}
  - delay 5
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}  @Sphere{r=8;p=4600;y=0;exact=true}
  - throw{v=5;vy=10} @PIR{r=8}
  - damagepercent{a=0.1} @PIR{r=8}
  - delay 5
  - message{m="&7[&f<mob.name>&7] &aEn tu me gardo."} @PIR{r=100}
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a2.0s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.9s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.8s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.7s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.6s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.5s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.4s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.3s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.2s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.1s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a1.0s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.9s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.8s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.7s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.6s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.5s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.4s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.3s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.2s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.1s";d=3} @PIR{r=100}
  - delay 2
  - sendtitle{t="&e&lIlluminating... &c&lHide! &a0.0s";d=3} @PIR{r=100}
  - delay 2
  - skill{s=FirstCheck}
FirstCheck:
  TargetConditions:
  - lineofsight castInstead FailCheck
  Skills:
  - message{m="&7[&f<mob.name>&7] &aHu... la ringrazio. I will make good use of this power."} @PIR{r=100}
  - potion{type=INCREASE_DAMAGE;lvl=2;duration=100}
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}
FailCheck:
  Skills:
  - message{m="&7[&f<mob.name>&7] &aYou've found the right verse for me."} @PIR{r=100}
  - potion{type=INCREASE_DAMAGE;lvl=8;duration=400} @self
  - setstance{s=Enraged} @Self
  - skill{s=CommandmentV}
CommandmentV:
  Conditions:
  - stance Enraged
  Skills:
  - teleport @target
  - aura{auraname=Commandment;os=Begin;ot=Phasing;oe=End;i=2;d=120}
Begin:
  Skills:
  - potion{type=BLINDNESS;lvl=2;duration=100} @target
  - potion{type=SLOW;lvl=2;duration=100} @target
  - potion{type=SLOW_DIGGING;lvl=2;duration=100} @target
  - potion{type=WEAKNESS;lvl=2;duration=100} @target
Phasing:
  Skills:
  - teleport @target
  - damage{d=15;ia=true} @target
  - heal{a=150} @self
End:
  Skills:
  - stun{d=60;f=true;cod=true} @target
  - setstance{s=Combat} @self
SonettoSecondSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&7[&f<mob.name>&7] &aSempre caro mi fu quest’ermo colle."} @PIR{r=100}
  - potion{type=INCREASE_DAMAGE;lvl=4;duration=100} @self
  - potion{type=DAMAGE_RESISTANCE;lvl=4;duration=100} @self
  - potion{type=REGENERATION;lvl=4;duration=100} @self
  - potion{type=SPEED;lvl=4;duration=100} @self
  - potionclear @PIR{r=10}
SonettoThirdSkill:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&7[&f<mob.name>&7] &aRegna sereni intenso ed infinito."} @PIR{r=100}
  - effect:particles{p=reddust;c=#08FF09;a=1;xs=0.1;zs=0.1;s=0.0125}  @Sphere{r=8;p=4600;y=0;exact=true}
  - skill{s=UnrestrictedChant}
UnrestrictedChant:
  Skills:
  - aura{auraname=UltimateCast;os=UltBegin;ot=UltTicking;oe=UltEnding;i=6;d=120}
UltBegin:
  Skills:
  - particlering{p=endRod;a=25;pts=14;r=0;s=0.01;y=0.42;vd=30} @self
UltTicking:
  Skills:
  - particlering{p=endRod;a=25;pts=14;r=2;s=0.01;y=0.42;vd=30} @pir{r=40}
  - lightning{d=10} @PIR{r=40}
UltEnding:
  Skills:
  - particlering{p=endRod;a=25;pts=14;r=4;s=0.01;y=0.42;vd=30} @self
  - skill{s=InTheLastLight}
InTheLastLight:
  Skills:
  - particlering{p=endRod;a=25;pts=14;r=10;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=9;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=8;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=7;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=6;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=5;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=4;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=3;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=2;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
  - particlering{p=endRod;a=25;pts=14;r=1;s=0.01;y=0.42;vd=30} @self
  - sendtitle{t="&c&lCrouch!";d=5}
  - delay 5
  - skill{s=JumpCheck}
  - delay 5
JumpCheck:
  TargetConditions:
  - crouching orElseCast JumpFail
  Skills:
  - message{m="&7[&f<mob.name>&7] &aMagnum opus! Operculum meum te obtinuit."}
  - heal{a=100} @Pir{r=40}
JumpFail:
  Skills:
  - message{m="&7[&f<mob.name>&7] &aCavete!"}
  - damagepercent{d=0.1;ia=true} @pir{r=40}
SonettoOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @Server
  - message{m="&f&l[&c&l<mob.name>&f&l] &9Chưa mà... Tớ hứa đó...."} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=6}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=50}