package shyrcs.extrastoragehook.bridge;

import java.security.SecureRandom;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Bridge Hook để quản lý mã kết nối giữa Discord và Minecraft
 * Tương tự như BridgeHook trong HyperHook
 */
public class BridgeHook {
    
    /**
     * Tạo key ngẫu nhiên
     */
    public static String generateKey(int length, char[] charMap) {
        Random random = new SecureRandom();
        StringBuilder keyBuilder = new StringBuilder();
        for(int i = 0; i < length; i++) {
            keyBuilder.append(charMap[random.nextInt(charMap.length)]);
        }
        return keyBuilder.toString();
    }
    
    private final Map<String, String> bridge;
    private final long tickTimeout;
    
    public BridgeHook(long timeout) {
        this.bridge = Collections.synchronizedMap(new HashMap<>());
        this.tickTimeout = timeout;
    }
    
    /**
     * Lấy timeout tính bằng ticks
     */
    public long getTimeOut() {
        return this.tickTimeout;
    }
    
    /**
     * Lấy timeout tính bằng giây
     */
    public long getTimeOutSeconds() {
        return Math.floorDiv(tickTimeout, 20);
    }
    
    /**
     * Ghi mã kết nối
     */
    public synchronized void write(String key, String id) {
        this.bridge.put(key, id);
    }
    
    /**
     * Đọc Discord ID từ mã kết nối
     */
    public synchronized String getID(String key) {
        return this.bridge.get(key);
    }
    
    /**
     * Kiểm tra mã kết nối có tồn tại không
     */
    public synchronized boolean keyExisted(String key) {
        return this.bridge.containsKey(key);
    }
    
    /**
     * Xóa mã kết nối
     */
    public synchronized void delete(String key) {
        this.bridge.remove(key);
    }
    
    /**
     * Lấy số lượng mã kết nối hiện tại
     */
    public synchronized int size() {
        return this.bridge.size();
    }
    
    /**
     * Xóa tất cả mã kết nối
     */
    public synchronized void clear() {
        this.bridge.clear();
    }
    
    /**
     * Lấy tất cả mã kết nối (chỉ để debug)
     */
    public synchronized Map<String, String> getAllBridges() {
        return new HashMap<>(this.bridge);
    }
}
