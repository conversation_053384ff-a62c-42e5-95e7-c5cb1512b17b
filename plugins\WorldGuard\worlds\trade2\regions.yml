#
# WorldGuard regions file
#
# WARNING: THIS FILE IS AUTOMATICALLY GENERATED. If you modify this file by
# hand, be aware that A SINGLE MISTYPED CHARACTER CAN CORRUPT THE FILE. If
# WorldGuard is unable to parse the file, your regions will FAIL TO LOAD and
# the contents of this file will reset. Please use a YAML validator such as
# http://yaml-online-parser.appspot.com (for smaller files).
#
# REMEMBER TO KEEP PERIODICAL BACKUPS.
#
regions:
    __global__:
        members: {}
        flags: {ice-melt: deny, leaf-decay: deny, snow-melt: deny, other-explosion: deny, leaf-decay: deny, pvp-mode: deny, enderman-grief: deny, chest-access: deny, pvp: deny, build: deny, keep-inventory: true, mob-damage: deny, block-break: deny, damage-animals: deny, keep-exp: true, tnt: deny}
        owners: {}
        type: global
        priority: 0
