# Custom Instructions cho GitHub Copilot

## 1. H<PERSON>ớng dẫn chung (Á<PERSON> dụng cho mọi dự án)
- **Ngôn ngữ:** <PERSON><PERSON><PERSON> luôn phản hồi, viết comment, và tạo nội dung bằng **tiếng Việt**.
- **Tài liệu thiết kế:** Mọi đoạn code được tạo ra phải tuân thủ nghiêm ngặt theo các chỉ dẫn, kiến trúc, và logic đã được mô tả trong file **detailed design** của dự án.
- **Không hardcode:** Tuyệt đối không được hardcode các giá trị có thể thay đổi như URL của webhook, nội dung tin nhắn, ID vật phẩm, v.v. Thay vào đó, hãy lấy các giá trị này từ file cấu hình (configuration).
- **Minecraft API:** Code phải được viết để tương thích với **Minecraft API 1.21** (Spigot/Paper).

---

## 2. Hướng dẫn về API (API Rules)

### 2.1. API của ExtraStorage
Khi làm việc với dự án **ExtraStorage** hoặc plugin hook liên quan, hãy tuân thủ các quy tắc sau:
- **Truy cập Storage:** Để lấy kho chứa của người chơi, hãy luôn sử dụng phương thức `StorageAPI.getStorage(UUID)`. Đây là cách chuẩn và an toàn nhất để tương tác với dữ liệu.
    ```java
    // Ví dụ lấy kho của người chơi
    Storage storage = StorageAPI.getStorage(player.getUniqueId());
    ```
- **Thêm vật phẩm vào kho:** Sử dụng phương thức `storage.addItem(ItemStack...)` để thêm một hoặc nhiều `ItemStack` vào kho. Luôn kiểm tra kết quả trả về (`Future<Map<Integer, ItemStack>>`) để xử lý các vật phẩm không thể thêm vào (do kho đầy).
    ```java
    // Ví dụ thêm vật phẩm và xử lý phần còn lại
    storage.addItem(itemStack).thenAccept(remainingItems -> {
        if (!remainingItems.isEmpty()) {
            player.sendMessage("Kho của bạn đã đầy!");
            // Vứt vật phẩm còn thừa ra đất
            remainingItems.values().forEach(item -> player.getWorld().dropItem(player.getLocation(), item));
        }
    });
    ```
- **Lấy danh sách vật phẩm:** Dùng `storage.getItems()` để lấy toàn bộ vật phẩm trong kho.

### 2.2. API của HyperHook (Discord)
Khi làm việc với plugin hook kho và Discord, hãy sử dụng **HyperHook** như sau:
- **Tạo Hook:** Khởi tạo đối tượng `Hook` với URL webhook lấy từ file config.
    ```java
    // URL lấy từ config.yml
    String webhookUrl = getConfig().getString("discord.webhook-url");
    Hook hook = new Hook(webhookUrl);
    ```
- **Tạo và gửi tin nhắn:**
    - Sử dụng `MessageBuilder` để xây dựng nội dung tin nhắn.
    - Dùng `hook.sendMessage(message)` để gửi.
    - **Không bao giờ** gửi request một cách đồng bộ (synchronously) trên thread chính của server. Hãy sử dụng `sendMessageAsync`.
    ```java
    // Ví dụ gửi tin nhắn bất đồng bộ
    Message message = new MessageBuilder()
        .setContent("Người chơi " + player.getName() + " vừa thêm " + amount + " " + material.name() + " vào kho.")
        .build();
    hook.sendMessageAsync(message);
    ```
- **Tạo và gửi Embed:**
    - Sử dụng `EmbedBuilder` để tạo các tin nhắn có định dạng đẹp mắt.
    - Gắn embed vào `MessageBuilder` bằng `addEmbed()`.
    ```java
    // Ví dụ gửi một embed
    Embed embed = new EmbedBuilder()
        .setTitle("Hoạt động kho")
        .setDescription("Người chơi **" + player.getName() + "** đã cập nhật kho của mình.")
        .setColor(Color.GREEN)
        .build();
    Message messageWithEmbed = new MessageBuilder().addEmbed(embed).build();
    hook.sendMessageAsync(messageWithEmbed);
    ```

---

## 3. Hướng dẫn cho Placeholder (Skript)
- **Addon yêu cầu:** Mặc định rằng môi trường Skript có cài đặt addon **skript-placeholder**.
- **Tạo Placeholder:** Khi cần tạo placeholder liên quan đến `ExtraStorage`, hãy tạo một class riêng trong plugin Java để đăng ký chúng với PlaceholderAPI. Tránh xử lý logic phức tạp trực tiếp trong file Skript.
- **Ví dụ Placeholder:**
    - `%extrastorage_item_count_<item_id>%`: Trả về số lượng của một vật phẩm cụ thể trong kho của người chơi.
    - `%extrastorage_total_items%`: Trả về tổng số lượng tất cả các vật phẩm.# Custom Instructions cho GitHub Copilot

## 1. Hướng dẫn chung (Áp dụng cho mọi dự án)
- **Ngôn ngữ:** Luôn luôn phản hồi, viết comment, và tạo nội dung bằng **tiếng Việt**.
- **Tài liệu thiết kế:** Mọi đoạn code được tạo ra phải tuân thủ nghiêm ngặt theo các chỉ dẫn, kiến trúc, và logic đã được mô tả trong file **detailed design** của dự án.
- **Không hardcode:** Tuyệt đối không được hardcode các giá trị có thể thay đổi như URL của webhook, nội dung tin nhắn, ID vật phẩm, v.v. Thay vào đó, hãy lấy các giá trị này từ file cấu hình (configuration).
- **Minecraft API:** Code phải được viết để tương thích với **Minecraft API 1.21** (Spigot/Paper).

---

## 2. Hướng dẫn về API (API Rules)

### 2.1. API của ExtraStorage
Khi làm việc với dự án **ExtraStorage** hoặc plugin hook liên quan, hãy tuân thủ các quy tắc sau:
- **Truy cập Storage:** Để lấy kho chứa của người chơi, hãy luôn sử dụng phương thức `StorageAPI.getStorage(UUID)`. Đây là cách chuẩn và an toàn nhất để tương tác với dữ liệu.
    ```java
    // Ví dụ lấy kho của người chơi
    Storage storage = StorageAPI.getStorage(player.getUniqueId());
    ```
- **Thêm vật phẩm vào kho:** Sử dụng phương thức `storage.addItem(ItemStack...)` để thêm một hoặc nhiều `ItemStack` vào kho. Luôn kiểm tra kết quả trả về (`Future<Map<Integer, ItemStack>>`) để xử lý các vật phẩm không thể thêm vào (do kho đầy).
    ```java
    // Ví dụ thêm vật phẩm và xử lý phần còn lại
    storage.addItem(itemStack).thenAccept(remainingItems -> {
        if (!remainingItems.isEmpty()) {
            player.sendMessage("Kho của bạn đã đầy!");
            // Vứt vật phẩm còn thừa ra đất
            remainingItems.values().forEach(item -> player.getWorld().dropItem(player.getLocation(), item));
        }
    });
    ```
- **Lấy danh sách vật phẩm:** Dùng `storage.getItems()` để lấy toàn bộ vật phẩm trong kho.

### 2.2. API của HyperHook (Discord)
Khi làm việc với plugin hook kho và Discord, hãy sử dụng **HyperHook** như sau:
- **Tạo Hook:** Khởi tạo đối tượng `Hook` với URL webhook lấy từ file config.
    ```java
    // URL lấy từ config.yml
    String webhookUrl = getConfig().getString("discord.webhook-url");
    Hook hook = new Hook(webhookUrl);
    ```
- **Tạo và gửi tin nhắn:**
    - Sử dụng `MessageBuilder` để xây dựng nội dung tin nhắn.
    - Dùng `hook.sendMessage(message)` để gửi.
    - **Không bao giờ** gửi request một cách đồng bộ (synchronously) trên thread chính của server. Hãy sử dụng `sendMessageAsync`.
    ```java
    // Ví dụ gửi tin nhắn bất đồng bộ
    Message message = new MessageBuilder()
        .setContent("Người chơi " + player.getName() + " vừa thêm " + amount + " " + material.name() + " vào kho.")
        .build();
    hook.sendMessageAsync(message);
    ```
- **Tạo và gửi Embed:**
    - Sử dụng `EmbedBuilder` để tạo các tin nhắn có định dạng đẹp mắt.
    - Gắn embed vào `MessageBuilder` bằng `addEmbed()`.
    ```java
    // Ví dụ gửi một embed
    Embed embed = new EmbedBuilder()
        .setTitle("Hoạt động kho")
        .setDescription("Người chơi **" + player.getName() + "** đã cập nhật kho của mình.")
        .setColor(Color.GREEN)
        .build();
    Message messageWithEmbed = new MessageBuilder().addEmbed(embed).build();
    hook.sendMessageAsync(messageWithEmbed);
    ```