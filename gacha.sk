# Tên Skript: randomkhuyenmai.sk
# Tác giả: Đ<PERSON>i tác lập tr<PERSON><PERSON> (Gemini)
# Phiên bản: 2.0
# Mô tả: Hệ thống khuyến mãi nạp thẻ ngẫu nhiên cho SimpPay, hỗ trợ Banking và Thẻ cào, sử dụng PlayerPoints.

options:
    # --- PHẦN CẤU HÌNH ---
    prefix: &e&lKHUYẾN MÃI &8»&r # Tiền tố cho các tin nhắn
    admin_permission: "khuyenmai.admin" # Quyền để sử dụng lệnh admin
    currency_symbol: "Points" # Đơn vị tiền tệ (ví dụ: Points, P, Điểm)

    # Cấu hình cho nạp Banking (PayOS)
    enable_banking_promo: true # Đặt là false để tắt KM cho banking
    banking_method_name: "banking" # Tên phương thức thanh toán banking trong config.yml của SimpPay

    # Cấ<PERSON> hình cho nạp Thẻ cào
    enable_card_promo: true # Đặt là false để tắt KM cho thẻ cào

#================================================================
# HÀM XỬ LÝ KHUYẾN MÃI (ĐỂ TRÁNH LẶP CODE)
#================================================================
function applyPromotion(player: player, amount: number):
    # Chỉ thực hiện nếu người chơi có khuyến mãi
    if {khuyenmai::%{_player}'s uuid%} is not set:
        stop

    # Kiểm tra xem khuyến mãi có hết hạn không (24 giờ)
    set {_expire} to {khuyenmai.time::%{_player}'s uuid%} + 24 hours
    set {_time_passed} to difference between {_expire} and now
    if {_time_passed} < 0:
        send "{@prefix} &cRất tiếc, khuyến mãi của bạn đã hết hạn!" to {_player}
        delete {khuyenmai::%{_player}'s uuid%}
        delete {khuyenmai.percent::%{_player}'s uuid%}
        delete {khuyenmai.time::%{_player}'s uuid%}
        stop

    # Tất cả điều kiện đã hợp lệ, tiến hành cộng thưởng
    set {_multiplier} to {khuyenmai::%{_player}'s uuid%}
    set {_bonus_percent} to {khuyenmai.percent::%{_player}'s uuid%}
    # Tính toán số tiền thưởng (Làm tròn số nguyên)
    set {_bonus_amount} to round(({_amount} * {_multiplier}) - {_amount})

    # Dùng PlayerPoints để cộng tiền
    execute console command "points give %{_player}'s name% %{_bonus_amount}%"

    # Gửi thông báo cho người chơi
    send "" to {_player}
    send "{@prefix} &a&lKHUYẾN MÃI THÀNH CÔNG!" to {_player}
    send "&fBạn đã nạp thành công &e%{_amount}%&f." to {_player}
    send "&fĐược thưởng &d&l%{_bonus_percent}%%&f, tương ứng &a&l+%{_bonus_amount}% {@currency_symbol}&f." to {_player}

    # Thông báo cho admin trên console
    log "[RandomKhuyenMai] Player %{_player}'s name% nap %{_amount}% va nhan duoc %{_bonus_amount}% points thuong." to "khuyenmai.log"

    # QUAN TRỌNG: Xóa khuyến mãi sau khi đã sử dụng
    delete {khuyenmai::%{_player}'s uuid%}
    delete {khuyenmai.percent::%{_player}'s uuid%}
    delete {khuyenmai.time::%{_player}'s uuid%}

#================================================================
# LỆNH CHO NGƯỜI CHƠI
#================================================================
command /nhankhuyenmai:
    aliases: /km, /quaykm
    trigger:
        if {khuyenmai::%player's uuid%} is set:
            set {_expire} to {khuyenmai.time::%player's uuid%} + 24 hours
            set {_timeLeft} to difference between {_expire} and now
            send "{@prefix} &cBạn đã có một mốc khuyến mãi đang chờ!"
            send "&7&oMức khuyến mãi của bạn là &e&l%{khuyenmai.percent::%player's uuid%}%&7&o, hết hạn sau &a%{_timeLeft}%&7&o."
            send "&7&oHãy nạp thẻ (Banking hoặc Thẻ cào) để sử dụng nhé!"
            stop

        set {_chance} to a random integer between 1 and 100
        if {_chance} is between 1 and 50: # 50%
            set {khuyenmai::%player's uuid%} to 1.5
            set {khuyenmai.percent::%player's uuid%} to 50
        else if {_chance} is between 51 and 80: # 30%
            set {khuyenmai::%player's uuid%} to 2
            set {khuyenmai.percent::%player's uuid%} to 100
        else if {_chance} is between 81 and 95: # 15%
            set {khuyenmai::%player's uuid%} to 2.5
            set {khuyenmai.percent::%player's uuid%} to 150
        else: # 5%
            set {khuyenmai::%player's uuid%} to 3
            set {khuyenmai.percent::%player's uuid%} to 200
        set {khuyenmai.time::%player's uuid%} to now
        send ""
        send "{@prefix} &a&lCHÚC MỪNG!"
        send "&fBạn vừa quay được gói khuyến mãi &e&l%{khuyenmai.percent::%player's uuid%}%&f!"
        send "&7Gói này sẽ có hiệu lực trong &b24 giờ&7 và áp dụng cho lần nạp Banking hoặc Thẻ cào tiếp theo."
        send ""

#================================================================
# SỰ KIỆN NẠP THẺ (BANKING)
#================================================================
on SimpPay successful paid:
    if {@enable_banking_promo} is false:
        stop
    set {_player} to event-player
    set {_amount} to event-amount
    set {_method} to event-payment method
    if {_method} is "{@banking_method_name}":
        applyPromotion({_player}, {_amount})

#================================================================
# SỰ KIỆN NẠP THẺ (THẺ CÀO)
#================================================================
on SimpPay successful card:
    if {@enable_card_promo} is false:
        stop
    set {_player} to event-player
    set {_amount} to event-amount
    applyPromotion({_player}, {_amount})

#================================================================
# LỆNH CHO ADMIN (Không thay đổi nhiều)
#================================================================
command /randomkm [<text>] [<offline player>]:
    permission: {@admin_permission}
    trigger:
        # ... (Phần lệnh admin giữ nguyên như phiên bản trước, chỉ cần copy paste lại)
        if arg-1 is not set:
            send "{@prefix} &fSử dụng:"
            send "&a/randomkm reload &7- Tải lại Skript."
            send "&a/randomkm check <tên> &7- Kiểm tra khuyến mãi của người chơi."
            send "&a/randomkm clear <tên> &7- Xóa khuyến mãi của người chơi."
            stop
            
        if arg-1 is "reload":
            execute console command "sk reload randomkhuyenmai"
            send "{@prefix} &aĐã tải lại Skript randomkhuyenmai.sk!"
            stop

        if arg-1 is "set":
            if arg-2 is not set or arg-3 is not set or arg-4 is not set:
                send "{@prefix} &cCú pháp: /randomkm set <tên> <hệ số> <phần trăm>"
                stop
            set {khuyenmai::%arg-2's uuid%} to arg-3 parsed as number
            set {khuyenmai.percent::%arg-2's uuid%} to arg-4 parsed as number
            set {khuyenmai.time::%arg-2's uuid%} to now
            send "{@prefix} &aĐã tạo khuyến mãi cá nhân cho &e%arg-2%&a!"
            stop

        if arg-1 is "check":
            if arg-2 is not set:
                send "{@prefix} &cVui lòng nhập tên người chơi."
                stop
            if {khuyenmai::%arg-2's uuid%} is set:
                set {_expire} to {khuyenmai.time::%arg-2's uuid%} + 24 hours
                set {_timeLeft} to difference between {_expire} and now
                send "{@prefix} &fThông tin khuyến mãi của &e%arg-2%&f:"
                send "&7- Mức KM: &e%{khuyenmai.percent::%arg-2's uuid%}%&7 (&ax%{khuyenmai::%arg-2's uuid%}%&7)"
                send "&7- Thời gian còn lại: &a%{_timeLeft}%"
            else:
                send "{@prefix} &cNgười chơi &e%arg-2%&c không có khuyến mãi nào."
            stop

        if arg-1 is "clear":
            if arg-2 is not set:
                send "{@prefix} &cVui lòng nhập tên người chơi."
                stop
            if {khuyenmai::%arg-2's uuid%} is set:
                delete {khuyenmai::%arg-2's uuid%}
                delete {khuyenmai.percent::%arg-2's uuid%}
                delete {khuyenmai.time::%arg-2's uuid%}
                send "{@prefix} &aĐã xóa khuyến mãi của người chơi &e%arg-2%&a."
            else:
                send "{@prefix} &cNgười chơi &e%arg-2%&c không có khuyến mãi nào để xóa."
            stop