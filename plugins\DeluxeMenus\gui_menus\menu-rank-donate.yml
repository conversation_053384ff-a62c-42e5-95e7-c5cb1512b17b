menu_title: '&eGiao diện mua rank'
size: 53
items:
  'Fill':
    material: GRAY_STAINED_GLASS_PANE
    display_name: '&f'
    slots:
    - 0-53
  'Fill-B':
    material: WHITE_STAINED_GLASS_PANE
    display_name: '&f'
    slots: [9,18,27,36,45,17,26,35,44]
  'U-Bar':
    material: BLUE_STAINED_GLASS_PANE
    display_name: '&f'
    slots:
    - 0-8
    - 45-53

  # 'next':
  #   material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvMjkxYWM0MzJhYTQwZDdlN2E2ODdhYTg1MDQxZGU2MzY3MTJkNGYwMjI2MzJkZDUzNTZjODgwNTIxYWYyNzIzYSJ9fX0=
  #   slot: 50
  #   display_name: '&fTrang kế'
  #   lore:
  #   - ''
  'pre':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvN2EyYzEyY2IyMjkxODM4NGUwYTgxYzgyYTFlZDk5YWViZGNlOTRiMmVjMjc1NDgwMDk3MjMxOWI1NzkwMGFmYiJ9fX0=
    slot: 48
    display_name: '&fTrang trước'
    lore:
    - ''
    click_commands:
    - '[openguimenu] rank'


  'vip':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZjUzNjhhNjZiY2M2MzFiZWZkOTk4ZmRlMWY2ZDAyZTVmNGY1Mzc4N2FlZjkzNzIxNWQwOGRjMTkxYWNlZjZkYSJ9fX0=
    priority: 0
    slot: 20
    display_name: '&#F6FF5FRank &#F6FF5Fᴠɪᴘ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fC<PERSON> tất cả quyền lợi của rank free'
    - ' &#B54BF3● &fMở khoá 8 slot vault.'
    - ' &#B54BF3● &fXem được kho đồ người khác &7[&e/invsee tên&7].'
    - ' &#B54BF3● &fĐội vật phẩm lên đầu &7[&e/hat&7].'
    - ' &#B54BF3● &fĐược dùng lệnh heal &7[&e/heal&7].'
    - ' &#B54BF3● &fĐược 4 slot &7[&e/sethome&7].'
    - ' &#B54BF3● &fTăng &b+500.000 &fSlot &7[&e/Kho]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.vip
  'vip-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZjUzNjhhNjZiY2M2MzFiZWZkOTk4ZmRlMWY2ZDAyZTVmNGY1Mzc4N2FlZjkzNzIxNWQwOGRjMTkxYWNlZjZkYSJ9fX0=
    priority: 1
    slot: 20
    display_name: '&#F6FF5FRank &#F6FF5Fᴠɪᴘ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank free'
    - ' &#B54BF3● &fMở khoá 8 slot vault.'
    - ' &#B54BF3● &fXem được kho đồ người khác &7[&e/invsee tên&7].'
    - ' &#B54BF3● &fĐội vật phẩm lên đầu &7[&e/hat&7].'
    - ' &#B54BF3● &fĐược dùng lệnh heal &7[&e/heal&7].'
    - ' &#B54BF3● &fĐược 4 slot &7[&e/sethome&7].'
    - ' &#B54BF3● &fTăng &b+500.000 &fSlot &7[&e/Kho]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ 150 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 150'
    - '[console] esadmin addspace 500000 %player_name%'
    - '[console] lp user %player_name% promote rank-donator'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.vip
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '150'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'
  'vipplus':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZjUzNjhhNjZiY2M2MzFiZWZkOTk4ZmRlMWY2ZDAyZTVmNGY1Mzc4N2FlZjkzNzIxNWQwOGRjMTkxYWNlZjZkYSJ9fX0=
    priority: 0
    slot: 21
    display_name: '&#F6FF5FRank &#F4FF37ᴠɪ&#FFE049ᴘ+'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 10 slot vault.'
    - ' &#B54BF3● &fTăng &b+700.000 &fSlot &7[&e/Kho&7]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.vipplus
  'vipplus-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZjUzNjhhNjZiY2M2MzFiZWZkOTk4ZmRlMWY2ZDAyZTVmNGY1Mzc4N2FlZjkzNzIxNWQwOGRjMTkxYWNlZjZkYSJ9fX0=
    priority: 1
    slot: 21
    display_name: '&#F6FF5FRank &#F4FF37ᴠɪ&#FFE049ᴘ+'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 10 slot vault.'
    - ' &#B54BF3● &fTăng &b+700.000 &fSlot &7[&e/Kho&7]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ Rank trước đó'
    - ' &#B54BF3✦ 300 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 300'
    - '[console] lp user %player_name% promote rank-donator'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.vipplus
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '300'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'


  'mvp':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZWRkNjI0ZmI4MmE1NWQ0MDAyMDI2NzJiNzFhNDlmNGFiZjg1OGE2YjAxNTI0Y2NhMjQ3ZmFkNTdlZTFlMzBlMiJ9fX0=
    priority: 0
    slot: 22
    display_name: '&#F6FF5FRank &b&lᴍᴠᴘ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 12 slot vault.'
    - ' &#B54BF3● &fĐược 5 slot &7[&e/sethome&7].'
    - ' &#B54BF3● &fTăng &b+1.400.000 &fSlot &7[&e/Kho&7]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.mvp
  'mvp-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZWRkNjI0ZmI4MmE1NWQ0MDAyMDI2NzJiNzFhNDlmNGFiZjg1OGE2YjAxNTI0Y2NhMjQ3ZmFkNTdlZTFlMzBlMiJ9fX0=
    priority: 1
    slot: 22
    display_name: '&#F6FF5FRank &b&lᴍᴠᴘ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 12 slot vault.'
    - ' &#B54BF3● &fĐược 5 slot &7[&e/sethome&7].'
    - ' &#B54BF3● &fTăng &b+1.400.000 &fSlot &7[&e/Kho&7]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ Rank trước đó'
    - ' &#B54BF3✦ 500 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 500'
    - '[console] lp user %player_name% promote rank-donator'
    - '[console] esadmin addspace 1400000 %player_name%'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.mvp
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '500'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'


  'mvpplus':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZWRkNjI0ZmI4MmE1NWQ0MDAyMDI2NzJiNzFhNDlmNGFiZjg1OGE2YjAxNTI0Y2NhMjQ3ZmFkNTdlZTFlMzBlMiJ9fX0=
    priority: 0
    slot: 23
    display_name: '&#F6FF5FRank &b&lᴍᴠᴘ+'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 14 slot vault.'
    - ' &#B54BF3● &fTăng &b+1.800.000 &fSlot &7[&e/Kho]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.mvpplus
  'mvpplus-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZWRkNjI0ZmI4MmE1NWQ0MDAyMDI2NzJiNzFhNDlmNGFiZjg1OGE2YjAxNTI0Y2NhMjQ3ZmFkNTdlZTFlMzBlMiJ9fX0=
    priority: 1
    slot: 23
    display_name: '&#F6FF5FRank &b&lᴍᴠᴘ+'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 14 slot vault.'
    - ' &#B54BF3● &fTăng &b+1.800.000 &fSlot &7[&e/Kho]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ Rank trước đó'
    - ' &#B54BF3✦ 800 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 800'
    - '[console] lp user %player_name% promote rank-donator'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.mvpplus
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '800'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'
#====================================================================#
  #                           ELITE RANKS                            #
  #====================================================================#

  'elite':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvOGEzY2RiNzNmMTIzYmY1MTkzOTI4ZjM3M2EyYjVlYzdlNjdlNmQ2ZWEwN2NlMjNkMzc1ZDc4OWMxZGQ2Mjc0OSJ9fX0=
    priority: 0
    slot: 24
    display_name: '&#A7F5FFRank &#33FFFCE&lʟɪᴛᴇ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 16 slot vault.'
    - ' &#B54BF3● &fĐược dùng bàn chế tạo ảo &7[&e/craft&7].'
    - ' &#B54BF3● &fTăng &b+2.000.000 &fSlot &7[&e/Kho]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.elite
  'elite-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvOGEzY2RiNzNmMTIzYmY1MTkzOTI4ZjM3M2EyYjVlYzdlNjdlNmQ2ZWEwN2NlMjNkMzc1ZDc4OWMxZGQ2Mjc0OSJ9fX0=
    priority: 1
    slot: 24
    display_name: '&#A7F5FFRank &#33FFFCE&lʟɪᴛᴇ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 16 slot vault.'
    - ' &#B54BF3● &fĐược dùng bàn chế tạo ảo &7[&e/craft&7].'
    - ' &#B54BF3● &fTăng &b+2.000.000 &fSlot &7[&e/Kho]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ Rank trước đó'
    - ' &#B54BF3✦ 1200 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 1200'
    - '[console] esadmin addspace 2000000 %player_name%'
    - '[console] lp user %player_name% promote rank-donator'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công rank Elite!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.elite
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '1200'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'

  'eliteplus':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvOGEzY2RiNzNmMTIzYmY1MTkzOTI4ZjM3M2EyYjVlYzdlNjdlNmQ2ZWEwN2NlMjNkMzc1ZDc4OWMxZGQ2Mjc0OSJ9fX0=
    priority: 0
    slot: 25
    display_name: '&#A7F5FFRank &#33FFFCE&lʟɪᴛᴇ&6&l+'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 18 slot vault.'
    - ' &#B54BF3● &fTăng &b+2.500.000 &fSlot &7[&e/Kho]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.eliteplus
  'eliteplus-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvOGEzY2RiNzNmMTIzYmY1MTkzOTI4ZjM3M2EyYjVlYzdlNjdlNmQ2ZWEwN2NlMjNkMzc1ZDc4OWMxZGQ2Mjc0OSJ9fX0=
    priority: 1
    slot: 25
    display_name: '&#A7F5FFRank &#33FFFCE&lʟɪᴛᴇ&6&l+'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 18 slot vault.'
    - ' &#B54BF3● &fTăng &b+2.500.000 &fSlot &7[&e/Kho]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ Rank trước đó'
    - ' &#B54BF3✦ 1700 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 1700'
    - '[console] esadmin addspace 2500000 %player_name%'
    - '[console] lp user %player_name% promote rank-donator'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công rank Elite+!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.eliteplus
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '1700'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'

  #====================================================================#
  #                         SUPERIOR RANKS                           #
  #====================================================================#

  'superior':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvMTNkNmQ1ZGMwZDcwNTg5YjgyNjNiMjUzNjdjNjExOTI5YzdiYTYwNzc3MWMxY2E3M2E2MDcxYjg4NWNhZTg2YyJ9fX0=
    priority: 0
    slot: 29
    display_name: '&#43FFAERank &#43FFAESᴜᴘᴇʀɪᴏʀ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 20 slot vault.'
    - ' &#B54BF3● &fĐược 6 slot &7[&e/sethome&7].'
    - ' &#B54BF3● &fTăng &b+3.500.000 &fSlot &7[&e/Kho]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.superior
  'superior-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvMTNkNmQ1ZGMwZDcwNTg5YjgyNjNiMjUzNjdjNjExOTI5YzdiYTYwNzc3MWMxY2E3M2E2MDcxYjg4NWNhZTg2YyJ9fX0=
    priority: 1
    slot: 29
    display_name: '&#43FFAERank &#43FFAESᴜᴘᴇʀɪᴏʀ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 20 slot vault.'
    - ' &#B54BF3● &fĐược 6 slot &7[&e/sethome&7].'
    - ' &#B54BF3● &fTăng &b+3.500.000 &fSlot &7[&e/Kho]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ Rank trước đó'
    - ' &#B54BF3✦ 2500 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 2500'
    - '[console] esadmin addspace 3500000 %player_name% '
    - '[console] lp user %player_name% promote rank-donator'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công rank Superior!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.superior
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '2500'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'

  'superiorplus':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvMTNkNmQ1ZGMwZDcwNTg5YjgyNjNiMjUzNjdjNjExOTI5YzdiYTYwNzc3MWMxY2E3M2E2MDcxYjg4NWNhZTg2YyJ9fX0=
    priority: 0
    slot: 30
    display_name: '&#43FFAERank &#43FFAESᴜᴘᴇʀɪᴏʀ&6&l+'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 22 slot vault.'
    - ' &#B54BF3● &fTăng &b+5.000.000 &fSlot &7[&e/Kho]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.superiorplus
  'superiorplus-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvMTNkNmQ1ZGMwZDcwNTg5YjgyNjNiMjUzNjdjNjExOTI5YzdiYTYwNzc3MWMxY2E3M2E2MDcxYjg4NWNhZTg2YyJ9fX0=
    priority: 1
    slot: 30
    display_name: '&#43FFAERank &#43FFAESᴜᴘᴇʀɪᴏʀ&6&l+'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 22 slot vault.'
    - ' &#B54BF3● &fTăng &b+5.000.000 &fSlot &7[&e/Kho]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ Rank trước đó'
    - ' &#B54BF3✦ 3500 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 3500'
    - '[console] esadmin addspace 5000000 %player_name% '
    - '[console] lp user %player_name% promote rank-donator'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công rank Superior+!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.superiorplus
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '3500'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'
          
  #====================================================================#
  #                          SUPREME RANK                            #
  #====================================================================#

  'supreme':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZDk2ZWM4MTQ3YzFkNjlhODc5MWJjM2MyMmY3ZWJkODJkODgzY2JjM2QxNjY3YWVjMmZlZWUzNjg3NzRkZjVmZSJ9fX0=
    priority: 0
    slot: 31
    display_name: '&#FF4747Rank &#FFC947Sᴜᴘʀᴇᴍᴇ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 25 slot vault.'
    - ' &#B54BF3● &fĐược 7 slot &7[&e/sethome&7].'
    - ' &#B54BF3● &fĐược bay ở khu vực an toàn &7[&e/fly&7].'
    - ' &#B54BF3● &fTăng &cVô Hạn &fSlot &7[&e/Kho]'
    - ''
    - '&8[&#B54BF3✔&8] &#CA70FFBạn đã có rank này rồi!'
    view_requirement:
      requirements:
        permission:
          type: 'has permission'
          permission: group.supreme
  'supreme-1':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZDk2ZWM4MTQ3YzFkNjlhODc5MWJjM2MyMmY3ZWJkODJkODgzY2JjM2QxNjY3YWVjMmZlZWUzNjg3NzRkZjVmZSJ9fX0=
    priority: 1
    slot: 31
    display_name: '&#FF4747Rank &#FFC947Sᴜᴘʀᴇᴍᴇ'
    lore:
    - ''
    - '&#B54BF3○ Qᴜʏềɴ ʟợɪ:'
    - ' &#B54BF3● &fCó tất cả quyền lợi của rank trước đó'
    - ' &#B54BF3● &fMở khoá 25 slot vault.'
    - ' &#B54BF3● &fĐược 7 slot &7[&e/sethome&7].'
    - ' &#B54BF3● &fĐược bay ở khu vực an toàn &7[&e/fly&7].'
    - ' &#B54BF3● &fTăng &cVô Hạn &fSlot &7[&e/Kho]'
    - ''
    - '&#B54BF3● Yêu cầu:'
    - ' &#B54BF3✦ Rank trước đó'
    - ' &#B54BF3✦ 5000 soul, &fđang có &r&5%playerpoints_points% &f%img_purple_orb%'
    - '&8[&#B54BF3✔&8] &#CA70FFNhấn &fđể mua'
    click_commands:
    - '[console] points take %player_name% 5000'
    - '[console] esadmin addspace 10000000 %player_name%'
    - '[console] lp user %player_name% promote rank-donator'
    - '[message] &8◇ &5Tiện &fích &f⇨ &aBạn đã mua thành công rank Supreme!'
    - '[close]'
    click_requirement:
      requirements:
        permission:
          type: '!has permission'
          permission: group.supreme
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn đã có rank này!'
        point:
          type: '>='
          input: '%playerpoints_points%'
          output: '5000'
          deny_commands:
          - '[close]'
          - '[sound] BLOCK_ANVIL_LAND'
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không có đủ soul!'
