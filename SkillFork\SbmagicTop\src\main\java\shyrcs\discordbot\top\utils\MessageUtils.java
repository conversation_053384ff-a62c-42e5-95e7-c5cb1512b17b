package shyrcs.discordbot.top.utils;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class MessageUtils {
    
    private static final Map<String, Color> COLOR_MAP = new HashMap<>();
    private static final Pattern COLOR_PATTERN = Pattern.compile("&[0-9a-fk-or]|§[0-9a-fk-or]");
    
    static {
        // Minecraft color codes to Discord colors
        COLOR_MAP.put("0", Color.BLACK);
        COLOR_MAP.put("1", Color.BLUE);
        COLOR_MAP.put("2", Color.GREEN);
        COLOR_MAP.put("3", Color.CYAN);
        COLOR_MAP.put("4", Color.RED);
        COLOR_MAP.put("5", Color.MAGENTA);
        COLOR_MAP.put("6", Color.ORANGE);
        COLOR_MAP.put("7", Color.LIGHT_GRAY);
        COLOR_MAP.put("8", Color.GRAY);
        COLOR_MAP.put("9", Color.BLUE);
        COLOR_MAP.put("a", Color.GREEN);
        COLOR_MAP.put("b", Color.CYAN);
        COLOR_MAP.put("c", Color.RED);
        COLOR_MAP.put("d", Color.MAGENTA);
        COLOR_MAP.put("e", Color.YELLOW);
        COLOR_MAP.put("f", Color.WHITE);
    }
    
    /**
     * Chuyển đổi Minecraft color codes thành text thuần
     */
    public static String stripColors(String text) {
        if (text == null) return "";
        return COLOR_PATTERN.matcher(text).replaceAll("");
    }
    
    /**
     * Chuyển đổi Minecraft formatting thành Discord markdown
     */
    public static String convertToDiscordMarkdown(String text) {
        if (text == null) return "";
        
        // Xóa color codes trước
        text = stripColors(text);
        
        // Chuyển đổi formatting codes
        text = text.replaceAll("&l|§l", "**"); // Bold
        text = text.replaceAll("&o|§o", "*"); // Italic
        text = text.replaceAll("&n|§n", "__"); // Underline
        text = text.replaceAll("&m|§m", "~~"); // Strikethrough
        text = text.replaceAll("&r|§r", ""); // Reset
        
        return text;
    }
    
    /**
     * Format số với đơn vị K, M, B
     */
    public static String formatNumber(long number) {
        if (number >= 1_000_000_000) {
            return String.format("%.1fB", number / 1_000_000_000.0);
        } else if (number >= 1_000_000) {
            return String.format("%.1fM", number / 1_000_000.0);
        } else if (number >= 1_000) {
            return String.format("%.1fK", number / 1_000.0);
        } else {
            return String.valueOf(number);
        }
    }
    
    /**
     * Format thời gian từ phút thành định dạng dễ đọc
     */
    public static String formatTime(long minutes) {
        if (minutes >= 525600) { // 1 năm
            return String.format("%.1f năm", minutes / 525600.0);
        } else if (minutes >= 43200) { // 1 tháng
            return String.format("%.1f tháng", minutes / 43200.0);
        } else if (minutes >= 1440) { // 1 ngày
            return String.format("%.1f ngày", minutes / 1440.0);
        } else if (minutes >= 60) { // 1 giờ
            return String.format("%.1f giờ", minutes / 60.0);
        } else {
            return minutes + " phút";
        }
    }
    
    /**
     * Tạo progress bar ASCII
     */
    public static String createProgressBar(double percentage, int length) {
        int filled = (int) (percentage * length / 100);
        StringBuilder bar = new StringBuilder();
        
        bar.append("▓".repeat(Math.max(0, filled)));
        bar.append("░".repeat(Math.max(0, length - filled)));
        
        return bar.toString();
    }
    
    /**
     * Validate Discord embed limits
     */
    public static String validateEmbedText(String text, int maxLength) {
        if (text == null) return "";
        
        if (text.length() > maxLength) {
            return text.substring(0, maxLength - 3) + "...";
        }
        
        return text;
    }
    
    /**
     * Escape Discord markdown characters
     */
    public static String escapeMarkdown(String text) {
        if (text == null) return "";
        
        return text.replaceAll("([*_`~\\\\|])", "\\\\$1");
    }
    
    /**
     * Tạo emoji rank dựa trên vị trí
     */
    public static String getRankEmoji(int rank) {
        switch (rank) {
            case 1: return "🥇";
            case 2: return "🥈";
            case 3: return "🥉";
            case 4: return "4️⃣";
            case 5: return "5️⃣";
            case 6: return "6️⃣";
            case 7: return "7️⃣";
            case 8: return "8️⃣";
            case 9: return "9️⃣";
            case 10: return "🔟";
            default: return String.valueOf(rank);
        }
    }
}
