Skill_1HOATHECHELINH: 
  Skills:
  - teleport{spreadh=3;spreadv=0} @target
  - delay 10
  - projectile{onTick=Skill_1HOATHECHELINH-Tick;onHit=Skill_1HOATHECHELINH-Hit;oe=Skill_1HOATHECHELINH-End;v=3;i=0.2;hR=1;vR=1;r=2;d=2;hnp=true} @targetLocation
Skill_1HOATHECHELINH-Tick:
  Skills:
  - effect:particleRing{p=sweep_attack;amount=5;speed=2;hS=1;vS=1;r=0.2} @origin
  - effect:particleRing{particle=reddust;color=#F992FF;amount=5;speed=2;hS=1;vS=1;r=0.2} @origin
Skill_1HOATHECHELINH-Hit:
  Skills:
  - damage{a=30}
  - damagepercent{percent=0.15}
  - potion{type=HARM;duration=40;lvl=2}
  - effect:particles{p=block_crack;m=redstone_block;amount=150;speed=0;hS=0.4;vS=0.4;y=0.5} @target
Skill_1HOATHECHELINH-End:
  Skills:
  - effect:particles{p=block_crack;m=redstone_block;amount=150;speed=0;hS=0.4;vS=0.4;y=0.5} @EntitiesInRadius{r=4}
  - lightning{d=50} @EntitiesInRadius{r=4} 
    
Skill_2HOATHECHELINH:
  Skills:
  - teleport{spreadh=10;spreadv=0} @target
  - prison{material=LAVA;duration=20} @target

Skill_3HOATHECHELINH:
  Skills:
  - spring{d=100;t=LAVA} @EntitiesInRadius{r=30}

Skill_4HOATHECHELINH:
  Skills:
  - stun{d=100} @self
  - projectile{onTick=Skill_4HOATHECHELINH-Tick;onHit=Skill_4HOATHECHELINH-Hit;oe=Skill_4HOATHECHELINH-End;v=3;i=0.2;hR=1;vR=1;r=2;d=2;hnp=true;repeat=3;repeati=15} @targetLocation
Skill_4HOATHECHELINH-Tick:
  Skills:
  - effect:guardianbeam{d=100;y=2} @EntitiesInRadius{r=20} 
Skill_4HOATHECHELINH-Hit:
  Skills:
  - damage{a=30}
  - damagepercent{percent=0.15}
  - potion{type=HARM;duration=40;lvl=2}
  - effect:particles{p=block_crack;m=redstone_block;amount=150;speed=0;hS=0.4;vS=0.4;y=0.5} @target
Skill_4HOATHECHELINH-End:
  Skills:
  - delay 100
  - effect:particles{p=block_crack;m=redstone_block;amount=150;speed=0;hS=0.4;vS=0.4;y=0.5} @EntitiesInRadius{r=4}
  - lightning{d=50} @EntitiesInRadius{r=20} 
Skill_5HOATHECHELINH:
  Skills:
  - potion{type=INVISIBILITY;duration=40;lvl=2} @self
  - effect:particlelinering{p=cloud;a=5;speed=2} @self
  - teleport{spreadh=10;spreadv=0} @target
  - leap{velocity=500} @target
  - jump{velocity=-100}
  - slash{y=1.8;w=4;h=2;mpd=true;a=10;oP=[ - effect:particles{particle=reddust;color=#F992FF} ];roll=-45to45}
  - slash{y=1.8;w=4;h=2;mpd=true;a=20;oP=[ - effect:particles{particle=reddust;color=#F992FF} ];roll=-75to15}
  - delay 1
  - slash{y=1.8;w=4;h=2;mpd=true;a=30;oP=[ - effect:particles{particle=reddust;color=#F992FF} ];roll=-45to45}
  - slash{y=1.8;w=4;h=2;mpd=true;a=40;oP=[ - effect:particles{particle=reddust;color=#F992FF} ];roll=-75to15}
  - delay 1
  - slash{y=1.8;w=4;h=2;mpd=true;a=50;oP=[ - effect:particles{particle=reddust;color=#F992FF} ];roll=-45to45}
  - slash{y=1.8;w=4;h=2;mpd=true;a=60;oP=[ - effect:particles{particle=reddust;color=#F992FF} ];roll=-75to15}
  - delay 1
  - slash{y=1.8;w=4;h=2;mpd=true;a=70;oP=[ - effect:particles{particle=reddust;color=#F992FF} ];roll=-45to45}
  - slash{y=1.8;w=4;h=2;mpd=true;a=80;oP=[ - effect:particles{particle=reddust;color=#F992FF} ];roll=-75to15}
  - damagepercent{percent=0.15}
  - ignite{d=100} @target
  - damage{a=30}

Skill_6HOATHECHELINH:
  Skills:
  - delay 34
  - projectile{ot=Skill_6HOATHECHELINH-1;oe=Skill_6HOATHECHELINH-2;v=8;i=1;hR=1;vR=1;i=1;mr=10;md=100;hnp=true;sso=-1;syo=2;repeat=3;repeati=15} @RingAroundOrigin{yoffset=6;points=10;radius=6}
  - effect:particlesphere{p=cloud;amount=2;radius=0.4;yOffset=0;r=2} @forward{a=0;sideOffset=-1;y=2}
  - delay 26
Skill_6HOATHECHELINH-1:
  Skills:
  - effect:particlelinering{p=cloud;a=1} @origin
  - effect:sound{s=block.amethyst_block.hit;p=1.4;v=3} @origin 0.1
Skill_6HOATHECHELINH-2:
  Skills:
  - missile{ot=Skill_6HOATHECHELINH-4;oh=Skill_6HOATHECHELINH-3;v=20;i=1;hnp=true;hR=0.1;vR=0.1;i=1;mr=60;md=100;fo=true} @EIR{r=30;sort=RANDOM}
Skill_6HOATHECHELINH-3:
  Skills:
  - damage{a=30}
  - damagepercent{percent=0.05}
  - potion{type=Blindness;duration=140;lvl=5}
  - effect:particlelinering{p=smoke;amount=2;radius=0.4;yOffset=0} origin
Skill_6HOATHECHELINH-4:
  Skills:
  - effect:particlering{particle=reddust;color=#F992FF;a=2;v=20} @origin

Skill_7HOATHECHELINH: 
  Skills:
  - projectile{onTick=Skill_7HOATHECHELINH-Tick;onHit=Skill_7HOATHECHELINH-Hit;oe=Skill_7HOATHECHELINH-end;v=20;i=2;hR=1;vR=1;hnp=true;repeat=3;repeati=15} @targetLocation
Skill_7HOATHECHELINH-Tick:
  Skills:
  - effect:particlesphere{p=block;material=ice;amount=400;speed=0;hS=0.2;vS=0.2;r=1} @origin
Skill_7HOATHECHELINH-Hit:
  Skills:
  - damage{a=30}
  - damagepercent{percent=0.15}
  - potion{type=SLOW;duration=40;lvl=10}
Skill_7HOATHECHELINH-end:
  Skills:
  - explosion{yield=3} 

Skill_8HOATHECHELINH: 
  Skills:
  - projectile{onTick=Skill_8HOATHECHELINH-Tick;onHit=Skill_8HOATHECHELINH-Hit;oe=Skill_8HOATHECHELINH-end;v=20;i=2;hR=1;vR=1;hnp=true;repeat=3;repeati=15} @targetLocation
Skill_8HOATHECHELINH-Tick:
  Skills:
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=400;speed=0;hS=0.2;vS=0.2;r=1} @origin
Skill_8HOATHECHELINH-Hit:
  Skills:
  - damage{a=30}
  - damagepercent{percent=0.15}
  - ignite{d=400}
Skill_8HOATHECHELINH-end:
  Skills:
  - explosion{yield=3} 

Skill_9HOATHECHELINH: 
  Skills:
  - projectile{onTick=Skill_9HOATHECHELINH-Tick;onHit=Skill_9HOATHECHELINH-Hit;oe=Skill_9HOATHECHELINH-end;v=20;i=2;hR=1;vR=1;sE=false;hnp=true;repeat=3;repeati=15} @targetLocation
Skill_9HOATHECHELINH-Tick:
  Skills:
  - effect:particlesphere{p=end_rod;amount=400;speed=0;hS=0.2;vS=0.2;r=1} @origin
Skill_9HOATHECHELINH-Hit:
  Skills:
  - damage{a=30}
  - damagepercent{percent=0.15}
  - lightning{d=50}
Skill_9HOATHECHELINH-end:
  Skills:
  - explosion{yield=3} 

Skill_10HOATHECHELINH:
  Skills:
  - skill{s=Skill_8HOATHECHELINH} @Target
  - skill{s=Skill_9HOATHECHELINH} @Target
  - skill{s=Skill_7HOATHECHELINH} @Target

Skill_11HOATHECHELINH:
  Cooldown: 1200
  Skills:
  - effect:particlering{particle=reddust;color=#F992FF;amount=40;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=5;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=10;hS=0.2;vS=0.2;r=5} @self
  - ignite{d=200} @EIR{r=5}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=40;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=10;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=10;hS=0.2;vS=0.2;r=10} @self
  - ignite{d=200} @EIR{r=10}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=40;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=15;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=10;hS=0.2;vS=0.2;r=15} @self
  - ignite{d=200} @EIR{r=15}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=40;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=20;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=10;hS=0.2;vS=0.2;r=20} @self
  - ignite{d=200} @EIR{r=20}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=40;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=25;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=10;hS=0.2;vS=0.2;r=25} @self
  - ignite{d=200} @EIR{r=25}
  - explosion{yield=80;ft=true} @origin 
  - damagepercent{percent=0.65} @EIR{r=40}
  - damage{a=50}
Skill_12HOATHECHELINH:
  Cooldown: 3600
  Skills:
  - summon{type=hoathechilinhtinhlinh;a=20;r=15} @self
  - delay 100
  - skill{s=Skill_12HOATHECHELINH_1}
  - consume{d=10000;h=20000} @MobsInRadius{type=hoathechilinhtinhlinh;r=30;sort=RANDOM}
  - delay 100
  - skill{s=Skill_12HOATHECHELINH_1}
  - consume{d=10000;h=20000} @MobsInRadius{type=hoathechilinhtinhlinh;r=40;sort=RANDOM}
  - delay 100
  - skill{s=Skill_12HOATHECHELINH_1}
  - consume{d=10000;h=200000} @MobsInRadius{type=hoathechilinhtinhlinh;r=90;sort=RANDOM}
  - delay 100
  - skill{s=Skill_12HOATHECHELINH_1}
  - consume{d=10000;h=200000} @MobsInRadius{type=hoathechilinhtinhlinh;r=140}
Skill_12HOATHECHELINH_1:
  Skills:
  - pull{v=3;to=true} @MobsInRadius{type=hoathechilinhtinhlinh;r=50;sort=RANDOM}
  - effect:particlering{p=end_rod;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlesphere{p=end_rod;amount=1000;hS=0.2;vS=0.2;r=20} @self
  - lightning{d=10} @MobsInRadius{type=hoathechilinhtinhlinh;r=40}
Skill_13HOATHECHELINH:
  Cooldown: 3600
  Skills:
  - summon{type=hoathechilinhtinhlinh;a=20;r=15} @self
  - delay 100
  - skill{s=Skill_12HOATHECHELINH_1}
  - consume{d=10000;h=20000} @MobsInRadius{type=hoathechilinhtinhlinh;r=30;sort=RANDOM}
  - delay 100
  - skill{s=Skill_12HOATHECHELINH_1}
  - consume{d=10000;h=20000} @MobsInRadius{type=hoathechilinhtinhlinh;r=40;sort=RANDOM}
  - delay 100
  - skill{s=Skill_12HOATHECHELINH_1}
  - consume{d=10000;h=200000} @MobsInRadius{type=hoathechilinhtinhlinh;r=90;sort=RANDOM}
  - delay 100
  - skill{s=Skill_12HOATHECHELINH_1}
  - consume{d=10000;h=200000} @MobsInRadius{type=hoathechilinhtinhlinh;r=140}
Skill_13HOATHECHELINH_1:
  Skills:
  - pull{v=3;to=true} @MobsInRadius{type=hoathechilinhtinhlinh;r=50;sort=RANDOM}
  - effect:particlering{p=end_rod;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlesphere{p=end_rod;amount=1000;hS=0.2;vS=0.2;r=20} @self
  - lightning{d=10} @MobsInRadius{type=hoathechilinhtinhlinh;r=40}
  
Skill_15HOATHECHELINH:
  Skills:
  - summon{type=hoathechilinhtinhlinhlua;a=5;r=15} @self
Skill_16HOATHECHELINH:
  Skills:
  - summon{type=hoathechilinhtinhlinhbang;a=5;r=15} @self
Skill_17HOATHECHELINH:
  Skills:
  - summon{type=hoathechilinhtinhlinhset;a=5;r=15} @self
Skill_18HOATHECHELINH:
  Skills:
  - throw{velocity=5;velocityY=5} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - effect:particles{p=hugeexplode;a=5;vs=0.5;hs=0.5;s=0;y=1} @Self
  - effect:particlering{particle=reddust;color=#F992FF;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=1} @Self
  - delay 2
  - effect:particlering{particle=reddust;color=#F992FF;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=3} @Self
  - delay 2
  - effect:particlering{particle=reddust;color=#F992FF;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=5} @Self
  - delay 2 
  - effect:particlering{particle=reddust;color=#F992FF;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=7} @Self
  - delay 2
  - effect:particlering{particle=reddust;color=#F992FF;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=9} @Self
  - delay 2
  - effect:particlering{particle=reddust;color=#F992FF;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=11} @Self
Skill_19HOATHECHELINH:
  Skills:
  - throw{velocity=5;velocityY=5} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - effect:particles{p=hugeexplode;a=5;vs=0.5;hs=0.5;s=0;y=1} @Self
  - effect:particlering{p=block;material=ice;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=1} @Self
  - delay 2
  - effect:particlering{p=block;material=ice;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=3} @Self
  - delay 2
  - effect:particlering{p=block;material=ice;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=5} @Self
  - delay 2 
  - effect:particlering{p=block;material=ice;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=7} @Self
  - delay 2
  - effect:particlering{p=block;material=ice;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=9} @Self
  - delay 2
  - effect:particlering{p=block;material=ice;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=11} @Self
Skill_20HOATHECHELINH:
  Skills:
  - throw{velocity=5;velocityY=5} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - effect:particles{p=hugeexplode;a=5;vs=0.5;hs=0.5;s=0;y=1} @Self
  - effect:particlering{p=cloud;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=1} @Self
  - lightning{d=50} @EntitiesInRadius{r=1} 
  - delay 2
  - effect:particlering{p=cloud;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=3} @Self
  - lightning{d=50} @EntitiesInRadius{r=3} 
  - delay 2
  - effect:particlering{p=cloud;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=5} @Self
  - lightning{d=50} @EntitiesInRadius{r=5} 
  - delay 2 
  - effect:particlering{p=cloud;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=7} @Self
  - lightning{d=50} @EntitiesInRadius{r=7} 
  - delay 2
  - effect:particlering{p=cloud;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=9} @Self
  - lightning{d=50} @EntitiesInRadius{r=9} 
  - delay 2
  - effect:particlering{p=cloud;a=40;vs=0.5;hs=0.5;s=0;y=0.3;points=20;radius=11} @Self
  - lightning{d=50} @EntitiesInRadius{r=11} 
effecthoathe:
  Skills:
  - projectile{onTick=effecthoathe-tick;v=2;i=1;hR=3;vR=3;;sE=false;hs=true;hnp=true;d=20} @self
effecthoathe-tick:
  Skills:
  - effect:particlelinering{particle=flame;amount=30;rp=30;d=1;rr=5;fromOrigin=true;yt=20;y=-1;s=2} @Selflocation
  - delay 1
  - effect:particlelinering{particle=flame;amount=30;rp=30;d=1;rr=5;fromOrigin=true;yt=20;y=-1;s=2} @Selflocation
  - delay 1
  - effect:particlelinering{particle=flame;amount=30;rp=30;d=1;rr=5;fromOrigin=true;yt=20;y=-1;s=2} @Selflocation
Skill_21HOATHECHELINH:
  Skills:
  - summon{type=hoathechilinhtinhlinhthancan;a=15;r=15} @self
Skill_22HOATHECHELINH:
  Skills:
  - explosion{yield=8;ft=true} @self 
  - damagepercent{percent=0.1} @EIR{r=5}
  - damage{a=30}
Skill_23HOATHECHELINH:
  Cooldown: 60
  Skills:
  - summon{type=hoathechilinhstorm;a=1;r=1} @self
Skill_14HOATHECHELINH:
  Skills:
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=5;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=5} @self
  - ignite{d=200} @EIR{r=5}
  - damagepercent{percent=0.1} @EIR{r=5}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=10;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=10} @self
  - ignite{d=200} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=15;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=15} @self
  - ignite{d=200} @EIR{r=15}
  - damagepercent{percent=0.1} @EIR{r=15}
  - damage{a=20}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=20;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=20} @self
  - ignite{d=200} @EIR{r=20}
  - damagepercent{percent=0.1} @EIR{r=20}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=25;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=25} @self
  - ignite{d=200} @EIR{r=25}
  - damagepercent{percent=0.1} @EIR{r=25}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=25;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=25} @self
  - ignite{d=200} @EIR{r=25}
  - damagepercent{percent=0.1} @EIR{r=25}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=20;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=20} @self
  - ignite{d=200} @EIR{r=20}
  - damagepercent{percent=0.1} @EIR{r=20}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=15;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=15} @self
  - ignite{d=200} @EIR{r=15}
  - damagepercent{percent=0.1} @EIR{r=15}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=10;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=10} @self
  - ignite{d=200} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=5;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=5} @self
  - ignite{d=200} @EIR{r=5}
  - damagepercent{percent=0.1} @EIR{r=5}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=10;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=10} @self
  - ignite{d=200} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=15;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=15} @self
  - ignite{d=200} @EIR{r=15}
  - damagepercent{percent=0.1} @EIR{r=15}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=20;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=20} @self
  - ignite{d=200} @EIR{r=20}
  - damagepercent{percent=0.1} @EIR{r=20}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=25;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=25} @self
  - ignite{d=200} @EIR{r=25}
  - damagepercent{percent=0.1} @EIR{r=25}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=25;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=25} @self
  - ignite{d=200} @EIR{r=25}
  - damagepercent{percent=0.1} @EIR{r=25}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=20;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=20} @self
  - ignite{d=200} @EIR{r=20}
  - damagepercent{percent=0.1} @EIR{r=20}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=15;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=15} @self
  - ignite{d=200} @EIR{r=15}
  - damagepercent{percent=0.1} @EIR{r=15}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=10;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=10} @self
  - ignite{d=200} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=5;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=5} @self
  - ignite{d=200} @EIR{r=5}
  - damagepercent{percent=0.1} @EIR{r=5}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=10;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=10} @self
  - ignite{d=200} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=15;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=15} @self
  - ignite{d=200} @EIR{r=15}
  - damagepercent{percent=0.1} @EIR{r=15}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=20;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=20} @self
  - ignite{d=200} @EIR{r=20}
  - damagepercent{percent=0.1} @EIR{r=20}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=25;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=25} @self
  - ignite{d=200} @EIR{r=25}
  - damagepercent{percent=0.1} @EIR{r=25}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=25;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=25} @self
  - ignite{d=200} @EIR{r=25}
  - damagepercent{percent=0.1} @EIR{r=25}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=20;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=20} @self
  - ignite{d=200} @EIR{r=20}
  - damagepercent{percent=0.1} @EIR{r=20}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=15;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=15} @self
  - ignite{d=200} @EIR{r=15}
  - damagepercent{percent=0.1} @EIR{r=15}
  - damage{a=30}
  - delay 20
  - effect:particlering{particle=reddust;color=#F992FF;amount=400;speed=1;hS=0.2;vS=0.2;r=5} @self
  - effect:particlering{particle=reddust;color=#F992FF;amount=20;hS=0.2;vS=0.2;r=10;points=100} @self
  - effect:particlesphere{particle=reddust;color=#F992FF;amount=1000;hS=0.2;vS=0.2;r=10} @self
  - ignite{d=200} @EIR{r=10}
  - damagepercent{percent=0.1} @EIR{r=10}
  - damage{a=30}
  - delay 20
hoathe_bossbar_set:
  Skills:
  - barCreate{
    name="<#FF00D9>&lH<#FF29B5>&lồ<#FF5391>&ln<#FF7C6D>&lg <#FFCF24>&lH<#FFF800>&là<#FFC617>&li <#FF6344>&lN<#FF325B>&lh<#FF0072>&li";
    display="<caster.name>";
    value=1.0;color=RED;
    style=SEGMENTED_6
    } @self

hoathe_bossbar_damage:
  Conditions:
  - damagecause{c=FIRE_TICK} false
  Skills:
  - brightness{b=15;s=15} @self
  - barSet{name="<#FF00D9>&lH<#FF29B5>&lồ<#FF5391>&ln<#FF7C6D>&lg <#FFCF24>&lH<#FFF800>&là<#FFC617>&li <#FF6344>&lN<#FF325B>&lh<#FF0072>&li";display="<caster.name>";value=<caster.hp>/<caster.mhp>;color=WHITE;style=SOLID} @self
  - delay 2
  - brightness{b=-1;s=-1} @self
  - barSet{name="<#FF00D9>&lH<#FF29B5>&lồ<#FF5391>&ln<#FF7C6D>&lg <#FFCF24>&lH<#FFF800>&là<#FFC617>&li <#FF6344>&lN<#FF325B>&lh<#FF0072>&li";display="<caster.name>";value=<caster.hp>/<caster.mhp>;color=RED;style=SOLID} @self
  - delay 2
  - barSet{name="<#FF00D9>&lH<#FF29B5>&lồ<#FF5391>&ln<#FF7C6D>&lg <#FFCF24>&lH<#FFF800>&là<#FFC617>&li <#FF6344>&lN<#FF325B>&lh<#FF0072>&li";display="<caster.name>";value=<caster.hp>/<caster.mhp>;color=WHITE;style=SOLID} @self
  - delay 2
  - barSet{name="<#FF00D9>&lH<#FF29B5>&lồ<#FF5391>&ln<#FF7C6D>&lg <#FFCF24>&lH<#FFF800>&là<#FFC617>&li <#FF6344>&lN<#FF325B>&lh<#FF0072>&li";display="<caster.name>";value=<caster.hp>/<caster.mhp>;color=RED;style=SOLID} @self
