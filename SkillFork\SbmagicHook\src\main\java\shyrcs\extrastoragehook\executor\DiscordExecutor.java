package shyrcs.extrastoragehook.executor;

import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;

/**
 * Abstract class cho Discord command executors
 */
public abstract class DiscordExecutor {
    
    protected final String name;
    protected final String description;
    
    public DiscordExecutor(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    /**
     * Xử lý slash command
     */
    public abstract void onSlashCommand(SlashCommandInteractionEvent event);
    
    /**
     * Xử lý chat command
     */
    public abstract void onChatCommand(MessageReceivedEvent event);
    
    /**
     * L<PERSON>y tên command
     */
    public String getName() {
        return name;
    }
    
    /**
     * L<PERSON><PERSON> mô tả command
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Tạo success reply (có thể override)
     */
    public String buildSuccessReply(Object... params) {
        return "Lệnh đã được thực hiện thành công!";
    }
}
