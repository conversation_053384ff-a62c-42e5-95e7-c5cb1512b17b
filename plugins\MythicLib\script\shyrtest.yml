# Purple Prison Dome - Tạo lồng giam tím với damage liên tục (Fixed)
purple_prison_dome:
  public: true
  modifiers:
    - damage
    - duration
  mechanics:
    # Âm thanh kích hoạt skill
    activation_sound:
      type: sound
      sound: BLOCK_BEACON_ACTIVATE
      volume: 2
      pitch: 0.5
    
    # Particle nổ khi kích hoạt
    activation_explosion:
      type: particle
      particle: EXPLOSION_LARGE
      amount: 5
      target:
        type: caster
        position: BODY
    
    # Tạo lồng giam dome trong 7 giây
    create_prison_dome:
      type: script
      name: prison_dome_visual
      counter: second
      iterations: 7
    
    # Bắt đầu damage loop mỗi giây
    start_damage_loop:
      type: script
      name: damage_prisoners_timer
      counter: second
      iterations: 7

# Visual effect cho dome - chạy mỗi giây
prison_dome_visual:
  mechanics:
    # Tạo khung lồng với particle tím mỗi giây
    dome_frame:
      type: sphere
      radius: 15
      tick: prison_bar_particle
      points: 200
      time_interval: 1
      points_per_tick: 50
      source:
        type: caster
        position: FEET
    
    # Âm thanh môi trường của lồng giam
    ambient_sound:
      type: sound
      sound: BLOCK_CONDUIT_AMBIENT
      volume: 0.3
      pitch: 0.8
      target:
        type: caster

# Particle tạo hiệu ứng thanh lồng
prison_bar_particle:
  conditions:
    # Chỉ hiển thị ở nửa trên (dome shape)
    dome_check:
      type: boolean
      formula: "<target.y> >= <caster.y>"
    # Tạo hiệu ứng thanh lồng (chỉ hiển thị particle ở một số vị trí nhất định)
    bar_pattern:
      type: boolean
      formula: "(<target.x> - <caster.x>) % 3 < 1 || (<target.z> - <caster.z>) % 3 < 1"
  mechanics:
    # Particle tím chính
    purple_bars:
      type: particle
      particle: REDSTONE
      amount: 3
      data: 1,0,1 # RGB cho màu tím
      target:
        type: target_location
    
    # Particle enchantment cho hiệu ứng magic
    magic_effect:
      type: particle
      particle: ENCHANTMENT_TABLE
      amount: 1
      target:
        type: target_location

# Timer damage mỗi giây
damage_prisoners_timer:
  mechanics:
    # Tìm tất cả entities trong dome và gây damage
    find_prisoners:
      type: script
      name: apply_prison_damage
      target:
        type: nearby_entities
        radius: 15
        ignore_caster: false

# Áp dụng damage lên prisoners - damage xuyên giáp
apply_prison_damage:
  conditions:
    # Kiểm tra target có thể bị damage
    valid_target:
      type: can_target
    
    # Kiểm tra trong phạm vi dome
    in_dome_range:
      type: boolean
      formula: "<target.distance_to_caster> <= 15"
    
    # Kiểm tra ở nửa trên (trong dome)
    in_dome_height:
      type: boolean
      formula: "<target.y> >= <caster.y> && <target.y> <= <caster.y> + 15"
  
  mechanics:
    # Gây damage xuyên giáp bằng cách set HP trực tiếp
    prison_damage:
      type: damage
      amount: '20 + <var.damage>'
      damage_type: MAGIC,SKILL,DOT
      ignore_armor: true
      target:
        type: target
    
    # Particle khi bị damage
    damage_effect:
      type: particle
      particle: REDSTONE
      amount: 15
      data: 0.5,0,0.5 # Màu tím đậm
      target:
        type: target
        position: BODY
    
    # Âm thanh khi bị damage
    damage_sound:
      type: sound
      sound: ENTITY_PLAYER_HURT
      volume: 0.8
      pitch: 1.0
      target:
        type: target
    
    # Hiệu ứng đau đớn
    pain_particles:
      type: particle
      particle: DAMAGE_INDICATOR
      amount: 5
      target:
        type: target
        position: BODY

# Version đơn giản hơn cho hiệu suất tốt hơn - COMPLETELY REWRITTEN
purple_prison_simple:
  public: true
  modifiers:
    - damage
  mechanics:
    # Kích hoạt
    cast_sound:
      type: sound
      sound: BLOCK_BEACON_ACTIVATE
      volume: 2
      pitch: 0.5

    cast_particles:
      type: particle
      particle: EXPLOSION_LARGE
      amount: 3

    # Tạo dome liên tục trong 7 giây
    create_dome_loop:
      type: script
      name: simple_dome_loop
      counter: tick
      iterations: 140

    # Damage mỗi giây trong 7 giây
    create_damage_loop:
      type: script
      name: simple_damage_loop
      counter: tick
      iterations: 7

# Loop tạo dome particles mỗi tick trong 7 giây
simple_dome_loop:
  mechanics:
    # Tạo vòng tròn particle tím
    dome_outline:
      type: particle
      particle: REDSTONE
      amount: 8
      x: 15
      y: 8
      z: 15
      data: 1,0,1 # Màu tím
      target:
        type: caster
        position: FEET

    # Particle ở giữa dome (ít hơn để tránh lag)
    center_effect:
      type: particle
      particle: ENCHANTMENT_TABLE
      amount: 2
      target:
        type: caster
        position: BODY

    # Âm thanh mỗi 40 ticks (2 giây)
    ambient_sound:
      type: sound
      sound: BLOCK_CONDUIT_AMBIENT
      volume: 0.2
      pitch: 0.9
      target:
        type: caster

    # Delay 4 ticks giữa mỗi lần tạo particle
    wait_ticks:
      type: delay
      amount: 4

# Loop damage mỗi giây trong 7 giây
simple_damage_loop:
  mechanics:
    # Damage tất cả trong phạm vi - đơn giản và hiệu quả
    area_damage:
      type: damage
      amount: '20 + <var.damage>'
      damage_type: MAGIC,SKILL,DOT
      ignore_armor: true
      ignore_resistances: true
      target:
        type: nearby_entities
        radius: 15
        ignore_caster: false

    # Particle damage
    damage_particles:
      type: particle
      particle: REDSTONE
      amount: 20
      x: 15
      y: 8
      z: 15
      data: 0.8,0,0.8 # Màu tím đậm
      target:
        type: caster
        position: FEET

    # Âm thanh damage
    damage_sound:
      type: sound
      sound: ENTITY_PLAYER_HURT
      volume: 1
      pitch: 1.2
      target:
        type: caster

    # Chờ 20 ticks (1 giây) trước damage tiếp theo
    wait_one_second:
      type: delay
      amount: 20

# Version alternative với true damage (bypass tất cả defense)
purple_prison_truedmg:
  public: true
  modifiers:
    - damage
  mechanics:
    cast_sound:
      type: sound
      sound: BLOCK_BEACON_ACTIVATE
      volume: 2
      pitch: 0.5
    
    cast_particles:
      type: particle
      particle: EXPLOSION_LARGE
      amount: 3
    
    # Visual effect
    dome_visual:
      type: script
      name: truedmg_visual
      counter: second
      iterations: 7
    
    # True damage loop
    truedmg_loop:
      type: script
      name: truedmg_apply
      counter: second
      iterations: 7

truedmg_visual:
  mechanics:
    dome_particles:
      type: particle
      particle: REDSTONE
      amount: 50
      x: 15
      y: 10
      z: 15
      data: 0.6,0,0.9 # Màu tím
      target:
        type: caster
        position: FEET

truedmg_apply:
  mechanics:
    # Apply true damage trực tiếp
    apply_truedmg:
      type: script
      name: truedmg_target
      target:
        type: nearby_entities
        radius: 15
        ignore_caster: false

truedmg_target:
  conditions:
    valid_target:
      type: can_target
    in_range:
      type: boolean
      formula: "<target.distance_to_caster> <= 15"
  mechanics:
    # True damage - sử dụng damage với multiplier cao
    true_damage:
      type: damage
      amount: '(20 + <var.damage>) * 5'
      damage_type: MAGIC,SKILL,DOT
      ignore_armor: true
      ignore_resistances: true
      target:
        type: target
    
    # Hoặc có thể dùng command với format đúng
    # true_damage_cmd:
    #   type: command
    #   format: VANILLA
    #   command: 'damage <target.name> <20 + <var.damage>> minecraft:magic'
    #   target:
    #     type: caster
    
    # Visual effects
    truedmg_particle:
      type: particle
      particle: REDSTONE
      amount: 10
      data: 1,0,0.3 # Màu tím sáng
      target:
        type: target
        position: BODY
    
    truedmg_sound:
      type: sound
      sound: ENTITY_PLAYER_HURT
      volume: 1
      pitch: 0.8
      target:
        type: target
