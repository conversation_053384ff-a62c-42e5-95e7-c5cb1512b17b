# HyperHook - v0.2-beta
The source code of HyperHook - A fork to connect PreventHopper with Discord

# Purpose
- This plugin is mainly used with DarkEvan's PreventHopper plugin to perform a forking procedure that allows player to use their mineral storage through Discord
- The bot implementation supports both the use of chat commands and slash commands, as well as custom activities.

# Dependencies
- **PreventHopper**: The base of this entire plugin
- **JDA 5.0.0-b17**: The library used to implement the bot
- **Vault**: The economy handler (used pair-in-pair with PreventHopper)

# Usage
- If you wish to use this plugin yourself, I'd suggest compiling the plugin on your own
- All needed libraries (JARs) are contained in the libs folder (except for PreventHopper)
- No releases will be posted to this repository.
