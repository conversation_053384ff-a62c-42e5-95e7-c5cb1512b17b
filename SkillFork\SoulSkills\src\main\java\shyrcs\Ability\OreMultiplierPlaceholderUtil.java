package shyrcs.Ability;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class OreMultiplierPlaceholderUtil {
    
    /**
     * Xử lý placeholder trong lore của item
     * Thay thế {ore_multiplier_rate} và {ore_multiplier} bằng giá trị thực
     */
    public static ItemStack processPlaceholders(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return item;
        
        ItemMeta meta = item.getItemMeta();
        if (!meta.hasLore()) return item;
        
        List<String> lore = new ArrayList<>();
        if (meta.hasLore()) {
            // Sử dụng deprecated method vì chưa có alternative tốt hơn
            @SuppressWarnings("deprecation")
            List<String> legacyLore = meta.getLore();
            if (legacyLore != null) {
                lore.addAll(legacyLore);
            }
        }
        if (lore.isEmpty()) return item;
        
        // Lấy giá trị từ NBT
        NBTItem nbtItem = NBTItem.get(item);
        String rateValue = "0";
        String multiplierValue = "2";
        
        if (nbtItem.hasTag("MMOITEMS_ORE_MULTIPLIER")) {
            rateValue = String.format("%.1f", nbtItem.getDouble("MMOITEMS_ORE_MULTIPLIER"));
        }
        
        if (nbtItem.hasTag("MMOITEMS_ORE_MULTIPLIER_AMOUNT")) {
            multiplierValue = String.format("%.1f", nbtItem.getDouble("MMOITEMS_ORE_MULTIPLIER_AMOUNT"));
        }
        
        // Xử lý từng dòng lore
        List<String> newLore = new ArrayList<>();
        boolean hasChanges = false;
        
        for (String line : lore) {
            String processedLine = line;
            
            // Thay thế placeholder
            if (processedLine.contains("{ore_multiplier_rate}")) {
                processedLine = processedLine.replace("{ore_multiplier_rate}", rateValue);
                hasChanges = true;
            }
            
            if (processedLine.contains("{ore_multiplier}")) {
                processedLine = processedLine.replace("{ore_multiplier}", multiplierValue);
                hasChanges = true;
            }
            
            newLore.add(processedLine);
        }
        
        // Cập nhật lore nếu có thay đổi
        if (hasChanges) {
            // Sử dụng deprecated method vì chưa có alternative tốt hơn
            List<String> finalLore = newLore;
            meta.setLore(finalLore);
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Kiểm tra xem item có chứa placeholder không
     */
    public static boolean hasPlaceholders(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return false;

        ItemMeta meta = item.getItemMeta();
        if (!meta.hasLore()) return false;

        // Sử dụng deprecated method vì chưa có alternative tốt hơn
        @SuppressWarnings("deprecation")
        List<String> lore = meta.getLore();
        if (lore == null || lore.isEmpty()) return false;

        for (String line : lore) {
            if (line.contains("{ore_multiplier_rate}") || line.contains("{ore_multiplier}")) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Kiểm tra xem item có phải MMOItem với ore multiplier stats không
     */
    public static boolean hasOreMultiplierStats(ItemStack item) {
        if (item == null) return false;
        
        try {
            NBTItem nbtItem = NBTItem.get(item);
            return nbtItem.hasTag("MMOITEMS_ORE_MULTIPLIER") || nbtItem.hasTag("MMOITEMS_ORE_MULTIPLIER_AMOUNT");
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Lấy giá trị ore multiplier rate từ item
     */
    public static double getOreMultiplierRate(ItemStack item) {
        if (item == null) return 0;
        
        try {
            NBTItem nbtItem = NBTItem.get(item);
            if (nbtItem.hasTag("MMOITEMS_ORE_MULTIPLIER")) {
                return nbtItem.getDouble("MMOITEMS_ORE_MULTIPLIER");
            }
        } catch (Exception e) {
            // Ignore
        }
        
        return 0;
    }
    
    /**
     * Lấy giá trị ore multiplier amount từ item
     */
    public static double getOreMultiplierAmount(ItemStack item) {
        if (item == null) return 2;
        
        try {
            NBTItem nbtItem = NBTItem.get(item);
            if (nbtItem.hasTag("MMOITEMS_ORE_MULTIPLIER_AMOUNT")) {
                return nbtItem.getDouble("MMOITEMS_ORE_MULTIPLIER_AMOUNT");
            }
        } catch (Exception e) {
            // Ignore
        }
        
        return 2; // Default multiplier
    }
}
