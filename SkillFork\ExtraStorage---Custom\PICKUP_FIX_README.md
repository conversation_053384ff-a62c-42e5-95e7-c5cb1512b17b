# Sửa lỗi nhặt item không vào kho sau khi chạy lâu

## Vấn đề gốc
Plugin ExtraStorage bị lỗi nhặt item không vào kho sau khi chạy lâu, phải sử dụng `plugman unload` và `plugman load` lại plugin mới hoạt động.

## Nguyên nhân phân tích
1. **Cache bị clear hoàn toàn**: Cache `locCache` và `userCache` bị clear toàn bộ định kỳ, làm mất thông tin user đang hoạt động
2. **User data bị mất**: Không có cơ chế khôi phục khi user data bị mất khỏi cache
3. **Event handling không ổn định**: Chỉ dựa vào manual event registration, không có backup
4. **Thiếu error handling**: Không có xử lý lỗi đầy đủ khi có exception

## Các thay đổi đã thực hiện

### 1. <PERSON><PERSON><PERSON> thiện Cache Management (StorageListener.java)
**Trước:**
```java
// Clear toàn bộ cache mỗi 3-5 phút
locCache.clear();
userCache.clear();
```

**Sau:**
```java
// Chỉ xóa những entry không còn cần thiết
locCache.entrySet().removeIf(entry -> {
    User user = entry.getValue();
    return user == null || !user.isOnline();
});
```

### 2. Thêm cơ chế khôi phục User Data
```java
User user = instance.getUserManager().getUser(player);
if (user == null) {
    // Thử load lại user data nếu bị mất
    instance.getUserManager().loadUser(player.getUniqueId());
    user = instance.getUserManager().getUser(player);
    if (user == null) {
        instance.getLogger().warning("Failed to load user data for player: " + player.getName());
        return;
    }
}
```

### 3. Thêm Backup EventHandler
```java
// Backup EventHandler để đảm bảo event luôn được xử lý
@EventHandler(priority = EventPriority.LOWEST, ignoreCancelled = true)
public void onEntityPickupItemBackup(EntityPickupItemEvent event) {
    if (!event.isCancelled()) {
        onEntityPickupItem(event);
    }
}
```

### 4. Kiểm tra định kỳ User Data
```java
// Kiểm tra định kỳ để đảm bảo tất cả online players đều có user data
new BukkitRunnable() {
    @Override
    public void run() {
        for (Player player : instance.getServer().getOnlinePlayers()) {
            User user = manager.getUser(player);
            if (user == null) {
                instance.getLogger().info("Reloading missing user data for player: " + player.getName());
                manager.loadUser(player.getUniqueId());
            }
        }
    }
}.runTaskTimerAsynchronously(instance, 20L * 60 * 10, 20L * 60 * 10); // 10 phút kiểm tra 1 lần
```

### 5. Cải thiện Error Handling (VanillaPickupListener.java)
```java
try {
    // Logic xử lý pickup
    storage.add(item, amount);
    
    // Xử lý action bar với try-catch riêng
    try {
        ActionBar.send(player, message);
    } catch (Exception e) {
        instance.getLogger().warning("Failed to send action bar: " + e.getMessage());
    }
} catch (Exception e) {
    instance.getLogger().severe("Error in VanillaPickupListener.onPickup: " + e.getMessage());
    e.printStackTrace();
}
```

## Lợi ích của các thay đổi

1. **Tăng độ ổn định**: Cache không bị clear hoàn toàn, giữ lại data của user đang online
2. **Tự động khôi phục**: Plugin tự động load lại user data khi bị mất
3. **Backup mechanism**: Có 2 cách xử lý event, đảm bảo không bị miss
4. **Monitoring**: Kiểm tra định kỳ và log các vấn đề để dễ debug
5. **Error resilience**: Xử lý lỗi tốt hơn, không crash toàn bộ function

## Cách test
1. Chạy server với plugin đã sửa
2. Để server chạy liên tục trong vài giờ
3. Test nhặt item xem có vào kho không
4. Kiểm tra log để xem có warning/error nào không
5. Không cần restart plugin nữa

## Lưu ý
- Các thay đổi backward compatible, không ảnh hưởng đến tính năng hiện có
- Thêm logging để dễ debug nếu vẫn có vấn đề
- Performance impact tối thiểu do chỉ thay đổi cách quản lý cache
