package shyrcs.Ability;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

public class BuffAmountStat {
    private final double baseAmount;
    private final double multiplier;
    private final String buffType;
    private final boolean stackable;
    private final int maxStacks;
    
    public BuffAmountStat(ConfigurationSection config) {
        this.baseAmount = config.getDouble("base-amount", 1.0);
        this.multiplier = config.getDouble("multiplier", 1.0);
        this.buffType = config.getString("buff-type", "ABSORPTION");
        this.stackable = config.getBoolean("stackable", false);
        this.maxStacks = config.getInt("max-stacks", 5);
    }
    
    public BuffAmountStat(double baseAmount, double multiplier, String buffType, boolean stackable, int maxStacks) {
        this.baseAmount = baseAmount;
        this.multiplier = multiplier;
        this.buffType = buffType;
        this.stackable = stackable;
        this.maxStacks = maxStacks;
    }
    
    /**
     * Tính toán số lượng buff dựa trên level của player hoặc item
     */
    public double calculateBuffAmount(int level) {
        return baseAmount + (level * multiplier);
    }
    
    /**
     * Áp dụng buff với số lượng đã tính toán
     */
    public void applyBuff(Player player, int duration, int level) {
        PotionEffectType effectType = BuffUtils.getEffectTypeByName(buffType);
        if (effectType == null) {
            player.sendMessage("§cBuff type không hợp lệ: " + buffType);
            return;
        }

        int amplifier = calculateAmplifier(level);

        // Kiểm tra xem có thể stack không
        if (stackable && player.hasPotionEffect(effectType)) {
            PotionEffect currentEffect = player.getPotionEffect(effectType);
            if (currentEffect != null && currentEffect.getAmplifier() < maxStacks - 1) {
                amplifier = Math.min(currentEffect.getAmplifier() + 1, maxStacks - 1);
                player.sendMessage("§eBuff " + BuffPlaceholderUtil.getBuffNameVi(effectType) +
                                 " đã được stack lên level " + (amplifier + 1) + "!");
            }
        }

        PotionEffect effect = new PotionEffect(effectType, duration * 20, amplifier);
        player.addPotionEffect(effect);

        // Thông báo áp dụng buff thành công
        String buffNameVi = BuffPlaceholderUtil.getBuffNameVi(effectType);
        player.sendMessage("§aĐã áp dụng " + buffNameVi + " Level " + (amplifier + 1) +
                          " trong " + duration + " giây!");
    }
    
    /**
     * Tính toán amplifier dựa trên level
     */
    private int calculateAmplifier(int level) {
        double amount = calculateBuffAmount(level);
        return Math.max(0, (int) Math.floor(amount) - 1); // Amplifier bắt đầu từ 0
    }
    
    /**
     * Lấy thông tin buff hiện tại của player
     */
    public BuffInfo getCurrentBuffInfo(Player player) {
        PotionEffectType effectType = BuffUtils.getEffectTypeByName(buffType);
        if (effectType == null || !player.hasPotionEffect(effectType)) {
            return new BuffInfo(false, 0, 0, 0);
        }
        
        PotionEffect effect = player.getPotionEffect(effectType);
        return new BuffInfo(true, 
                           effect.getDuration() / 20, 
                           effect.getAmplifier() + 1, 
                           effect.getAmplifier());
    }
    
    // Getters
    public double getBaseAmount() {
        return baseAmount;
    }
    
    public double getMultiplier() {
        return multiplier;
    }
    
    public String getBuffType() {
        return buffType;
    }
    
    public boolean isStackable() {
        return stackable;
    }
    
    public int getMaxStacks() {
        return maxStacks;
    }
    
    /**
     * Class để lưu trữ thông tin buff
     */
    public static class BuffInfo {
        private final boolean active;
        private final int duration;
        private final int level;
        private final int amplifier;
        
        public BuffInfo(boolean active, int duration, int level, int amplifier) {
            this.active = active;
            this.duration = duration;
            this.level = level;
            this.amplifier = amplifier;
        }
        
        public boolean isActive() {
            return active;
        }
        
        public int getDuration() {
            return duration;
        }
        
        public int getLevel() {
            return level;
        }
        
        public int getAmplifier() {
            return amplifier;
        }
        
        @Override
        public String toString() {
            if (!active) {
                return "Không có buff";
            }
            return String.format("Level %d (%ds)", level, duration);
        }
    }
    
    @Override
    public String toString() {
        return String.format("BuffAmountStat{base=%.2f, multiplier=%.2f, type=%s, stackable=%s, maxStacks=%d}",
                baseAmount, multiplier, buffType, stackable, maxStacks);
    }
}
