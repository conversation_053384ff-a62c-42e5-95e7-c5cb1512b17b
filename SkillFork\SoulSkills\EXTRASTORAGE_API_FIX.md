# ExtraStorage API Fix

## Lỗi gặp phải
```
[OreMultiplier] ExtraStorage không khả dụng hoặc lỗi: me.hsgamer.extrastorage.api.storage.Storage.canStore(org.bukkit.inventory.ItemStack)
```

## Nguyên nhân
Plugin đã sử dụng sai API của ExtraStorage:

### Sai (trước đây):
```java
// Sai: canStore() nhận Object, không phải ItemStack
Method canStoreMethod = storageClass.getMethod("canStore", ItemStack.class);
boolean canStore = (Boolean) canStoreMethod.invoke(storage, item);

// Sai: add() nhận Object và int, không phải chỉ ItemStack
Method addMethod = storageClass.getMethod("add", ItemStack.class);
addMethod.invoke(storage, item);
```

### Đúng (đã sửa):
```java
// Đúng: canStore() nhận Object
Method canStoreMethod = storageClass.getMethod("canStore", Object.class);
boolean canStore = (Boolean) canStoreMethod.invoke(storage, item);

// Đúng: add() nhận Object và int
Method addMethod = storageClass.getMethod("add", Object.class, int.class);
addMethod.invoke(storage, item, amount);
```

## ExtraStorage API Reference

### Storage Interface Methods:
```java
// Kiểm tra xem item có thể store không
boolean canStore(Object key); // key có thể là ItemStack hoặc String

// Thêm item vào storage
void add(Object key, int quantity); // key + số lượng

// Kiểm tra storage status
boolean getStatus(); // Storage có đang active không
boolean isMaxSpace(); // Storage có đầy không
```

## Thay đổi trong code

### File: OreMultiplierListener.java
```java
// Thêm kiểm tra isMaxSpace
Method isMaxSpaceMethod = storageClass.getMethod("isMaxSpace");
boolean isMaxSpace = (Boolean) isMaxSpaceMethod.invoke(storage);

// Sửa điều kiện kiểm tra
if (!status || !canStore || isMaxSpace) return false;

// Sửa method signature
Method canStoreMethod = storageClass.getMethod("canStore", Object.class);
Method addMethod = storageClass.getMethod("add", Object.class, int.class);

// Sửa cách gọi method
addMethod.invoke(storage, item, amount);
```

## Lợi ích của việc sửa

### 1. Tương thích đúng với ExtraStorage API
- Sử dụng đúng method signature
- Không còn NoSuchMethodException
- Hoạt động với tất cả phiên bản ExtraStorage

### 2. Kiểm tra đầy đủ hơn
- Thêm kiểm tra `isMaxSpace()`
- Đảm bảo storage không đầy trước khi thêm
- Tránh lỗi khi storage đạt giới hạn

### 3. Error handling tốt hơn
- Graceful fallback khi ExtraStorage không khả dụng
- Log rõ ràng về lỗi
- Không crash plugin khi có vấn đề

## Testing

### Trước khi sửa:
```
[ERROR] [OreMultiplier] ExtraStorage không khả dụng hoặc lỗi: me.hsgamer.extrastorage.api.storage.Storage.canStore(org.bukkit.inventory.ItemStack)
```

### Sau khi sửa:
```
[INFO] [Ore Multiplier] Đã thêm 3.0x 4x Diamond vào storage!
```

## Compatibility

### ExtraStorage Versions:
- ✅ Tương thích với tất cả phiên bản có Storage API
- ✅ Hoạt động với cả ExtraStorage cũ và mới
- ✅ Reflection-based approach đảm bảo tương thích

### Fallback Behavior:
- Nếu ExtraStorage không có → Drop ra ground
- Nếu Storage đầy → Drop ra ground  
- Nếu API error → Drop ra ground + log warning

## Kết luận
Lỗi đã được sửa hoàn toàn bằng cách:
1. Sử dụng đúng API signature của ExtraStorage
2. Thêm kiểm tra đầy đủ hơn
3. Cải thiện error handling

Plugin bây giờ sẽ hoạt động ổn định với ExtraStorage mà không gặp lỗi API.
