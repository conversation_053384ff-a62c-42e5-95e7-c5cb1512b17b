package shyrcs.Ability;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffectType;

public class BuffCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (command.getName().equalsIgnoreCase("buffs")) {
            showAllBuffs(player);
            return true;
        }
        
        if (command.getName().equalsIgnoreCase("mybuffs")) {
            showPlayerBuffs(player);
            return true;
        }

        if (command.getName().equalsIgnoreCase("buffhelp")) {
            showBuffHelp(player);
            return true;
        }

        if (command.getName().equalsIgnoreCase("cooldown")) {
            showCooldownInfo(player);
            return true;
        }

        return false;
    }
    
    /**
     * Hiển thị danh sách tất cả buff có sẵn
     */
    private void showAllBuffs(Player player) {
        player.sendMessage("§6=== Effect ===");
        player.sendMessage("§eFormat: <buff_id> <level_buff> <time_buff> <click_type> <cooldown>");
        player.sendMessage("");

        // Positive Effects
        player.sendMessage("§a• BUFF:");
        sendBuffInfo(player, "ABSORPTION", "Hấp Thụ");
        sendBuffInfo(player, "SPEED", "Tăng Tốc");
        sendBuffInfo(player, "STRENGTH", "Sức Mạnh");
        sendBuffInfo(player, "REGENERATION", "Hồi Máu");
        sendBuffInfo(player, "RESISTANCE", "Kháng Cự");
        sendBuffInfo(player, "FIRE_RESISTANCE", "Kháng Lửa");
        sendBuffInfo(player, "WATER_BREATHING", "Thở Dưới Nước");
        sendBuffInfo(player, "INVISIBILITY", "Tàng Hình");
        sendBuffInfo(player, "NIGHT_VISION", "Nhìn Trong Bóng Tối");
        sendBuffInfo(player, "HEALTH_BOOST", "Tăng Sinh Lực");
        sendBuffInfo(player, "SATURATION", "Bão Hòa");
        sendBuffInfo(player, "LUCK", "May Mắn");
        sendBuffInfo(player, "HASTE", "Đào Nhanh");
        sendBuffInfo(player, "JUMP_BOOST", "Nhảy Cao");
        sendBuffInfo(player, "INSTANT_HEALTH", "Sinh Lực Tức Thì");
        sendBuffInfo(player, "GLOWING", "Phát Sáng");
        sendBuffInfo(player, "SLOW_FALLING", "Rơi Chậm");
        sendBuffInfo(player, "CONDUIT_POWER", "Sức Mạnh Thuỷ Triều");
        sendBuffInfo(player, "DOLPHINS_GRACE", "Ân Huệ Của Cá Heo");
        sendBuffInfo(player, "HERO_OF_THE_VILLAGE", "Người Hùng Dân Làng");
        
        player.sendMessage("");
        player.sendMessage("§c• DEBUFF:");
        sendBuffInfo(player, "SLOWNESS", "Chậm Rãi");
        sendBuffInfo(player, "WEAKNESS", "Yếu Đuối");
        sendBuffInfo(player, "POISON", "Độc");
        sendBuffInfo(player, "BLINDNESS", "Mù Quáng");
        sendBuffInfo(player, "HUNGER", "Đói");
        sendBuffInfo(player, "MINING_FATIGUE", "Đào Chậm");
        sendBuffInfo(player, "INSTANT_DAMAGE", "Sát Thương Tức Thì");
        sendBuffInfo(player, "NAUSEA", "Choáng");
        sendBuffInfo(player, "WITHER", "Khô Héo");
        sendBuffInfo(player, "LEVITATION", "Bay");
        sendBuffInfo(player, "UNLUCK", "Rủi Ro");
        sendBuffInfo(player, "BAD_OMEN", "Điềm Xấu");
        sendBuffInfo(player, "DARKNESS", "Bóng Tối");
        sendBuffInfo(player, "INFESTED", "Nhiễm Khuẩn");
        sendBuffInfo(player, "OOZING", "Rò Rỉ");
        sendBuffInfo(player, "WEAVING", "Thiêu Dệt");
        sendBuffInfo(player, "WIND_CHARGED", "Nạp Gió");
        sendBuffInfo(player, "RAID_OMEN", "Điềm Xâm Lược");
        sendBuffInfo(player, "TRIAL_OMEN", "Điềm Thử Thách");

        player.sendMessage("");
        player.sendMessage("§b• HƯỚNG DẪN:");
        player.sendMessage("§f• §eFormat: §f<buff_id> <level_buff> <time_buff> <click_type> <cooldown>");
        player.sendMessage("");
        player.sendMessage("§7• Commands: §e/mybuffs §7| §e/buffhelp");
        player.sendMessage("§7• Tổng cộng: §e" + getTotalBuffCount() + " §7buff có sẵn!");
    }
    
    /**
     * Hiển thị thông tin một buff cụ thể
     */
    private void sendBuffInfo(Player player, String buffId, String description) {
        player.sendMessage("§f• §e" + buffId + "§7 - " + description);
    }
    
    /**
     * Hiển thị buff hiện tại của player
     */
    private void showPlayerBuffs(Player player) {
        player.sendMessage("§6=== BUFF HIỆN TẠI ===");

        if (player.getActivePotionEffects().isEmpty()) {
            player.sendMessage("§7Bạn hiện không có buff nào.");
            return;
        }

        player.getActivePotionEffects().forEach(effect -> {
            PotionEffectType type = effect.getType();
            String nameVi = BuffPlaceholderUtil.getBuffNameVi(type);
            int duration = effect.getDuration() / 20; // Chuyển từ tick sang giây
            int level = effect.getAmplifier() + 1;

            String levelRoman = BuffUtils.toRoman(level);
            String color = BuffUtils.isPositiveEffect(type) ? "§a" : "§c";

            player.sendMessage(color + "• " + nameVi + " " + levelRoman + "§7 (" + duration + "s)");
        });

        player.sendMessage("");
        player.sendMessage("§eTổng cộng: " + player.getActivePotionEffects().size() + " buff");
    }

    /**
     * Hiển thị hướng dẫn sử dụng buff
     */
    private void showBuffHelp(Player player) {
        player.sendMessage("§6=== GUIDE ===");
        player.sendMessage("§eFormat: §f<buff_id> <level_buff> <time_buff> <click_type> <cooldown>");
        player.sendMessage("");

        player.sendMessage("§a• THAM SỐ:");
        player.sendMessage("§f• §ebuff_id§7: ID của buff (VD: absorption, speed)");
        player.sendMessage("§f• §elevel_buff§7: Level buff (1-256)");
        player.sendMessage("§f• §etime_buff§7: Thời gian buff (giây)");
        player.sendMessage("§f• §eclick_type§7: left_click, right_click, shift_left_click, shift_right_click");
        player.sendMessage("§f• §ecooldown§7: Thời gian cooldown (giây)");

        player.sendMessage("");
        player.sendMessage("§d• COMMANDS:");
        player.sendMessage("§f• §e/buffs§7 - Xem tất cả buff có sẵn");
        player.sendMessage("§f• §e/mybuffs§7 - Xem buff hiện tại của bạn");
        player.sendMessage("§f• §e/buffhelp§7 - Xem hướng dẫn này");
        player.sendMessage("§f• §e/cooldown§7 - Xem cooldown hiện tại");
    }

    /**
     * Đếm tổng số buff có sẵn
     */
    private int getTotalBuffCount() {
        return BuffPlaceholderUtil.getAllBuffIds().length;
    }

    /**
     * Hiển thị thông tin cooldown hiện tại
     */
    private void showCooldownInfo(Player player) {
        player.sendMessage("§6Cooldown Info:");

        CooldownManager cooldownManager = CooldownManager.getInstance();
        if (cooldownManager == null) {
            player.sendMessage("§cCooldown Manager chưa được khởi tạo!");
            return;
        }

        java.util.Map<String, Long> cooldowns = cooldownManager.getPlayerCooldowns(player);

        if (cooldowns.isEmpty()) {
            player.sendMessage("§aBạn hiện không có cooldown nào!");
            return;
        }

        player.sendMessage("§eCooldown hiện tại:");
        for (java.util.Map.Entry<String, Long> entry : cooldowns.entrySet()) {
            String skillType = entry.getKey();
            long remaining = cooldownManager.getRemainingCooldown(player, skillType);

            if (remaining > 0) {
                String timeFormat = BuffUtils.formatCooldownTime(remaining);
                player.sendMessage("§f• §e" + skillType + "§7: " + timeFormat);
            }
        }

        // player.sendMessage("");
        // player.sendMessage("§7Cooldown sẽ hiển thị trên ActionBar khi bạn thử sử dụng skill!");
    }

}
