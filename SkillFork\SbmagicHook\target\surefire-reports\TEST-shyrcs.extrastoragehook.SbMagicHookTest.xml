<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="shyrcs.extrastoragehook.SbMagicHookTest" time="0.376" tests="8" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="24"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\SoulMC Dev\SkillFork\SbmagicHook\target\test-classes;C:\Users\<USER>\Desktop\SoulMC Dev\SkillFork\SbmagicHook\target\classes;C:\Users\<USER>\.m2\repository\io\papermc\paper\paper-api\1.21.4-R0.1-SNAPSHOT\paper-api-1.21.4-R0.1-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\33.3.1-jre\guava-33.3.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.28.0\error_prone_annotations-2.28.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\3.0.0\j2objc-annotations-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\joml\joml\1.10.8\joml-1.10.8.jar;C:\Users\<USER>\.m2\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;C:\Users\<USER>\.m2\repository\it\unimi\dsi\fastutil\8.5.15\fastutil-8.5.15.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.1\log4j-api-2.17.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\com\mojang\brigadier\1.3.10\brigadier-1.3.10.jar;C:\Users\<USER>\.m2\repository\net\md-5\bungeecord-chat\1.20-R0.2-deprecated+build.19\bungeecord-chat-1.20-R0.2-deprecated+build.19.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-resolver-provider\3.9.6\maven-resolver-provider-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.9.6\maven-model-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.9.6\maven-model-builder-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.26\plexus-interpolation-1.26.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.9.6\maven-artifact-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-builder-support\3.9.6\maven-builder-support-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\eclipse\sisu\org.eclipse.sisu.inject\0.9.0.M2\org.eclipse.sisu.inject-0.9.0.M2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.9.6\maven-repository-metadata-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-api\1.9.18\maven-resolver-api-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-spi\1.9.18\maven-resolver-spi-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-util\1.9.18\maven-resolver-util-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-impl\1.9.18\maven-resolver-impl-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-named-locks\1.9.18\maven-resolver-named-locks-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\3.5.1\plexus-utils-3.5.1.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-minimessage\4.20.0\adventure-text-minimessage-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson\4.20.0\adventure-text-serializer-gson-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-json\4.20.0\adventure-text-serializer-json-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\option\1.1.0\option-1.1.0.jar;C:\Users\<USER>\.m2\repository\com\google\auto\service\auto-service-annotations\1.1.1\auto-service-annotations-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-commons\4.20.0\adventure-text-serializer-commons-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-legacy\4.20.0\adventure-text-serializer-legacy-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-plain\4.20.0\adventure-text-serializer-plain-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-logger-slf4j\4.20.0\adventure-text-logger-slf4j-4.20.0.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-api\4.21.0\adventure-api-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-key\4.21.0\adventure-key-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-api\1.3.0\examination-api-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-string\1.3.0\examination-string-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;C:\Users\<USER>\.m2\repository\net\dv8tion\JDA\5.6.1\JDA-5.6.1.jar;C:\Users\<USER>\.m2\repository\com\neovisionaries\nv-websocket-client\2.14\nv-websocket-client-2.14.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.10\kotlin-stdlib-common-1.9.10.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.8.21\kotlin-stdlib-jdk8-1.8.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.8.21\kotlin-stdlib-1.8.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.8.21\kotlin-stdlib-jdk7-1.8.21.jar;C:\Users\<USER>\.m2\repository\club\minnced\opus-java\1.1.1\opus-java-1.1.1.jar;C:\Users\<USER>\.m2\repository\club\minnced\opus-java-api\1.1.1\opus-java-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;C:\Users\<USER>\.m2\repository\club\minnced\opus-java-natives\1.1.1\opus-java-natives-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\net\sf\trove4j\core\3.1.0\core-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.18.3\jackson-core-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.18.3\jackson-databind-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.3\jackson-annotations-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\google\crypto\tink\tink\1.17.0\tink-1.17.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\4.28.2\protobuf-java-4.28.2.jar;C:\Users\<USER>\.m2\repository\com\github\MilkBowl\VaultAPI\1.7\VaultAPI-1.7.jar;C:\Users\<USER>\.m2\repository\org\bukkit\bukkit\1.13.1-R0.1-SNAPSHOT\bukkit-1.13.1-R0.1-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\me\clip\placeholderapi\2.11.5\placeholderapi-2.11.5.jar;C:\Users\<USER>\.m2\repository\org\bstats\bstats-bukkit\3.0.1\bstats-bukkit-3.0.1.jar;C:\Users\<USER>\.m2\repository\org\bstats\bstats-base\3.0.1\bstats-base-3.0.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-platform-bukkit\4.3.1\adventure-platform-bukkit-4.3.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-platform-api\4.3.1\adventure-platform-api-4.3.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-bungeecord\4.3.1\adventure-text-serializer-bungeecord-4.3.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-nbt\4.13.1\adventure-nbt-4.13.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson-legacy-impl\4.13.1\adventure-text-serializer-gson-legacy-impl-4.13.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-platform-facet\4.3.1\adventure-platform-facet-4.3.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-platform-viaversion\4.3.1\adventure-platform-viaversion-4.3.1.jar;C:\Users\<USER>\.m2\repository\com\github\brcdev-minecraft\shopgui-api\3.0.0\shopgui-api-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.9.2\junit-jupiter-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.9.2\junit-jupiter-api-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.9.2\junit-platform-commons-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.9.2\junit-jupiter-params-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.9.2\junit-jupiter-engine-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.9.2\junit-platform-engine-1.9.2.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Bangkok"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="24"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-24\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire4425527751174689899\surefirebooter-20250721192906710_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire4425527751174689899 2025-07-21T19-29-06_380-jvmRun1 surefire-20250721192906710_1tmp surefire_0-20250721192906710_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\SoulMC Dev\SkillFork\SbmagicHook\target\test-classes;C:\Users\<USER>\Desktop\SoulMC Dev\SkillFork\SbmagicHook\target\classes;C:\Users\<USER>\.m2\repository\io\papermc\paper\paper-api\1.21.4-R0.1-SNAPSHOT\paper-api-1.21.4-R0.1-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\33.3.1-jre\guava-33.3.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.28.0\error_prone_annotations-2.28.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\3.0.0\j2objc-annotations-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\joml\joml\1.10.8\joml-1.10.8.jar;C:\Users\<USER>\.m2\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;C:\Users\<USER>\.m2\repository\it\unimi\dsi\fastutil\8.5.15\fastutil-8.5.15.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.1\log4j-api-2.17.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\com\mojang\brigadier\1.3.10\brigadier-1.3.10.jar;C:\Users\<USER>\.m2\repository\net\md-5\bungeecord-chat\1.20-R0.2-deprecated+build.19\bungeecord-chat-1.20-R0.2-deprecated+build.19.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-resolver-provider\3.9.6\maven-resolver-provider-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.9.6\maven-model-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.9.6\maven-model-builder-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.26\plexus-interpolation-1.26.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.9.6\maven-artifact-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-builder-support\3.9.6\maven-builder-support-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\eclipse\sisu\org.eclipse.sisu.inject\0.9.0.M2\org.eclipse.sisu.inject-0.9.0.M2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.9.6\maven-repository-metadata-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-api\1.9.18\maven-resolver-api-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-spi\1.9.18\maven-resolver-spi-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-util\1.9.18\maven-resolver-util-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-impl\1.9.18\maven-resolver-impl-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-named-locks\1.9.18\maven-resolver-named-locks-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\3.5.1\plexus-utils-3.5.1.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-minimessage\4.20.0\adventure-text-minimessage-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson\4.20.0\adventure-text-serializer-gson-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-json\4.20.0\adventure-text-serializer-json-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\option\1.1.0\option-1.1.0.jar;C:\Users\<USER>\.m2\repository\com\google\auto\service\auto-service-annotations\1.1.1\auto-service-annotations-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-commons\4.20.0\adventure-text-serializer-commons-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-legacy\4.20.0\adventure-text-serializer-legacy-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-plain\4.20.0\adventure-text-serializer-plain-4.20.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-logger-slf4j\4.20.0\adventure-text-logger-slf4j-4.20.0.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-api\4.21.0\adventure-api-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-key\4.21.0\adventure-key-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-api\1.3.0\examination-api-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-string\1.3.0\examination-string-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;C:\Users\<USER>\.m2\repository\net\dv8tion\JDA\5.6.1\JDA-5.6.1.jar;C:\Users\<USER>\.m2\repository\com\neovisionaries\nv-websocket-client\2.14\nv-websocket-client-2.14.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.10\kotlin-stdlib-common-1.9.10.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.8.21\kotlin-stdlib-jdk8-1.8.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.8.21\kotlin-stdlib-1.8.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.8.21\kotlin-stdlib-jdk7-1.8.21.jar;C:\Users\<USER>\.m2\repository\club\minnced\opus-java\1.1.1\opus-java-1.1.1.jar;C:\Users\<USER>\.m2\repository\club\minnced\opus-java-api\1.1.1\opus-java-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;C:\Users\<USER>\.m2\repository\club\minnced\opus-java-natives\1.1.1\opus-java-natives-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\net\sf\trove4j\core\3.1.0\core-3.1.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.18.3\jackson-core-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.18.3\jackson-databind-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.3\jackson-annotations-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\google\crypto\tink\tink\1.17.0\tink-1.17.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\4.28.2\protobuf-java-4.28.2.jar;C:\Users\<USER>\.m2\repository\com\github\MilkBowl\VaultAPI\1.7\VaultAPI-1.7.jar;C:\Users\<USER>\.m2\repository\org\bukkit\bukkit\1.13.1-R0.1-SNAPSHOT\bukkit-1.13.1-R0.1-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\me\clip\placeholderapi\2.11.5\placeholderapi-2.11.5.jar;C:\Users\<USER>\.m2\repository\org\bstats\bstats-bukkit\3.0.1\bstats-bukkit-3.0.1.jar;C:\Users\<USER>\.m2\repository\org\bstats\bstats-base\3.0.1\bstats-base-3.0.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-platform-bukkit\4.3.1\adventure-platform-bukkit-4.3.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-platform-api\4.3.1\adventure-platform-api-4.3.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-bungeecord\4.3.1\adventure-text-serializer-bungeecord-4.3.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-nbt\4.13.1\adventure-nbt-4.13.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson-legacy-impl\4.13.1\adventure-text-serializer-gson-legacy-impl-4.13.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-platform-facet\4.3.1\adventure-platform-facet-4.3.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-platform-viaversion\4.3.1\adventure-platform-viaversion-4.3.1.jar;C:\Users\<USER>\.m2\repository\com\github\brcdev-minecraft\shopgui-api\3.0.0\shopgui-api-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.9.2\junit-jupiter-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.9.2\junit-jupiter-api-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.9.2\junit-platform-commons-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.9.2\junit-jupiter-params-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.9.2\junit-jupiter-engine-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.9.2\junit-platform-engine-1.9.2.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-24"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\SoulMC Dev\SkillFork\SbmagicHook"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire4425527751174689899\surefirebooter-20250721192906710_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="24.0.1+9-30"/>
    <property name="user.name" value="kusan"/>
    <property name="stdout.encoding" value="Cp1252"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="24.0.1"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\SoulMC Dev\SkillFork\SbmagicHook"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-24\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files (x86)\Bitvise SSH Client;C:\Program Files\nodejs\;C:\Program Files\dotnet\;C:\Program Files (x86)\cloudflared\;C:\Program Files\CMake\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\apache-maven-3.9.9\bin;;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="Cp1252"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="24.0.1+9-30"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="68.0"/>
  </properties>
  <testcase name="testStorageHookConnections" classname="shyrcs.extrastoragehook.SbMagicHookTest" time="0.217"/>
  <testcase name="testStorageHookMultipleConnections" classname="shyrcs.extrastoragehook.SbMagicHookTest" time="0.003"/>
  <testcase name="testStorageHookInvalidOperations" classname="shyrcs.extrastoragehook.SbMagicHookTest" time="0.007"/>
  <testcase name="testGenerateKey" classname="shyrcs.extrastoragehook.SbMagicHookTest" time="0.005"/>
  <testcase name="testThreadSafety" classname="shyrcs.extrastoragehook.SbMagicHookTest" time="0.025"/>
  <testcase name="testColorCodeConversion" classname="shyrcs.extrastoragehook.SbMagicHookTest" time="0.022"/>
  <testcase name="testBridgeHookTimeout" classname="shyrcs.extrastoragehook.SbMagicHookTest" time="0.003"/>
  <testcase name="testBridgeHookBasicOperations" classname="shyrcs.extrastoragehook.SbMagicHookTest" time="0.003"/>
</testsuite>