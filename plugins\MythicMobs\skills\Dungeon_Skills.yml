LunaPas:
  Skills:
  - sendtitle{t="&e> &d&n&k|&r &c&lChữa Lành &d&n&k|&r &e<";st=" &6&l[ &f&lH<PERSON>i gần quá rồi đó... &6&l]";d=40} @PIR{r=25}
  - effect:particles{p=totem;a=200;points=10;s=0.4;hS=0.2;vS=0.2} @self
  - sound{s=entity.player.levelup} @PIR{r=40}
  - delay 20
  - healpercent {m=5} @self
LunaOnSpawn:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name><&sp>&7[<&sp>&cHoàn<&sp>Chỉnh<&sp>&7]<&sp>&f&l<<”;subtitle=“&f[<&sp>&b&lĐã<&sp><PERSON><PERSON><PERSON><PERSON><&sp>Triệu<&sp><PERSON><PERSON><PERSON><&sp>&f]”}
  - delay 20
  - effect:smokeswirl{d=100;i=10} @Self
  - delay 20
  - lightning{d=500} @PIR{r=10}
  - throw{velocity=50;velocityY=10} @PlayersInRadius{r=6}
  - sound{s=entity.lightning_bolt.thunder;v=5} @PIR{r=30}
LunaOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”}
  - message{m="&f&l[&c&l<mob.name>&f&l] &cKhông.... Đừng bỏ em mà...."} @PIR{r=25} ~onDeath
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=6}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=50}
LunaFirstSkill:
  Skills:
  - sendtitle{t="&e> &d&n&k|&r &c&lGiật Tung &d&n&k|&r &e<";st=" &6&l[ &f&lBiến mất khỏi thế giới này đi! &6&l]";d=95} @PIR{r=25}
  - potion{type=LEVITATION;lvl=2;duration=200} @self
  - potion{type=DAMAGE_RESISTANCE;lvl=3;duration=180} @self
  - heal{a=20000} @self
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}
  - effect:bloodyscreen{d=20} @PIR{r=40}
  - lightning{d=5000;ia=true} @PIR{r=25}
  - delay 25
  - damagepercent{p=0.05;ia=true} @target
  - healpercent{m=1} @Self
  - throw{v=5;vy=15} @target
  - teleport @target
  - delay 25
  - heal{a=20000} @self
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}
  - effect:bloodyscreen{d=20} @PIR{r=40}
  - lightning{d=5000;ia=true} @PIR{r=25}
  - delay 25
  - damagepercent{p=0.05;ia=true} @target
  - healpercent{m=1} @Self
  - throw{v=5;vy=15} @target
  - teleport @target
  - delay 25
  - heal{a=20000} @self
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}
  - effect:bloodyscreen{d=20} @PIR{r=40}
  - lightning{d=5000;ia=true} @PIR{r=25}
  - delay 25
  - damagepercent{p=0.05;ia=true} @target
  - healpercent{m=1} @Self
  - throw{v=5;vy=15} @target
  - teleport @target
  - delay 25
  - heal{a=20000} @self
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}
  - effect:bloodyscreen{d=20} @PIR{r=40}
  - lightning{d=5000;ia=true} @PIR{r=25}
  - delay 25
  - damagepercent{p=0.05;ia=true} @target
  - healpercent{m=1} @Self
  - throw{v=5;vy=15} @target
  - teleport @target
  - delay 25
  - pull{v=8} @PIR{r=25}
  - throw{v=5;vy=25} @PIR{r=25}
  - lightning{d=10000;ia=true} @target
  - sound{s=entity.generic.explode}
LunaSecondSkill:
  Skills:
  - sendtitle{t="&e> &d&n&k|&r &e&lTan Nát &d&n&k|&r &e<";st=" &a&l[ &f&lQuy phục xuống trước nguồn sức mạnh này! &a&l]";d=95} @PIR{r=25}
  - effect:particles{particle=cloud;a=200;points=10;hS=0;y=0.5;speed=0;dir=3,0,3} @target ~onTimer:5
  - teleport @target
  - delay 25
  - lightning{d=2000;ia=true} @Ring{p=30;r=5;fill=true}
  - lightning{d=2000;ia=true} @PIR{r=30}
  - effect:lightning @PIR{r=30}
  - sound{s=entity.lightning_bolt.thunder;v=5} @PIR{r=30}
  - delay 20
  - throw{v=5;vy=25} @target
  - damage{a=1000;ia=true;ir=true} @target
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}
LunaThirdSkill:
  Skills:
  - sendtitle{t="&e> &d&n&k|&r &e&lÁnh Trăng Máu &d&n&k|&r &e<";st=" &a&l[ &f&lThật đẹp, cho một linh hồn sắp lụi tàn... &a&l]";d=95} @PIR{r=25}
  - potion{type=LEVITATION;lvl=2;duration=100} @PIR{r=20}
  - effect:particles{p=cloud;a=100;y=1;points=10;s=0.1;hS=0.3;vS=0.3} @PIR{r=20}
  - delay 100
  - effect:sound{s=entity.generic.explode;v=1;p=0}
  - prison{m=glass;d=100} @PIR{r=20}
  - message{m="&f&l[&c&l<mob.name>&f&l] &cKhỏi chạy nữa!"} @PIR{r=30}
  - delay 40
  - message{m="&f&l[&c&l<mob.name>&f&l] &cVĩnh biệt!"} @PIR{r=30}
  - effect:particles{p=cloud;a=100;y=1;points=10;s=0.1;hS=0.3;vS=0.3} @PIR{r=20}
  - delay 40
  - sound{s=entity.generic.explode}
  - explosion{y=5} @PIR{r=20}
  - damage{a=4000;ia=true;ir=true} @target
FloridPlayerKill:
  Skills:
  - healpercent{m=0.05} @self
  - modifytargetscore{obj=KillCount;action=ADD;v=1} @self ~onPlayerKill
  - potion{type=HARM;lvl=10;duration=20} @PIR{r=30}
  - potion{type=HUNGER;lvl=3;duration=6000} @PIR{r=30}
  - potion{type=POISON;lvl=5;duration=1000} @PIR{r=30}
  - potion{type=WITHER;lvl=5;duration=1000} @PIR{r=30}
  - message{m="&f[&cCảnh báo&f] &3Cường hóa của &a&l<mob.name> &3đã đạt bậc &d<mob.score.KillCount>&f."} @PIR{r=30}
FloridPassive:
  Skills:
  - heal{a=10000} @self
  - damage{a=100;pkb=false} @PIR{r=50}
FloridOnSpawn:
  Skills:
  - prison{m=barrier;d=300;b=false} @PIR{r=50}
  - message{m="&f[&a<mob.name>&f] &bA! Cuối cùng cũng thấy cậu rồi!"} @PIR{r=50}
  - delay 60
  - message{m="&f[&a<mob.name>&f] &bSẵn sàng chưa nhỉ? Tớ sẽ bung hết sức đấy nhé!"} @PIR{r=50}
  - delay 60
  - message{m="&f[&a<mob.name>&f] &bĐược rồi! Quẩy thôi nào! Hehe....."} @PIR{r=50}
  - delay 60
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @PIR{r=50}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @PIR{r=50}
  - potion{type=BLINDNESS;lvl=2;duration=6000} @PIR{r=50}
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @PIR{r=50}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @PIR{r=50}
  - potion{type=SLOW;lvl=2;duration=6000} @PIR{r=50}
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @PIR{r=50}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @PIR{r=50}
  - potion{type=SLOW_DIGGING;lvl=2;duration=6000} @PIR{r=50}
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @PIR{r=50}
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @PIR{r=50}
  - potion{type=WEAKNESS;lvl=2;duration=6000} @PIR{r=50}
  - effect:particles{p=instantSpell;a=100;y=1;points=10;s=0.1;hS=0.3;vS=0.3} @self
  - delay 60
  - message{m="&f[&3Floor 7&f] &3Đã cung cấp đủ hiệu ứng."} @PIR{r=50}
FloridFirstSkill:
  Skills:
  - sendtitle{t="&c&l10";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l9";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l8";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l7";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l6";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l5";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l4";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l3";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l2";d=20} @PIR{r=50}
  - delay 20
  - sendtitle{t="&c&l1";d=20} @PIR{r=50}
  - delay 20
  - skill{s=FloridSuccessSkill}
FloridSuccessSkill:
  TargetConditions:
  - onBlock{b=BEDROCK} orElseCast FloridFailSkill
  Skills:
  - sendtitle{t="&a&lSafe";d=20} 
  - effect:sound{s=entity.experience_orb.pickup;p=0.5;v=1}
  - message{m="&f&l[&a&l<mob.name>&f&l] &bCũng đỉnh đó. Chắc hong có lần sau đâu nhỉ."} 
  - heal{a=100} @PIR{r=50}
FloridFailSkill:
  Skills:
  - sendtitle{t="&c&lFail";d=20}
  - effect:sound{s=entity.player.death;p=2;v=1}
  - message{m="&f&l[&a&l<mob.name>&f&l] &bHoy, cố lần sau nhe."} 
  - damagepercent{p=0.2;ia=true} @PIR{r=50}
FloridSecondSkill:
  Skills:
  - projectile{oT=FSS-OnTick;oH=FSS-OnHit;oE=FSS-OnEnd;i=1;v=12;hR=2;vR=2}
FSS-OnTick:
  Skills:
  - effect:particles{p=dragonbreath;amount=20;speed=0;hs=.3;vs=.3} @origin
  - effect:particles{p=happyvillager;amount=100;speed=0.1;hs=.3;vs=.3} @origin
  - sound{s=entity.enderdragon.growl;v=1} @origin
FSS-OnHit:
  Skills:
  - damage{a=1000}
  - teleport
  - throw{velocitiy=35;velocityY=15} @PIR{r=3}
FSS-OnEnd:
  Skills:
  - effect:particles{p=instantSpell;a=100;hS=1;vS=1}
  - effect:sound{s=entity.generic.explode;v=1;p=0}
  - damage{a=1500;ia=false} @PIR{r=5}
FloridThirdSkill:
  Skills:
  - message{m="&f&l[&a&l<mob.name>&f&l] &bĐược òi. Núp lẹ đi mấy cưng. Hong là đi đóa."}
  - delay 60
  - message{m="&f&l[&a&l<mob.name>&f&l] &bHun cái nà."}
  - potion{type=SLOW;lvl=50;duration=100} @PIR{r=50}
  - pull{v=50} @PIR{r=50}
  - skill{s=FloridThirdFailSkill}
FloridThirdFailSkill:
  TargetConditions:
  - distance{d=<5} true
  Skills:
  - forcepull{s=2}
  - prison{m=barrier;d=200;b=false}
  - message{m="&f&l[&a&l<mob.name>&f&l] &bHoy xong ròi. Bái bai nha. Hẹn hong gặp lại~~"}
  - delay 40
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - lightning{d=10000}
  - delay 5
  - damagepercent{p=1;ia=true}
FloridFourthSkill:
  Cooldown: 300
  Skills:
  - teleport @origin
  - prison{m=barrier;d=150;b=false} @PIR{r=50}
  - message{m="&f&l[&a&l<mob.name>&f&l] &bOaaa. Ghê dữ vậy trời."} @PIR{r=50}
  - delay 20
  - message{m="&f&l[&a&l<mob.name>&f&l] &bThôi thì không đùa giỡn nữa nhỉ."} @PIR{r=50}
  - delay 20
  - message{m="&f&l[&a&l<mob.name>&f&l] &bVào công chuyện thôi nào."} @PIR{r=50}
  - delay 20
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=INCREASE_DAMAGE;lvl=4;duration=6000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=SPEED;lvl=4;duration=6000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=ABSORPTION;lvl=4;duration=6000} @self
  - delay 10
  - effect:sound{s=entity.splash_potion.break;p=1;v=1} @self
  - effect:particles{p=mobSpell;a=20;y=0;points=5;c=#F12CFB} @self
  - potion{type=PROTECTION;lvl=4;duration=6000} @self
  - message{m="&f&l[&a&l<mob.name>&f&l] &a&lMode &c&lTận Diệt&b, khởi động."} @PIR{r=30}
  - delay 20
  - healpercent{m=100} @self
  - effect:particlering{p=totem;a=20;points=25;r=10;hS=1;vS=0} @self
  - effect:bloodyscreen{d=40} @PIR{r=50}
  - forcepull{s=1} @PIR{r=50}
  - effect:lightning{repeat=5;repeatInterval=2} @PIR{r=50}
  - lightning{d=10000} @PIR{r=50} 
FloridOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”}
  - message{m="&r&f&l[&a&l<mob.name>&f&l] &aChúc..... mừng..... cậu....!"} @PIR{r=25} ~onDeath
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=50}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=50}
  
ManiacTestifying:
  Skills:
  - message{m="&f[&dSparkle&f] &cLên nòng thôi!"} @Self
  - delay 20
  - message{m="&f[&dSparkle&f] &cKhai hỏa!"} @Self
  - randomskill{s=RouletteOne,RouletteTwo,RouletteThree,RouletteFour,RouletteFive,FailRoulette}
RouletteOne:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
RouletteTwo:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
RouletteThree:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
RouletteFour:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
RouletteFive:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
FailRoulette:
  Skills:
  - message{m="&f[&dSparkle&f] &cÍ ẹ... Kẹo đồng thật..."} @Self
  - delay 20
  - suicide @self
  