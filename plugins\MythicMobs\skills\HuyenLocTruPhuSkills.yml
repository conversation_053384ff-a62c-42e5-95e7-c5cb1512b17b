HuyenLocSpawner:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name><&sp>&7[<&sp>&cHoàn<&sp>Chỉnh<&sp>&7]<&sp>&f&l<<”;subtitle=“&f[<&sp>&b&lĐã<&sp><PERSON><PERSON><PERSON><PERSON><&sp>Triệu<&sp>Hồi<&sp>&f]”}
  - delay 10
  - effect:lightning @Self 
  - throw{velocity=50;velocityY=10} @PlayersInRadius{r=6}
  - effect:particles{p=largeexplosion;a=5;vS=0.75;hS=0.75} @Ring{radius=4;points=12}
  - sound{s=entity.elder_guardian.curse;v=5} @self
HuyenLocDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đ<PERSON>h<&sp>b<PERSON><PERSON><&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=6}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=50}
HuyenLocHealth:
  Skills:
  - sendactionmessage{msg=“<mob.name><&sp><&co><&sp> &a<mob.hp>&6/&c<mob.mhp>”} @PIR{r=20}
  - sound{s=block.sand.step} @self
  - effect:itemspray{item=redstone;amount=1;velocity=1;d=15;} @self
HuyenLocSkill1TruongTho:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp>&e&lTrao<&sp>&e&lQuà<&sp><<”;subtitle=“&7[<&sp>&2&lSự<&sp>Ban<&sp>Ơn<&sp>Mùa<&sp>Lễ<&sp>Hội<&sp>&7]”} @PIR{r=50}
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - effect:particlesphere{particle=enchantment_table;amount=150;radius=3} @self
  - delay 5
  - potion{type=HEALTH_BOOST;d=1200;level=50} @self
  - heal{a=30000} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=20}
HuyenLocSkill2GioNhe:
  Skills:
  - projectile{onTick=HuyenLocSkill2GioNhe-Tick;onHit=HuyenLocSkill2GioNhe-Hit;oE=HuyenLocSkill2GioNhe-End;v=12;i=1;hR=1;vR=1}
HuyenLocSkill2GioNhe-Tick:
  Skills:
  - effect:particles{p=totem;amount=20;speed=0;hs=.3;vs=.3} @origin
  - effect:particles{p=happyvillager;amount=100;speed=0.1;hs=.3;vs=.3} @origin
  - sound{s=entity.enderdragon.growl;v=1} @origin
HuyenLocSkill2GioNhe-Hit:
  Skills:
  - damage{a=200}
  - teleport
  - throw{velocitiy=35;velocityY=15} @PIR{r=3}
HuyenLocSkill2GioNhe-End:
  Skills:
  - effect:particles{p=instantSpell;a=100;hS=1;vS=1}
  - effect:sound{s=entity.generic.explode;v=1;p=0}
  - damage{a=400;i=false} @PIR{r=5}
HuyenLocSkill3SungBanLong:
  Skills:
  - potion{type=SLOWNESS;lvl=10;duration=600} @PIR{r=30}
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>>&4&k&n|&r<&sp>&e&lSừng<&sp>&e&lTuần<&sp>&e&lLộc&e&l<&sp>&4&k&n|&r&f&l<<”;subtitle=“&7[<&sp><mob.name><&sp>&c&lđã<&sp>&c&ltích<&sp>&c&ltụ<&sp>&c&lđủ<&sp>&c&ltầng<&sp>&a&lTụ<&sp>&a&lNăng<&sp>&7]”} @PIR{r=50}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.player.breath;p=1} @PIR{r=50}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.zombie.break_door_wood} @PIR{r=50}
  - teleport @target
  - damage{a=1000} @target
  - damage{a=500} @PIR{r=30}
  - heal{a=1500} @self
  - effect:blockmask{m=grass_block;dv=0;r=15;d=100} @self
  - explosion{blockdamage=false;fire=false;yield=5} 
  - effect:particles{p=largeexplosion;a=3;vS=0.75;hS=0.75} @self
  - delay 20
  - sound{s=entity.player.breath;p=1} @PIR{r=50}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.zombie.break_door_wood} @PIR{r=50}
  - teleport @target
  - damage{a=1000} @target
  - damage{a=500} @PIR{r=30}
  - heal{a=1500} @self
  - effect:blockmask{m=grass_block;dv=0;r=15;d=100} @self
  - explosion{blockdamage=false;fire=false;yield=5} 
  - delay 20
  - sound{s=entity.player.breath;p=1} @PIR{r=50}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.zombie.break_door_wood} @PIR{r=50}
  - teleport @target
  - damage{a=1000} @target
  - damage{a=500} @PIR{r=30}
  - heal{a=1500} @self
  - effect:blockmask{m=grass_block;dv=0;r=15;d=100} @self
  - explosion{blockdamage=false;fire=false;yield=5} 
  - delay 20
  - sound{s=entity.player.breath;p=1} @PIR{r=50}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.zombie.break_door_wood} @PIR{r=50}
  - teleport @target
  - damage{a=1000} @target
  - damage{a=500} @PIR{r=30}
  - heal{a=1500} @self
  - effect:blockmask{m=grass_block;dv=0;r=15;d=100} @self
  - explosion{blockdamage=false;fire=false;yield=5} 
  - delay 20
  - sound{s=entity.player.breath;p=1;v=5} @PIR{r=30}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.zombie.break_door_wood} @PIR{r=40}
  - teleport @target
  - teleport @target
  - damage{a=1000} @target
  - damage{a=500} @PIR{r=30}
  - heal{a=1500} @self
  - effect:blockmask{m=grass_block;dv=0;r=15;d=100} @self
  - explosion{blockdamage=false;fire=false;yield=5} 
  - effect:particles{p=largeexplosion;a=3;vS=0.75;hS=0.75} @self
  - delay 20
  - sound{s=entity.player.breath;p=1} @PIR{r=50}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.zombie.break_door_wood} @PIR{r=50}
  - teleport @target
  - damage{a=1000} @target
  - damage{a=500} @PIR{r=30}
  - heal{a=1500} @self
  - effect:blockmask{m=grass_block;dv=0;r=15;d=100} @self
  - explosion{blockdamage=false;fire=false;yield=5} 
  - delay 20
  - sound{s=entity.player.breath;p=1} @PIR{r=50}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.zombie.break_door_wood} @PIR{r=50}
  - teleport @target
  - damage{a=1000} @target
  - damage{a=500} @PIR{r=30}
  - heal{a=1500} @self
  - effect:blockmask{m=grass_block;dv=0;r=15;d=100} @self
  - explosion{blockdamage=false;fire=false;yield=5} 
  - delay 20
  - sound{s=entity.player.breath;p=1} @PIR{r=50}
  - effect:smokeswirl{duration=60;interval=1} @Self
  - delay 20
  - sound{s=entity.zombie.break_door_wood} @PIR{r=50}
  - teleport @target
  - damage{a=1000} @target
  - damage{a=500} @PIR{r=30}
  - heal{a=1500} @self
  - effect:blockmask{m=grass_block;dv=0;r=15;d=100} @self
  - explosion{blockdamage=false;fire=false;yield=5} 
HuyenLocSkill4TiengDaoDong:
  Skills:
  - potion{type=LEVITATION;lvl=2;duration=100} @self
  - potion{type=PROTECTION;lvl=4;duration=1000} @self
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&e<&4&k&n|&e><&sp>&b&lGió Phương Bắc<&sp>&e<&4&k&n|&e>”;subtitle=“&7[<&sp>&e&lHành<&sp>trình<&sp>trải<&sp>dài<&sp>khắp<&sp>&d&lmọi<&sp>nơi<&sp>&7]”} @PIR{r=50}
  - lightning{d=15;ia=true} @PIR{r=40}
  - effect:totemofundying{duration=60;interval=1} @PIR{r=40}
  - delay 20
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}
  - lightning{d=15;ia=true} @PIR{r=40}
  - effect:totemofundying{duration=60;interval=1} @PIR{r=40}
  - delay 20
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}
  - lightning{d=15;ia=true} @PIR{r=40}
  - effect:totemofundying{duration=60;interval=1} @PIR{r=50}
  - delay 20
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}