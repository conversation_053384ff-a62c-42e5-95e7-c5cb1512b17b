package me.hsgamer.extrastorage.commands.subs.player;

import me.hsgamer.extrastorage.api.item.Item;
import me.hsgamer.extrastorage.api.storage.Storage;
import me.hsgamer.extrastorage.api.user.User;
import me.hsgamer.extrastorage.commands.abstraction.Command;
import me.hsgamer.extrastorage.commands.abstraction.CommandContext;
import me.hsgamer.extrastorage.commands.abstraction.CommandListener;
import me.hsgamer.extrastorage.commands.abstraction.CommandTarget;
import me.hsgamer.extrastorage.configs.Message;
import me.hsgamer.extrastorage.configs.Setting;
import me.hsgamer.extrastorage.data.Constants;
import me.hsgamer.extrastorage.util.Digital;
import me.hsgamer.extrastorage.util.ItemUtil;
import me.hsgamer.extrastorage.util.Utils;
import org.bukkit.Material;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Command(value = "withdraw", usage = "/{label} withdraw <material-key|all> [amount|all]", permission = Constants.PLAYER_WITHDRAW_PERMISSION, target = CommandTarget.ONLY_PLAYER)
public final class WithdrawCmd
        extends CommandListener {

    private final Setting setting;
    private final String USAGE_REGEX = Utils.getRegex("usage");
    public WithdrawCmd() {
        this.setting = instance.getSetting();
    }

    @Override
    public void execute(CommandContext context) {
        Player player = context.castToPlayer();
        User user = instance.getUserManager().getUser(player);
        if (user == null) {
            context.sendMessage(Message.getMessage("FAIL.player-not-found"));
            return;
        }
        Storage storage = user.getStorage();

        String args0 = context.getArgs(0);
        if (args0.equalsIgnoreCase("all")) {
            withdrawAll(player, storage, context);
            return;
        }

        Optional<Item> optional = storage.getItem(args0);
        if (!optional.isPresent()) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(Utils.getRegex("player"), player.getName()));
            return;
        }
        Item item = optional.get();

        int current = item.getQuantity();
        if (current < 1) {
            context.sendMessage(Message.getMessage("FAIL.not-enough-item").replaceAll(Utils.getRegex("item"), setting.getNameFormatted(args0, true)));
            return;
        }

        if (context.getArgsLength() == 1) {
            withdrawMaterialAll(player, storage, context, args0);
            return;
        }

        String args1 = context.getArgs(1);
        if (args1.equalsIgnoreCase("all")) {
            withdrawMaterialAll(player, storage, context, args0);
            return;
        }

        int amount;
        try {
            amount = Digital.getBetween(1, current, Integer.parseInt(args1));
        } catch (NumberFormatException ignored) {
            context.sendMessage(Message.getMessage("FAIL.not-number").replaceAll(VALUE_REGEX, args1));
            return;
        }
        withdrawSpecificAmount(player, storage, context, args0, amount);
    }

    private void withdrawAll(Player player, Storage storage, CommandContext context) {
        int totalWithdrew = 0;
        for (Item item : new ArrayList<>(storage.getItems().values())) { // Create a copy to avoid ConcurrentModificationException
            String materialKey = item.getKey();
            int quantity = item.getQuantity();

            if (quantity < 1) continue;

            ItemStack iStack = item.getItem().clone();
            iStack.setAmount(quantity);

            if (item.getType() == ItemUtil.ItemType.VANILLA) {
                iStack.setItemMeta(null);
            }

            int free = getFreeSpace(player, iStack);
            if (free == -1) {
                context.sendMessage(Message.getMessage("FAIL.inventory-is-full"));
                break; // Inventory is full, stop withdrawing
            }
            iStack.setAmount(free);

            storage.subtract(materialKey, free);
            ItemUtil.giveItem(player, iStack);
            totalWithdrew += free;

            context.sendMessage(Message.getMessage("SUCCESS.withdrew-item")
                    .replaceAll(Utils.getRegex("quantity"), Digital.formatThousands(free))
                    .replaceAll(Utils.getRegex("item"), setting.getNameFormatted(materialKey, true)));
        }

        if (totalWithdrew > 0) {
            context.sendMessage(Message.getMessage("SUCCESS.withdrew-all-items"));
        } else {
            context.sendMessage(Message.getMessage("FAIL.no-items-to-withdraw"));
        }
    }

    private void withdrawMaterialAll(Player player, Storage storage, CommandContext context, String materialKey) {
        Optional<Item> optional = storage.getItem(materialKey);
        if (!optional.isPresent()) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(Utils.getRegex("player"), player.getName()));
            return;
        }
        Item item = optional.get();

        int current = item.getQuantity();
        if (current < 1) {
            context.sendMessage(Message.getMessage("FAIL.not-enough-item").replaceAll(Utils.getRegex("item"), setting.getNameFormatted(materialKey, true)));
            return;
        }
        ItemStack iStack = item.getItem().clone();
        iStack.setAmount(current);

        if (item.getType() == ItemUtil.ItemType.VANILLA) {
            iStack.setItemMeta(null);
        }

        int free = this.getFreeSpace(player, iStack);
        if (free == -1) {
            player.sendMessage(Message.getMessage("FAIL.inventory-is-full"));
            return;
        }
        iStack.setAmount(free);

        storage.subtract(materialKey, free);
        ItemUtil.giveItem(player, iStack);

        context.sendMessage(Message.getMessage("SUCCESS.withdrew-item")
                .replaceAll(Utils.getRegex("quantity"), Digital.formatThousands(free))
                .replaceAll(Utils.getRegex("item"), setting.getNameFormatted(materialKey, true)));
    }

    private void withdrawSpecificAmount(Player player, Storage storage, CommandContext context, String materialKey, int amount) {
        Optional<Item> optional = storage.getItem(materialKey);
        if (!optional.isPresent()) {
            context.sendMessage(Message.getMessage("FAIL.item-not-in-storage").replaceAll(Utils.getRegex("player"), player.getName()));
            return;
        }
        Item item = optional.get();

        int current = item.getQuantity();
        if (current < amount) {
            context.sendMessage(Message.getMessage("FAIL.not-enough-item").replaceAll(Utils.getRegex("item"), setting.getNameFormatted(materialKey, true)));
            return;
        }
        ItemStack iStack = item.getItem().clone();
        iStack.setAmount(amount);

        if (item.getType() == ItemUtil.ItemType.VANILLA) {
            iStack.setItemMeta(null);
        }

        int free = this.getFreeSpace(player, iStack);
        if (free == -1) {
            player.sendMessage(Message.getMessage("FAIL.inventory-is-full"));
            return;
        }
        iStack.setAmount(free);

        storage.subtract(materialKey, free);
        ItemUtil.giveItem(player, iStack);

        context.sendMessage(Message.getMessage("SUCCESS.withdrew-item")
                .replaceAll(Utils.getRegex("quantity"), Digital.formatThousands(free))
                .replaceAll(Utils.getRegex("item"), setting.getNameFormatted(materialKey, true)));
    }

    /*
     * Trả về khoảng trống còn lại trong kho đồ của người chơi:
     * Sẽ là -1 nếu không còn khoảng trống nào.
     */
    private int getFreeSpace(Player player, ItemStack item) {
        ItemStack[] items = player.getInventory().getStorageContents();
        int empty = 0;
        for (ItemStack stack : items) {
            if ((stack == null) || (stack.getType() == Material.AIR)) {
                empty += item.getMaxStackSize();
                continue;
            }
            if (!item.isSimilar(stack)) continue;
            empty += (stack.getMaxStackSize() - stack.getAmount());
        }
        if (empty > 0) return Math.min(empty, item.getAmount());
        return -1;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, org.bukkit.command.Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            return new ArrayList<>();
        }

        Player player = (Player) sender;
        Storage storage = instance.getUserManager().getUser(player).getStorage();

        switch (args.length) {
            case 1 -> {
                // material-key or "all"
                String input = args[0].toLowerCase();
                List<String> suggestions = new ArrayList<>();
                suggestions.add("all");
                suggestions.addAll(storage.getItems().keySet().stream()
                        .filter(key -> storage.getItem(key).isPresent() && storage.getItem(key).get().getQuantity() > 0)
                        .filter(key -> key.toLowerCase().startsWith(input))
                        .sorted()
                        .collect(Collectors.toList()));
                return suggestions.stream().filter(s -> s.startsWith(input)).collect(Collectors.toList());
            }
            case 2 -> {
                // amount or "all"
                String firstArg = args[0].toLowerCase();
                String secondArg = args[1].toLowerCase();

                if (firstArg.equals("all")) {
                    return new ArrayList<>(); // No further arguments after "withdraw all"
                } else {
                    List<String> suggestions = new ArrayList<>();
                    suggestions.add("all");
                    suggestions.addAll(Arrays.asList("1", "10", "64", "1000", "10000", "100000", "1000000"));
                    return suggestions.stream()
                            .filter(amount -> amount.startsWith(secondArg))
                            .collect(Collectors.toList());
                }
            }
        }
        return new ArrayList<>();
    }
}
