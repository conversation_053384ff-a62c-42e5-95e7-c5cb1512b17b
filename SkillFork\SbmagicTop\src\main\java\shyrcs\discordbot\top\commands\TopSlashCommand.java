package shyrcs.discordbot.top.commands;

import net.dv8tion.jda.api.EmbedBuilder;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.interaction.GenericInteractionCreateEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.events.GenericEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import net.dv8tion.jda.api.interactions.commands.OptionType;
import net.dv8tion.jda.api.interactions.commands.build.SlashCommandData;
import net.dv8tion.jda.api.interactions.commands.build.Commands;
import net.dv8tion.jda.api.interactions.commands.build.OptionData;
import org.bukkit.Bukkit;
import shyrcs.discordbot.top.SbmagicTopPlugin;
import shyrcs.discordbot.top.managers.ConfigManager;
import shyrcs.discordbot.top.managers.PlaceholderManager;
import shyrcs.discordbot.top.models.TopConfig;

import java.awt.*;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class TopSlashCommand extends ListenerAdapter {
    
    private final SbmagicTopPlugin plugin;
    private final ConfigManager configManager;
    private final PlaceholderManager placeholderManager;
    private final Logger logger;
    
    public TopSlashCommand(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
        this.configManager = plugin.getConfigManager();
        this.placeholderManager = plugin.getPlaceholderManager();
        this.logger = plugin.getLogger();


    }



    @Override
    public void onMessageReceived(MessageReceivedEvent event) {
        if (event.getAuthor().isBot()) return;

        String message = event.getMessage().getContentDisplay();

        // Command !top <type>
        if (message.startsWith("!top")) {
            if (message.equals("!top")) {
                // Hiển thị hướng dẫn
                StringBuilder help = new StringBuilder();
                help.append("**Message Commands:**\n");
                help.append("• `!top balance` - Top người giàu nhất\n");

                event.getChannel().sendMessage(help.toString()).queue();
                return;
            }

            String[] parts = message.split(" ");
            if (parts.length >= 2) {
                String topType = parts[1];


                // Xử lý như slash command
                processTopCommandFromMessage(event, topType);
            } else {
                event.getChannel().sendMessage("❌ Sử dụng: `!top <type>` (ví dụ: `!top balance`)\n" +
                                             "📋 Gõ `!top` để xem hướng dẫn đầy đủ").queue();
            }
        }
    }
    
    public static SlashCommandData getCommandData() {
        List<String> topChoices = new ArrayList<>();

        // Lấy danh sách top từ ConfigManager (sẽ được cập nhật khi plugin khởi động)
        SbmagicTopPlugin instance = SbmagicTopPlugin.getInstance();
        if (instance != null && instance.getConfigManager() != null) {
            Map<String, TopConfig> configs = instance.getConfigManager().getAllTopConfigs();
            topChoices.addAll(configs.keySet());
            instance.getLogger().info("📋 Tìm thấy " + configs.size() + " config tops: " + topChoices);
        }

        // Nếu chưa có config nào, thêm mặc định
        if (topChoices.isEmpty()) {
            topChoices.add("balance");
            if (instance != null) {
                instance.getLogger().warning("⚠️ Không tìm thấy config, sử dụng mặc định: balance");
            }
        }

        OptionData topOption = new OptionData(OptionType.STRING, "type", "Loại top muốn xem", true);
        for (String choice : topChoices) {
            topOption.addChoice(choice, choice);
        }

        SlashCommandData commandData = Commands.slash("top", "Hiển thị bảng xếp hạng")
                .addOptions(topOption);

        if (instance != null) {
            instance.getLogger().info("🔧 Tạo command data: /top với " + topChoices.size() + " options");
        }

        return commandData;
    }
    
    @Override
    public void onSlashCommandInteraction(SlashCommandInteractionEvent event) {
        // Xử lý ping command
        if (event.getName().equals("ping")) {
            event.reply("🏓 Pong! Bot hoạt động bình thường!\n" +
                       "📝 Sử dụng `!top balance` hoặc `/top balance` để xem leaderboard.")
                    .queue();
            return;
        }

        if (!event.getName().equals("top")) {
            return;
        }

        // Defer reply
        event.deferReply().queue(
            success -> {
                String topType = event.getOption("type").getAsString();

                // Chạy async
                Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
                    try {
                        processTopCommand(event, topType);
                    } catch (Exception e) {
                        logger.severe("Error processing top command: " + e.getMessage());
                        event.getHook().editOriginal("❌ Có lỗi xảy ra khi tạo bảng xếp hạng: " + e.getMessage())
                                .queue();
                    }
                });
            },
            error -> {
                logger.severe("Error deferring reply: " + error.getMessage());
            }
        );
    }
    
    private void processTopCommand(SlashCommandInteractionEvent event, String topType) {
        TopConfig topConfig = configManager.getTopConfig(topType);

        if (topConfig == null) {
            event.getHook().editOriginal("❌ Không tìm thấy cấu hình cho top: " + topType)
                    .queue();
            return;
        }

        try {
            // Tạo embed
            EmbedBuilder embed = new EmbedBuilder();
            embed.setTitle(topConfig.getName());
            embed.setColor(new Color(topConfig.getColor()));
            embed.setTimestamp(Instant.now());

            // Set thumbnail nếu có
            if (topConfig.getThumbnail() != null && !topConfig.getThumbnail().isEmpty()) {
                embed.setThumbnail(topConfig.getThumbnail());
            }

            // Set footer
            String footerText = plugin.getConfig().getString("embed.footer", "Skyblock Magic - Leaderboard");
            embed.setFooter(footerText);

            // Parse và thêm content
            StringBuilder description = new StringBuilder();
            if (topConfig.getDescription() != null && !topConfig.getDescription().isEmpty()) {
                description.append(topConfig.getDescription()).append("\n\n");
            }

            for (String line : topConfig.getContent()) {
                String parsedLine = placeholderManager.parsePlaceholders(line);
                String discordLine = placeholderManager.convertMinecraftToDiscord(parsedLine);
                description.append(discordLine).append("\n");
            }

            embed.setDescription(description.toString());

            // Gửi embed
            event.getHook().editOriginalEmbeds(embed.build()).queue(
                success -> {
                    // Success
                },
                error -> {
                    logger.severe("Error sending embed: " + error.getMessage());
                    event.getHook().editOriginal("❌ Có lỗi xảy ra khi gửi bảng xếp hạng!")
                            .queue();
                }
            );

        } catch (Exception e) {
            logger.severe("Error in processTopCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra: " + e.getMessage())
                    .queue();
        }
    }

    private void processTopCommandFromMessage(MessageReceivedEvent event, String topType) {
        TopConfig topConfig = configManager.getTopConfig(topType);

        if (topConfig == null) {
            event.getChannel().sendMessage("❌ Không tìm thấy cấu hình cho top: " + topType).queue();
            return;
        }

        // Chạy async để không block Discord thread
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                // Tạo embed
                EmbedBuilder embed = new EmbedBuilder();
                embed.setTitle(topConfig.getName());
                embed.setColor(new Color(topConfig.getColor()));
                embed.setTimestamp(Instant.now());

                // Set thumbnail nếu có
                if (topConfig.getThumbnail() != null && !topConfig.getThumbnail().isEmpty()) {
                    embed.setThumbnail(topConfig.getThumbnail());
                }

                // Set footer
                String footerText = plugin.getConfig().getString("embed.footer", "Skyblock Magic - Leaderboard");
                embed.setFooter(footerText);

                // Parse và thêm content
                StringBuilder description = new StringBuilder();
                if (topConfig.getDescription() != null && !topConfig.getDescription().isEmpty()) {
                    description.append(topConfig.getDescription()).append("\n\n");
                }

                for (String line : topConfig.getContent()) {
                    String parsedLine = placeholderManager.parsePlaceholders(line);
                    String discordLine = placeholderManager.convertMinecraftToDiscord(parsedLine);
                    description.append(discordLine).append("\n");
                }

                embed.setDescription(description.toString());

                // Gửi embed
                event.getChannel().sendMessageEmbeds(embed.build()).queue(
                    success -> {
                        // Success
                    },
                    error -> {
                        logger.severe("Error sending embed: " + error.getMessage());
                        event.getChannel().sendMessage("❌ Có lỗi xảy ra khi gửi bảng xếp hạng!")
                                .queue();
                    }
                );

            } catch (Exception e) {
                logger.severe("Error in processTopCommandFromMessage: " + e.getMessage());
                event.getChannel().sendMessage("❌ Có lỗi xảy ra: " + e.getMessage())
                        .queue();
            }
        });
    }
}
