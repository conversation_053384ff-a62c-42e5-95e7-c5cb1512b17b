# Custom Instructions cho GitHub Copilot (Chính)

## Hướng dẫn chung
- **Ngôn ngữ:** <PERSON><PERSON>n luôn phản hồi, viết comment và tạo nội dung bằng **tiếng Việt**.
- **Tài liệu thiết kế:** Mọi đoạn code được tạo ra phải tuân thủ nghiêm ngặt theo các chỉ dẫn, kiến trúc và logic đã được mô tả trong file **detailed design** của dự án.
- **Không hardcode:** Tuyệt đối không được hardcode các giá trị có thể thay đổi. Luôn lấy giá trị từ file cấu hình.
- **Minecraft API Version:** Code phải được viết để tương thích với **Minecraft API 1.21** (Spigot/Paper).

---

## Hướng dẫn cho dự án `ExtraStorage`
- <PERSON><PERSON><PERSON> là một plugin về kho chứa, chức năng chính là cho phép người chơi lưu trữ các loại block với số lượng lớn.
- Implement tính năng tự động nhặt các vật phẩm (item drop) trên mặt đất và đưa chúng vào kho chứa này.

---

## Hướng dẫn cho Skript & Placeholder
- **Skript Addon:** Luôn mặc định rằng môi trường có cài đặt addon **skript-placeholder**.
- **Hiệu năng:** Khi tạo placeholder mới, ưu tiên sử dụng API từ plugin Java thay vì định nghĩa logic phức tạp trực tiếp trong Skript.

---

## **QUAN TRỌNG: Hướng dẫn về MMOItems**
- Đối với tất cả các tác vụ liên quan đến việc tạo, sửa đổi hoặc tương tác với các vật phẩm và kỹ năng của MMOItems, hãy **tham khảo chi tiết trong file `copilot-instructions-mmoitems.md`**.
- File đó chứa tất cả các quy tắc, kiến trúc và các API được ưu tiên sử dụng.

# Custom Instructions cho MMOItems API

## 1. Nguyên tắc cốt lõi & Kiến trúc
- **Kiến trúc Manager:** MMOItems sử dụng kiến trúc dựa trên các 'Manager' để quản lý từng thành phần. Khi cần truy cập một chức năng, hãy luôn lấy manager tương ứng.
- **Truy cập Manager:** Sử dụng instance static của class chính để gọi các manager.
    - `MMOItems.plugin.getStats()` để lấy **StatManager**.
    - `MMOItems.plugin.getTypes()` để lấy **TypeManager**.
    - `MMOItems.plugin.getTemplates()` để lấy **TemplateManager**.
    - `MMOItems.plugin.getSkills()` để lấy **SkillManager**.
    - `MMOItems.plugin.getTiers()` để lấy **TierManager**.
- **Template và Instance:**
    - **`MMOItemTemplate`**: Là bản thiết kế (blueprint) cho một vật phẩm, được định nghĩa trong file cấu hình.
    - **`MMOItem`**: Là một instance (thể hiện) cụ thể của vật phẩm được tạo ra từ một template. Đây là đối tượng bạn sẽ làm việc cùng nhiều nhất.

---

## 2. Tạo và Lấy Vật phẩm (Cách chuẩn)
- **Phương thức ưu tiên:** Luôn sử dụng các phương thức API trong class `MMOItems` để lấy vật phẩm.
    - `MMOItems.plugin.getItem(Type type, String id)`: Trả về một `ItemStack`. Đây là cách an toàn và phổ biến nhất.
    - `MMOItems.plugin.getMMOItem(Type type, String id)`: Trả về đối tượng `MMOItem`, cho phép truy cập sâu hơn vào dữ liệu trước khi build thành `ItemStack`.
- **Ví dụ lấy item:**
    ```java
    // Lấy Type từ TypeManager
    Type swordType = MMOItems.plugin.getTypes().get("SWORD");

    // Lấy ItemStack từ template có ID là "STEEL_SWORD"
    ItemStack steelSword = MMOItems.plugin.getItem(swordType, "STEEL_SWORD");
    ```
- **Lấy item với Level và Tier:**
    ```java
    PlayerData playerData = PlayerData.get(player);
    MMOItem mmoItem = MMOItems.plugin.getMMOItem(swordType, "STEEL_SWORD", playerData);
    ItemStack item = mmoItem.newBuilder().build();
    ```

---

## 3. Làm việc với Kỹ năng (Abilities)
- **Đối tượng Stat chính:** Sử dụng **`ItemStats.ABILITIES`** để truy cập và chỉnh sửa dữ liệu kỹ năng của một vật phẩm.
- **Cấu trúc Ability:** Một ability trong `ConfigurationSection` bao gồm `type`, `mode`, và các `modifiers`.
    ```yaml
    ability:
      Fireball:
        type: 'FIREBALL'
        mode: 'RIGHT_CLICK'
        cooldown: 10
        damage: 20
    ```
- **Đăng ký Kỹ năng mới (API):**
    - Nếu cần tạo một kỹ năng hoàn toàn mới, hãy tạo class kế thừa `RegisteredSkill`.
    - Dùng `MMOItemsAPI.registerSkill(skill)` để đăng ký. **Lưu ý:** Phải gọi phương thức này trong `onLoad()` của plugin bạn hoặc sử dụng `loadbefore: [MMOItems]` trong `plugin.yml`.
- **Kích hoạt Kỹ năng (API):**
    - Để buộc một người chơi thi triển kỹ năng, hãy dùng `MMOItemsAPI.castSkill(...)`.
    - Phương thức này cho phép tùy chỉnh các `modifier` của kỹ năng một cách linh hoạt tại thời điểm thi triển.

---

## 4. Quy tắc & Các API quan trọng khác
- **Đọc dữ liệu từ `ItemStack`:**
    - Để kiểm tra một `ItemStack` có phải là MMOItem không, hãy dùng `NBTItem.get(itemStack).hasType()`.
    - Dùng `MMOItems.getType(itemStack)` và `MMOItems.getID(itemStack)` để lấy `Type` và `ID` của vật phẩm.
- **Dữ liệu người chơi:** Luôn lấy dữ liệu người chơi qua `PlayerData.get(player)`. `PlayerData` là cửa ngõ để truy cập `RPGPlayer` và các thông tin khác.
- **Danh sách Stat:** Tham khảo class `net.Indyuce.mmoitems.ItemStats` để có danh sách đầy đủ tất cả các `ItemStat` có sẵn.