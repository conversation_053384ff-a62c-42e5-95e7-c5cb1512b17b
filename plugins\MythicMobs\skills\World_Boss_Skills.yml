# LuxTimerBegin:
#   Conditions:
#   - stance Combat
#   Cooldown: 485
#   Skills:
#   - message{m="&r&f&l[&d<mob.name>&f&l] &aBắt đầu tính giờ!!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a1 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a2 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a3 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a4 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a5 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a6 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a7 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a8 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a9 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a10 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a11 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a12 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a13 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a14 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a15 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a16 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a17 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a18 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &a19 phút đã trôi qua!"} @PIR{r=100}
#   - delay 1200
#   - message{m="&r&f&l[&d<mob.name>&f&l] &aA! Tiếp viện tới rồi. Tạm biệt :>"} @PIR{r=100}
#   - damage{a=100000;ia=true} @PIR{r=200}
#   - delay 5
#   - damage{a=100000;ia=true} @PIR{r=200}
#   - delay 5
#   - damage{a=100000;ia=true} @PIR{r=200}
#   - delay 5
#   - damage{a=100000;ia=true} @PIR{r=200}
#   - delay 5
#   - damage{a=100000;ia=true} @PIR{r=200}
#   - delay 5
#   - damage{a=100000;ia=true} @PIR{r=200}
#   - lightning{d=10000}
#   - delay 5
#   - lightning{d=10000}
#   - delay 5
#   - lightning{d=10000}
#   - delay 5
#   - lightning{d=10000}
#   - delay 5
#   - lightning{d=10000}
#   - delay 5
#   - lightning{d=10000}
#   - delay 5
#   - heal{a=12000000} @self
# LuxFirstSkill:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aNé xa nhẹ cái nà. Hong là tới đó!"} @PIR{r=100}
#   - effect:sound{s=entity.experience_orb.pickup;p=0.5;v=1} @PIR{r=100}
#   - delay 20
#   - effect:sound{s=entity.experience_orb.pickup;p=0.5;v=1} @PIR{r=100}
#   - delay 20
#   - effect:sound{s=entity.experience_orb.pickup;p=0.5;v=1} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &aĂn nhẹ cái trói nè."} @PIR{r=4}
#   - stun{d=30;f=false;kb=true} @PIR{r=4}
# LuxSecondSkill:
#   Conditions:
#   - stance Combat
#   Skills:
#   - randomskill{s=RandomOne,RandomTwo,RandomThree,RandomFour,RandomFive,RandomSix,RandomSeven,RandomEight,RandomNine,RandomTen,RandomEleven,RandomTwelve,RandomThirteen,RandomFourteen,RandomFifteen,RandomSixteen}
# RandomOne:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aCúi xuống đi nào."} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessOne}
# RandomTwo:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aNhảy lên cái nè."} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessTwo}
# RandomThree:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aNhảy thậc cao lên nha."} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessThree}
# RandomFour:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aCó khiên không? Chặn cái này đi!"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessFour}
# RandomFive:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aCó kiếm &dGỗ &aở đó hong ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessFive}
# RandomSix:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aCó kiếm &dĐá &aở đó hong ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessSix}
# RandomSeven:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aCó kiếm &dSắt &aở đó hong ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20  - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessSeven}
# RandomEight:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aCó kiếm &dVàng &aở đó hong ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessEight}
# RandomNine:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aCó kiếm &dKim Cương &aở đó hong ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessNine}
# RandomTen:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aNúp đi nào. &dTia Sáng Chói Rọi!"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessTen}
# RandomEleven:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aDưới chân phải khối &dRedstone &akhông ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessEleven}
# RandomTwelve:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aDưới chân phải khối &dLapis &akhông ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessTwelve}
# RandomThirteen:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aDưới chân phải khối &dIron &akhông ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessThirteen}
# RandomFourteen:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aDưới chân phải khối &dGold &akhông ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessFourteen}
# RandomFifteen:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aDưới chân phải khối &dDiamond &akhông ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessFifteen}
# RandomSixteen:
#   Conditions:
#   - stance Combat
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aDưới chân phải khối &dEmerald &akhông ta?"} @PIR{r=100}
#   - sendtitle{t="&e&l3";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l2";d=20} @PIR{r=100}
#   - delay 20
#   - sendtitle{t="&e&l1";d=20} @PIR{r=100}
#   - delay 20
#   - skill{s=SuccessSixteen}
# SuccessOne:
#   TargetConditions:
#   - crouching orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessTwo:
#   TargetConditions:
#   - heightabove{h=67} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessThree:
#   TargetConditions:
#   - heightabove{h=68} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessFour:
#   TargetConditions:
#   - blocking orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessFive:
#   TargetConditions:
#   - holding{m=wood_sword} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessSix:
#   TargetConditions:
#   - holding{m=stone_sword} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessSeven:
#   TargetConditions:
#   - holding{m=iron_sword} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessEight:
#   TargetConditions:
#   - holding{m=gold_sword} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessNine:
#   TargetConditions:
#   - holding{m=diamond_sword} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessTen:
#   TargetConditions:
#   - inside orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessEleven:
#   TargetConditions:
#   - onBlock{b=REDSTONE_BLOCK} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessTwelve:
#   TargetConditions:
#   - onBlock{b=LAPIS_BLOCK} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessThirteen:
#   TargetConditions:
#   - onBlock{b=IRON_BLOCK} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessFourteen:
#   TargetConditions:
#   - onBlock{b=GOLD_BLOCK} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessFifteen:
#   TargetConditions:
#   - onBlock{b=DIAMOND_BLOCK} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# SuccessSixteen:
#   TargetConditions:
#   - onBlock{b=EMERALD_BLOCK} orElseCast LuxSecondFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aAyda. Nỡ lòng nào làm vậy với tấm thân ngọc ngà này à :<"} @PIR{r=100}
#   - damage{a=300000;pkb=true;ii=true} @self
# LuxSecondFailSkill:
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aTiếc quá nhờ. Hoy thất bại một lần cho nhớ :>"} @PIR{r=100}
#   - effect:sound{s=entity.player.death;p=2;v=1} @PIR{r=100}
#   - heal{a=50000} @self
# LuxThirdSkill:
#   Conditions:
#   - stance Combat
#   Skills:
#   - randomskill{s=RadiusFour,RadiusSix,RadiusNine,RadiusEleven}
# RadiusFour:
#   Conditions:
#   - stance Combat
#   Skills:
#   - teleport @origin
#   - forcepull{s=2} @PIR{r=100}
#   - stun{d=80;f=true;kb=true} @self
#   - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=4;p=4600;y=0;exact=true}
#   - message{m="&d[&e<mob.name>&d] &a3."} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &a2."} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &a1."} @PIR{r=100}
#   - delay 20
#   - skill{s=RadiusSuccessFour}
# RadiusSix:
#   Conditions:
#   - stance Combat
#   Skills:
#   - teleport @origin 
#   - forcepull{s=2} @PIR{r=100}
#   - stun{d=80;f=true;kb=true} @self
#   - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=6;p=4600;y=0;exact=true}
#   - message{m="&d[&e<mob.name>&d] &a3."} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &a2."} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &a1."} @PIR{r=100}
#   - delay 20
#   - skill{s=RadiusSuccessSix}
# RadiusNine:
#   Conditions:
#   - stance Combat
#   Skills:
#   - teleport @origin
#   - forcepull{s=2} @PIR{r=100}
#   - stun{d=80;f=true;kb=true} @self
#   - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=9;p=4600;y=0;exact=true}
#   - message{m="&d[&e<mob.name>&d] &a3."} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &a2."} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &a1."} @PIR{r=100}
#   - delay 20
#   - skill{s=RadiusSuccessNine}
# RadiusEleven:
#   Conditions:
#   - stance Combat
#   Skills:
#   - teleport @origin
#   - forcepull{s=2} @PIR{r=100}
#   - stun{d=80;f=true;kb=true} @self
#   - effect:particles{p=endRod;a=1;s=0;vs=0.1;xs=0.1;zs=0.1} @Sphere{r=11;p=4600;y=0;exact=true}
#   - message{m="&d[&e<mob.name>&d] &a3."} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &a2."} @PIR{r=100}
#   - delay 20
#   - message{m="&d[&e<mob.name>&d] &a1."} @PIR{r=100}
#   - delay 20
#   - skill{s=RadiusSuccessEleven}
# RadiusSuccessFour:
#   TargetConditions:
#   - distance{d=>4} orElseCast LuxThirdFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aĐược đó. Tiếp tục như vậy nhoa :3"} @PIR{r=100}
# RadiusSuccessSix:
#   TargetConditions:
#   - distance{d=>6} orElseCast LuxThirdFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aĐược đó. Tiếp tục như vậy nhoa :3"} @PIR{r=100}
# RadiusSuccessNine:
#   TargetConditions:
#   - distance{d=>9} orElseCast LuxThirdFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aĐược đó. Tiếp tục như vậy nhoa :3"} @PIR{r=100}
# RadiusSuccessEleven:
#   TargetConditions:
#   - distance{d=>11} orElseCast LuxThirdFailSkill
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aĐược đó. Tiếp tục như vậy nhoa :3"} @PIR{r=100}
# LuxThirdFailSkill:
#   Skills:
#   - message{m="&d[&e<mob.name>&d] &aHoy hì mất xíu máu cho nhớ nhoa."} @PIR{r=100}
#   - heal{a=25000} @self
#   - lightning{d=10000}
#   - delay 5
#   - lightning{d=10000}
#   - delay 5
#   - lightning{d=10000}
#   - delay 5
LuxOnSpawn:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp>&a&lＥｌｅ&b&lｍｅｎ&c&lｔａｌ&d&lｉｓｔ &e&lＬｕｘ<&sp>&7[<&sp>&cHoàn<&sp>Chỉnh<&sp>&7]<&sp>&f&l<<”;subtitle=“&f[<&sp>&b&lĐã<&sp>Được<&sp>Triệu<&sp>Hồi<&sp>&f]”} @Server
  - prison{m=barrier;d=300;b=false} @PIR{r=100}
  - message{m="&f[&e<mob.name>&f] &aOra ora. Chào mấy cưng nhé."} @PIR{r=100}
  - delay 60
  - message{m="&f[&e<mob.name>&f] &aĐến đây để theo đuổi toy à."} @PIR{r=100}
  - delay 60
  - message{m="&f[&e<mob.name>&f] &aNhắm sống được thì nhào vô"} @PIR{r=100}
  - delay 60
  - message{m="&r&f[&e<mob.name>&f] &aTiếp viện 8 phút nữa mới tới à. Oke."} @PIR{r=100}
  - delay 10
  - setstance{s=Combat} @self
Lux_Pas:
  Conditions:
  - stance Combat
  Skills:
  - orbital{r=0.5;p=8;hitradius=0.5;rotate=true;hp=true;a=LuxPassive;i=1;d=60;os=Lux_Pas_Start;ot=Lux_Pas_Tick;oh=Lux_Pas_Hit} @casterLocation
Lux_Pas_Start:
  Skills:
  - particle{p=soul;a=2} @self
Lux_Pas_Tick:
  Skills:
  - particle{p=red_dust;color=#000000}
Lux_E:
  Conditions:
  - stance Combat
  Skills:
  - projectile{type=NORMAL;os=Lux_E_Start;ot=Lux_E_Tick;oe=Lux_E_End;i=2;d=48;mr=11;v=2.5;accuracy=4;hp=false;hnp=false;sb=false;repeat=4;repeatinterval=1} @RLNC{a=44;r=11;os=true}
Lux_E_Start:
  Skills:
  - particlesphere{r=0.5;p=red_dust;color=#f80fe5;a=1;s=0}
Lux_E_Tick:
  Skills:
  - particle{p=cloud;a=1;s=0.02;vd=16} @origin
  - sound{s=entity.wind_charge.throw;p=0.2;v=6} @ThreatTablePlayers
Lux_E_End:
  Skills:
  - projectile{type=NORMAL;os=Lux_E_Linger_Start;ot=Lux_E_Linger_Tick;oe=Lux_E_Linger_End;d=100;mr=1;v=0;hp=false;hnp=false;sb=false;i=2}
Lux_E_Linger_Start:
  Skills:
  - bloodyScreen{d=40} @PIR{r=3}
Lux_E_Linger_Tick:
  Skills:
  - potion{t=SLOWNESS;d=30;lvl=3;i=false;p=false} @PIR{r=3}
  - particlering{pts=24;r=3;p=end_rod;a=2;v=0;vd=16} @origin
  - particlesphere{r=0.5;p=red_dust;color=#f80fe5;a=3;s=0}
  - particleline{md=1;p=lava;a=2;speed=0;fixedyaw=0}
  - particleline{md=1;p=lava;a=2;speed=0;fixedyaw=45}
  - particleline{md=1;p=lava;a=2;speed=0;fixedyaw=90}
  - particleline{md=1;p=lava;a=2;speed=0;fixedyaw=135}
  - particleline{md=1;p=lava;a=2;speed=0;fixedyaw=180}
  - particleline{md=1;p=lava;a=2;speed=0;fixedyaw=225}
  - particleline{md=1;p=lava;a=2;speed=0;fixedyaw=270}
  - particleline{md=1;p=lava;a=2;speed=0;fixedyaw=315}
Lux_E_Linger_End:
  Skills:
  - potion{t=SLOWNESS;d=60;lvl=3;i=false;p=false;overwrite=true} @PIR{r=3}
  - damage{a=265;ii=true;ieff=true} @PIR{r=3}
LuxOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @PIR{r=100}
  - message{m="&r&f&l[&e&l<mob.name>&f&l] &bÁnh sáng của.... tớ...."} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=50}
  - potionclear @PIR{r=100}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=100}
LunaPas:
  Skills:
  - effect:particles{p=totem;a=200;points=10;s=0.4;hS=0.2;vS=0.2} @self
  - sound{s=entity.player.levelup} @ThreatTable
  - throw{v=2;vy=2} @PIR{r=3} 
  - delay 5
  - heal{a=50} @self
LunaOnSpawn:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name><&sp>&7[<&sp>&cHoàn<&sp>Chỉnh<&sp>&7]<&sp>&f&l<<”;subtitle=“&f[<&sp>&b&lĐã<&sp>Được<&sp>Triệu<&sp>Hồi<&sp>&f]”}
  - delay 20
  - effect:smokeswirl{d=100;i=10} @Self
  - delay 20
  - throw{velocity=50;velocityY=10} @PlayersInRadius{r=6}
  - sound{s=entity.lightning_bolt.thunder;v=5} @PIR{r=30}
LunaOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @Server
  - message{m="&7[&cLuna&7] &cKhông.... Đừng bỏ em mà...."} @ThreatTable
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=6}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @ThreatTable
LunaFirstSkill:
  Skills:
  - potion{type=LEVITATION;lvl=2;duration=200} @self
  - potion{type=DAMAGE_RESISTANCE;lvl=3;duration=180} @self
  - heal{a=20000} @self
  - sound{s=entity.evocation_fangs.attack} @PIR{r=40}
  - effect:bloodyscreen{d=20} @PIR{r=40}
  - lightning{d=100;ia=true} @PIR{r=25}
  - delay 25
  - damagepercent{p=0.05;ia=true} @target
  - healpercent{m=0.5} @Self
  - throw{v=5;vy=15} @target
  - teleport @target
  - delay 25
  - heal{a=20000} @self
  - sound{s=entity.evocation_fangs.attack} @PIR{r=40}
  - effect:bloodyscreen{d=20} @PIR{r=40}
  - lightning{d=100;ia=true} @PIR{r=25}
  - delay 25
  - damagepercent{p=0.05;ia=true} @target
  - healpercent{m=0.5} @Self
  - throw{v=5;vy=15} @target
  - teleport @target
  - delay 25
  - heal{a=20000} @self
  - sound{s=entity.evocation_fangs.attack} @PIR{r=40}
  - effect:bloodyscreen{d=20} @PIR{r=40}
  - lightning{d=100;ia=true} @PIR{r=25}
  - delay 25
  - damagepercent{p=0.05;ia=true} @target
  - healpercent{m=0.5} @Self
  - throw{v=5;vy=15} @target
  - teleport @target
  - delay 25
  - heal{a=20000} @self
  - sound{s=entity.evocation_fangs.attack} @PIR{r=40}
  - effect:bloodyscreen{d=20} @PIR{r=40}
  - lightning{d=100;ia=true} @PIR{r=25}
  - delay 25
  - damagepercent{p=0.05;ia=true} @target
  - healpercent{m=0.5} @Self
  - throw{v=5;vy=15} @target
  - teleport @target
  - delay 25
  - pull{v=8} @PIR{r=25}
  - throw{v=5;vy=25} @PIR{r=25}
  - lightning{d=200;ia=true} @target
  - sound{s=entity.generic.explode}
Luna_Q:
  Skills:
  - setStance{s=Combat} @self
  - message{m="&7[&cLuna&7] &eChuẩn bị cho bất ngờ của em chưa??"} @ThreatTable
  - teleport @casterSpawn
  - aura{a=LunaQ;os=LunaQ_Start;ot=LunaQ_Tick;oe=LunaQ_End;i=6;d=180}
LunaQ_Start:
  Skills:
  - sound{s=block.bell.use} @ThreatTable 
LunaQ_Tick:
  Skills:
  - particlering{pts=80;r=8;particle=cloud;a=2;s=0.03;vspread=0.01;yoffset=4.7} @casterSpawnLocation
  - particlering{pts=60;r=6;particle=cloud;a=2;s=0.03;vspread=0.01;yoffset=4.7} @casterSpawnLocation
  - particlering{pts=40;r=4;particle=cloud;a=2;s=0.03;vspread=0.01;yoffset=4.7} @casterSpawnLocation
  - particlering{pts=20;r=2;particle=cloud;a=2;s=0.03;vspread=0.01;yoffset=4.7} @casterSpawnLocation
  - particlesphere{p=cherry_leaves;r=0.5;a=13;yoffset=4.7;s=0.03} @casterSpawnLocation 
  - particlering{pts=12;r=2;p=cloud;a=2;s=0.01;yoffset=0.42} @ThreatTable 
  - particleline{startyoffset=8.82;targetyoffset=0;distancebetween=0.5;md=20;particle=dripping_lava;a=3;speed=0.03} @ThreatTable
  - bloodyScreen{d=6} @ThreatTable
LunaQ_End:
  Skills:
  - projectile{bullettype=ARROW;type=METEOR;os=LunaProtQ_Start;ot=LunaProtQ_Tick;oh=LunaProtQ_Hit;i=2;g=0.8;hp=true;onhitblockskill=LunaProtQ_Linger;heightfromsurface=10;horizontalradius=2} @ThreatTable
LunaQ_End_Delay:
  Skills:
  - delay 60
  - skill{s=LunaQ_End}
LunaProtQ_Start:
  Skills:
  - message{m="&7[&cLuna&7] &eNhìn lên nè!"} @ThreatTable
LunaProtQ_Tick:
  Skills:
  - particlesphere{p=fireworksspark;radius=0.35;a=17;speed=0.01} @origin
  - particle{p=endrod;a=18} @origin
  - particlering{pts=38;r=2;p=dragonbreath;yoffset=0.1} @projectileforward{blockCentered=true;f=10}
LunaProtQ_Hit:
  Skills:
  - damage{a=160;ia=true;ii=true;is=true;ieff=true;ie=true;ir=true;preventimmunity=true}
  - aura{a=LingeringEffect;os=Linger_Start;ot=Linger_Tick;oe=Linger_End;i=6;d=60;cancelongivedamage=true} @trigger
LunaProtQ_Linger:
  Skills:
  - skill{s=LunaSummon}
LunaSummon:
  Skills:
  - summon{a=1;m=Luna_Clone;inheritthreattable=true;summonerisparent=true} 
Linger_Start:
  Skills:
  - message{m="&4&oBạn thấy bản thân dần yếu dần..."} @trigger
  - delay 4
  - message{m="&4&oCó lẽ máu của Luna sẽ giúp bạn."} @trigger
  - delay 4
  - message{m="&e&o(Gây sát thương một lần lên Luna để xóa hiệu ứng)"} @trigger
Linger_Tick:
  Skills:
  - particlering{pts=8;p=flame;r=1;a=3;s=0.03} @trigger
  - sound{s=block.fire.ambient;v=2;p=0.3} @trigger
Linger_End:
  Skills:
  - delay 10  
  - skill{s=Linger_End_Delayed}
Linger_End_Delayed:
  TriggerConditions:
  - score{obj=AuraCancel;value=1} castinstead Linger_End_Cancel
  Skills:
  - potion{t=DARKNESS;lvl=2;d=160;hasicon=true;overwrite=true} @trigger
  - potion{t=SLOW;lvl=2;d=160;hasicon=true;overwrite=true} @trigger
  - potion{t=MINING_FATIGUE;lvl=5;d=160;hasicon=true;overwrite=true} @trigger
  - potion{t=WEAKNESS;lvl=4;d=160;hasicon=true;overwrite=true} @trigger
  - damage{a=1000;ia=true;ie=true;ir=true;ignoreeffects=true} @trigger
Linger_End_Cancel:
  Skills:
  - message{m="&a&oBạn thấy bản thân hồi phục nhanh hơn."} @trigger
  - potion{t=REGENERATION;lvl=2;d=30;hasicon=true;overwrite=true} @trigger
  - modifytargetscore{objective=AuraCancel;value=0;action=SET} @trigger

Luna_W:
  Conditions:
  - stance Combat
  Skills:
  - randomskill{skills=Luna_W_R1,Luna_W_R2}
Luna_W_R1:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&7[&cLuna&7] &aCúi xuống đi nào."} 
  - sendtitle{t="&e&l2";d=20} 
  - delay 20
  - sendtitle{t="&e&l1";d=20} 
  - delay 20
  - skill{s=Luna_W_S_R1}
Luna_W_R2:
  Conditions:
  - stance Combat
  Skills:
  - message{m="&7[&cLuna&7] &aNhảy lên cái nè."} 
  - sendtitle{t="&e&l2";d=20} 
  - delay 20
  - sendtitle{t="&e&l1";d=20} 
  - delay 20
  - skill{s=Luna_W_S_R2}
Luna_W_S_R1:
  TargetConditions:
  - crouching orElseCast Luna_W_Fail
  Skills:
  - message{m="&7[&cLuna&7] &cNgoannnn."} 
  - sound{s=entity.experience_orb.pickup;p=0.2;v=3} 
  - potion{t=REGENERATION;duration=2;l=1;p=true;i=true} 

Luna_W_S_R2:
  TargetConditions:
  - altitude{h=2-10} orElseCast Luna_W_Fail
  Skills:
  - message{m="&7[&cLuna&7] &cNgoannnn."} 
  - sound{s=entity.experience_orb.pickup;p=0.2;v=3} 
  - potion{t=REGENERATION;duration=2;l=1;p=true;i=true} 

Luna_W_Fail:
  Skills:
  - message{m="&7[&cLuna&7] &cTập trung vào nào!"} 
  - effect:sound{s=entity.player.death;p=2;v=1} 
  - heal{a=250} @self
  - damage{a=110}

Luna_E_75P:
  Cooldown: 100000
  Skills:
  - teleport @casterSpawn
  - potion{t=SLOWNESS;l=50;d=60} @self
  - delay 2
  - aura{aura=LunaE;i=2;t=140;os=Luna_E_Start;ot=Luna_E_Tick}
Luna_E_50P:
  Cooldown: 100000
  Skills:
  - teleport @casterSpawn
  - potion{t=SLOWNESS;l=50;d=60} @self
  - delay 2
  - aura{aura=LunaE;i=2;t=140;os=Luna_E_Start;ot=Luna_E_Tick}
Luna_E_25P:
  Cooldown: 100000
  Skills:
  - teleport @casterSpawn
  - potion{t=SLOWNESS;l=50;d=60} @self
  - delay 2
  - aura{aura=LunaE;i=2;t=140;os=Luna_E_Start;ot=Luna_E_Tick}
Luna_E_Start:
  Skills:
  - sendTitle{t="&e>> &c&lWARNING!! &e<<";duration=60;fadein=5;fadeout=5}
Luna_E_Tick:
  Skills:
  - teleport @casterSpawn
  - pull{v=2.8}
  - damage{a=100000000} @PIR{r=3}
  - particlering{pts=32;r=2;p=red_dust;c=#e61616;a=2;yoffset=2.0;speed=0;vd=15;vs=0.07} @self
  - particlering{pts=32;r=2;p=red_dust;c=#e61616;a=2;yoffset=1.6;speed=0;vd=15;vs=0.07} @self
  - particlering{pts=32;r=2;p=red_dust;c=#e61616;a=2;yoffset=1.2;speed=0;vd=15;vs=0.07} @self
  - particlering{pts=32;r=2;p=red_dust;c=#e61616;a=2;yoffset=0.8;speed=0;vd=15;vs=0.07} @self
  - particlering{pts=32;r=2;p=red_dust;c=#e61616;a=2;yoffset=0.4;speed=0;vd=15;vs=0.07} @self

# Luna_W:
#   Skills:
#   - randomskill{s=LunaW1_1,LunaW2_1}
# LunaW1_1:
#   Skills:
#   - aura{a=LW1;os=LW_Start;ot=LW1_Tick;oe=LW1_End;i=2;d=42} @ThreatTablePlayers
# LW_Start:
#   Skills:
#   - message{m="&7[&cLuna&7] &eTập trung nà!"} @ThreatTable
# LW1_Tick:
#   Skills:
#   - modifytargetscore{obj=FC_1;v=1;a=ADD} @self
#   - setVariable{var=caster.LW1_FC_1;val=(<caster.score.FC_1>/10);type=FLOAT} @self
#   - sendTitle{t="&eCúi xuống đi! <caster.var.LW1_FC_1>s";d=2} @trigger
# LW1_End:
#   TriggerConditions:
#   - crouching castinstead LW_Success
#   Skills:
#   - modifytargetscore{obj=FC_1;v=0;a=SET} @self
#   - message{m="&4&oFail!"} @trigger
#   - heal{a=1600} @self
# LunaW2_1:
#   Skills:
#   - aura{a=LW2;os=LW_Start;ot=LW2_Tick;oe=LW2_End;i=2;d=42} @ThreatTablePlayers
# LW2_Tick:
#   Skills:
#   - modifytargetscore{obj=FC_2;v=1;a=ADD} @self
#   - setVariable{var=caster.LW1_FC_2;val=(<caster.score.FC_2>/10);type=FLOAT} @self
#   - sendTitle{t="&eNhảy lên nè! <caster.var.LW1_FC_2>s";d=2} @trigger
# LW2_End:
#   TriggerConditions:
#   - altitude{h=2-5} castinstead LW_Success
#   Skills:
#   - modifytargetscore{obj=FC_2;v=0;a=SET} @self
#   - message{m="&4&oFail!"} @trigger
#   - heal{a=1600} @self
# LW_Success:
#   Skills:
#   - modifytargetscore{obj=FC_2;v=0;a=SET} @self
#   - modifytargetscore{obj=FC_1;v=0;a=SET} @self
#   - message{m="&e&oSuccess!"} @trigger
# Test_Luna_W:
#   Skills:
#   - skill{s=LunaW_1}
#   #- randomskill{s=LunaW_1,LunaW_2}
# LunaW_1:
#   Skills:
#   - aura{a=Luna_W1;os=LunaW1_Start;ot=LunaW1_Tick;oe=LunaW1_End;i=2;d=42} @ThreatTablePlayers
#   - aura{aura=Luna_W1_check;ot=LunaW1_Check_Tick;i=1;d=42} @ThreatTablePlayers

# # targetting aura
# LunaW1_Start:
#   Skills:
#   - message{m="&7[&cLuna&7] &eTập trung nà!"} @ThreatTable
# LunaW1_Tick:
#   Skills:
#   - modifytargetscore{obj=FocusCheck_1;v=1;a=ADD} @self
#   # how to make a working decimal countdown?? huh??? wtf
#   # testing mob: World_Boss.yml/Timer_Test
#   # (solved, but looking for a way to do it with variable :D)
#   - setVariable{var=caster.FCheck;val=(<caster.score.FocusCheck_1>/10);type=FLOAT} @self
#   - sendTitle{t="&eCúi xuống đi! <caster.var.FCheck>s";d=2} @trigger
#   - skill{s=LunaW_1_Check} @trigger
# LunaW1_End:
#   Skills:
#   - modifytargetscore{objective=FocusCheck_1;v=0;a=SET} @Self
#   - delay 8
#   - skill{s=LunaW1_End_Delay}
# LunaW1_End_Delay:
#   TriggerConditions:
#   - score{obj=FocusCompletion;v=1} castinstead LunaW1_Success
#   Skills:
#   - message{m="&4&oFail!"} @trigger
#   - heal{a=1600} @self
# LunaW1_Success:
#   Skills:
#   - sound{s=entity.experience_orb.pickup;p=0.2;v=3} @trigger
#   - modifytargetscore{obj=FocusCompletion;v=0;a=SET} @trigger
#   - message{m="&e&oSuccess!"} @trigger

# # condition check aura
# LunaW_1_Check:
#   TargetConditions:
#   - crouching true
#   Skills:
#   - sound{s=entity.experience_orb.pickup;p=0.2;v=3} @trigger
#   - modifytargetscore{obj=FocusCompletion;v=1;a=SET} @trigger

# # initial cancelling
# LunaW_1_Cancel:
#   Skills:
#   - delay 4
#   - skill{s=LunaW_1_Cancel_Delay}
# LunaW_1_Cancel_Delay:
#   TriggerConditions:
#   - score{obj=FocusCompletion;v=1} true
#   Skills:
#   - auraRemove{a=Luna_W1} @self

# # 2nd aura cancelling
# LunaW1_Check_Tick:
#   TriggerConditions:
#   - score{obj=FocusCompletion;v=1} true
#   Skills:
#   - auraRemove{a=Luna_W1} @self

# LunaW_Success_2:
#   TargetConditions:
#   - altitude{h=1-10} true
#   Skills:
#   - sound{s=entity.experience_orb.pickup;p=0.2;v=3} @trigger

LunaSecondSkill:
  Skills:
  - sendtitle{t="&e> &d&n&k|&r &e&lTan Nát &d&n&k|&r &e<";st=" &a&l[ &f&lQuy phục xuống trước nguồn sức mạnh này! &a&l]";d=95} @PIR{r=25}
  - effect:particles{particle=cloud;a=200;points=10;hS=0;y=0.5;speed=0;dir=3,0,3} @target ~onTimer:5
  - teleport @target
  - delay 25
  - lightning{d=200;ia=true} @Ring{p=30;r=5;fill=true}
  - lightning{d=200;ia=true} @PIR{r=30}
  - effect:lightning @PIR{r=30}
  - sound{s=entity.lightning_bolt.thunder;v=5} @PIR{r=30}
  - delay 20
  - throw{v=5;vy=25} @target
  - damage{a=100;ia=true;ir=true} @target
  - sound{s=entity.evocation_fangs.attack } @PIR{r=40}
LunaThirdSkill:
  Skills:
  - sendtitle{t="&e> &d&n&k|&r &e&lÁnh Trăng Máu &d&n&k|&r &e<";st=" &a&l[ &f&lThật đẹp, cho một linh hồn sắp lụi tàn... &a&l]";d=95} @PIR{r=25}
  - potion{type=LEVITATION;lvl=2;duration=100} @PIR{r=20}
  - effect:particles{p=cloud;a=100;y=1;points=10;s=0.1;hS=0.3;vS=0.3} @PIR{r=20}
  - delay 100
  - effect:sound{s=entity.generic.explode;v=1;p=0}
  - prison{m=glass;d=100} @PIR{r=20}
  - message{m="&f&l[&c&l<mob.name>&f&l] &cKhỏi chạy nữa!"} @PIR{r=30}
  - delay 40
  - message{m="&f&l[&c&l<mob.name>&f&l] &cVĩnh biệt!"} @PIR{r=30}
  - effect:particles{p=cloud;a=100;y=1;points=10;s=0.1;hS=0.3;vS=0.3} @PIR{r=20}
  - delay 40
  - sound{s=entity.generic.explode}
  - explosion{y=5} @PIR{r=20}
  - damage{a=400;ia=true;ir=true} @target
GwenOnSpawn:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name><&sp>&7[<&sp>&cHoàn<&sp>Chỉnh<&sp>&7]<&sp>&f&l<<”;subtitle=“&f[<&sp>&b&lĐã<&sp>Được<&sp>Triệu<&sp>Hồi<&sp>&f]”} @server 
  - message{m="&f[&e<mob.name>&f] &aĐược rồi mọi người."} @PIR{r=100}
  - delay 20
  - throw{velocity=50;velocityY=10} @PlayersInRadius{r=6}
  - message{m="&f[&e<mob.name>&f] &aĐầu tiên chúng ta phải chiến thắng đã."} @PIR{r=100}
  - delay 20
  - message{m="&f[&e<mob.name>&f] &aSau đó, tớ rất vui lòng sửa quần áo, phụ kiện hay.. ý chí bị hư."} @PIR{r=100}
  - effect:smokeswirl{d=100;i=10} @Self
  - delay 20
  - sound{s=entity.lightning_bolt.thunder;v=5} @PIR{r=30}
  - setStance{s=Combat} @self
GwenPas:
  Conditions:
  - stance{s=Combat}
  Skills:
  - heal{a=50} @self
StanceCheck:
  Conditions:
  - stance{s=Combat}
  Skills:
  - message{m="&bStance exists!"} @PIR{r=5}
Gwen_Q:
  Conditions:
  - stance{s=Combat}
  Skills:
  - setstance{s=Immobolised} @self
  - teleport @casterSpawnLocation 
  - delay 8 
  - stun{noknockback=true;d=80} @self
  - stun{facing=true;noknockback=true;d=20} @PIR{r=15}
  - delay 10
  - aura{aura=GwenQ;os=Gwen_Q-Start;ot=Gwen-Q-Tick;oe=Gwen-Q-End;i=2;d=80}
Gwen_Q-Start:
  Skills:
  - sound{s=block.amethyst_block.chime;p=0.2;v=1.3} @ThreatTable
  - message{m="&f[&9Gwen&f] &eTrốn đi nè..."} @ThreatTable
Gwen-Q-Tick:
  Skills:
  - particleline{startyoffset=0;targetyoffset=0;md=20;p=red_dust;color=#210af1;a=4;speed=0.04} @ThreatTablePlayers
  - sound{s=block.amethyst_block.place;p=0.3;v=1.1;} @ThreatTable
Gwen-Q-End:
  Skills:
  - projectile{b=ARROW;type=NORMAL;os=Prot-Q-Start;ot=Prot-Q-Tick;oh=Prot-Q-Hit;i=2;d=120;v=35;mr=15;hp=true;hnp=true} @ThreatTablePlayers
  - delay 30
  - forcepull{s=1} @PIR{r=40}
  - skill{s=Gwen_W}
Prot-Q-Start:
  Skills:
  - sound{s=block.anvil.place;p=0.2;v=1.3} @ThreatTable
  - message{m="&f[&9<mob.name>&f] &ePằng!"} @ThreatTable
Prot-Q-Tick:
  Skills:
  - effect:particles{particle=reddust;color=#546EED;a=3;speed=0.04;viewdistance=15} @origin
Prot-Q-Hit:
  Skills:
  - damage{a=270}
  - damage{a=90;ia=true}
  - message{m="&4Bạn cảm thấy yếu đi dưới tác dụng của màn sương lam..."}
  - potion{t=Slow;d=100;l=2;hasicon=true;hasparticles=false}
  - potion{t=DARKNESS;d=100;l=1;hasicon=true;hasparticles=false}
  - potion{t=WEAKNESS;d=100;l=3;hasicon=true;hasparticles=false}
Gwen_Q_Enhanced:
  Conditions:
  - stance{s=Combat}
  Skills:
  - setstance{s=Immobolised} @self
  - teleport @casterSpawnLocation 
  - stun{noknockback=true;d=80} @self
  - stun{facing=true;noknockback=true;d=50} @PIR{r=15}
  - delay 10
  - aura{aura=GwenQ;os=Gwen_Q-Start;ot=Gwen-Q-Tick;oe=Gwen-Q-End_Enhanced;i=2;d=80}
Gwen-Q-End_Enhanced:
  Skills:
  - projectile{b=ARROW;type=NORMAL;os=Prot-Q-Start;ot=Prot-Q-Tick;oh=Prot-Q-Hit_Enhanced;i=2;d=120;v=35;mr=20;hp=true;hnp=true} @ThreatTablePlayers
  - delay 30
  - forcepull{s=1} @PIR{r=40}
  - skill{s=Gwen_W_Enhanced}
Prot-Q-Hit_Enhanced:
  Skills:
  - damage{a=470}
  - damage{a=190;ia=true}
  - message{m="&4Bạn cảm thấy yếu đi dưới tác dụng của màn sương lam..."}
  - potion{t=Slow;d=160;l=3;hasicon=true;hasparticles=false}
  - potion{t=DARKNESS;d=160;l=1;hasicon=true;hasparticles=false}
  - potion{t=WEAKNESS;d=160;l=4;hasicon=true;hasparticles=false}

  # - aura{auraname=xoetxoet;os=xoetxoet_start;ot=xoetxoet_tick;oe=xoetxoet_end;i=2;d=120}
# xoetxoet_start:
#   Skills:
#   - teleport @target
#   - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self
#   - damage{a=150} @target
#   - delay 20
#   - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self
#   - damage{a=150} @PIR{r=5}
#   - delay 10
#   - effect:particles{particle=reddust;color=#7D26CD;amount=200;hS=0;vS=1.5;speed=0;dir=0,0.5,2} @self
#   - damage{a=140;ia=true} @PIR{r=5}
# xoetxoet_tick:
#   Skills:
#   - damage{a=40} @target ~onAttack
#   - damage{a=10;ia=true} @target ~onAttack
#   - teleport @target ~onTimer:60
# xoetxoet_end:
#   Skills:
#   - teleport @target
#   - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
#   - damage{a=130} @target
#   - throw{velocity=18;velocityY=5} @target
#   - teleport @target
#   - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
#   - damage{a=130} @target
#   - throw{velocity=18;velocityY=5} @target
#   - teleport @target
#   - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
#   - damage{a=130} @target
#   - throw{velocity=18;velocityY=5} @target  
#   - teleport @target
#   - effect:particles{particle=reddust;color=#546EED;amount=200;hS=1.7;vS=1;speed=0;dir=0,0.5,2} @self  
#   - damage{a=130} @target
#   - throw{velocity=18;velocityY=5} @target
Gwen_W:
  Skills:
  - teleport @casterSpawnLocation
  - stun{noknockback=true;d=200} @self
  - message{m="&f[&9Gwen&f] &eSủi vô sương lam đây, ehe :3"} @ThreatTable
  - particlering{p=endrod;points=5;radius=1;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=5;radius=1;a=3;yoffset=3.1;vd=15;vspread=1.;speed=0.07} @casterSpawnLocation
  - orbital{r=1;p=5;rotate=true;reversed=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=10;radius=2;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=10;radius=2;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=2;p=10;rotate=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=15;radius=3;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=15;radius=3;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=3;p=15;rotate=true;reversed=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=20;radius=4;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=20;radius=4;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=4;p=20;rotate=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=25;radius=5;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=25;radius=5;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=5;p=25;rotate=true;reversed=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=30;radius=6;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=30;radius=6;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=6;p=30;rotate=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=35;radius=7;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=35;radius=7;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=7;p=35;reversed=true;rotate=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=40;radius=8;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=40;radius=8;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=8;p=40;rotate=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=45;radius=9;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=45;radius=9;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=9;p=45;rotate=true;os=BMist_Start;ot=BMist_Tick;oh=BMist_Hit;oe=BMist_End;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @casterSpawnLocation
  - delay 10
  - message{m="&f[&9Gwen&f] &eSẵn sàng rồi nè, chiến tiếp nhỉ."} @ThreatTable
  - potion{type=ABSORPTION;lvl=3;duration=100} @self
  - potion{type=WEAKNESS;lvl=2;duration=200} @PIR{r=100}
  - setStance{s=Combat} @self
BMist_Tick:
  Skills:
  - particles{p=crit_magic;a=25;vspread=0.49;s=0.0125;yoffset=1.16} @origin
BMist_Hit:
  Skills:
  - damage{a=95} 
  - heal{amount=140} @caster
  - sound{s=block.amethyst_block.place;pitch=0.1;v=3}
BMist_Start:
  Skills:
  - sound{s=ambient.underwater.enter;v=5} @PIR{r=15}
BMist_End:
  Skills:
  - shield{a=170;ma=5100} @caster
Gwen_W_Enhanced:
  Conditions:
  - stance{s=Combat}
  Skills:
  - teleport @casterSpawnLocation
  - stun{noknockback=true;d=200} @self
  - message{m="&f[&9Gwen&f] &eSủi vô sương lam đây, ehe :3"} @ThreatTable
  - particlering{p=endrod;points=5;radius=1;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=5;radius=1;a=3;yoffset=3.1;vd=15;vspread=1.;speed=0.07} @casterSpawnLocation
  - orbital{r=1;p=5;rotate=true;reversed=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=10;radius=2;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=10;radius=2;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=2;p=10;rotate=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=15;radius=3;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=15;radius=3;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=3;p=15;rotate=true;reversed=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=20;radius=4;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=20;radius=4;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=4;p=20;rotate=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=25;radius=5;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=25;radius=5;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=5;p=25;rotate=true;reversed=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=30;radius=6;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=30;radius=6;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=6;p=30;rotate=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=35;radius=7;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=35;radius=7;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=7;p=35;reversed=true;rotate=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=40;radius=8;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=40;radius=8;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=8;p=40;rotate=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @casterSpawnLocation
  - delay 20
  - particlering{p=endrod;points=45;radius=9;a=3;yoffset=0.08;vd=15} @casterSpawnLocation
  - particlering{p=endrod;points=45;radius=9;a=3;yoffset=3.1;vd=15;vspread=2.8;speed=0.07} @casterSpawnLocation
  - orbital{r=9;p=45;rotate=true;os=BMist_Start_Enhanced;ot=BMist_Tick_Enhanced;oh=BMist_Hit_Enhanced;oe=BMist_End_Enhanced;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @casterSpawnLocation
  - delay 10
  - message{m="&f[&9Gwen&f] &eSẵn sàng rồi nè, chiến tiếp nhỉ."} @ThreatTable
  - potion{type=ABSORPTION;lvl=3;duration=100} @self
  - potion{type=WEAKNESS;lvl=2;duration=200} @PIR{r=100}
  - setStance{s=Combat} @self
BMist_Start_Enhanced:
  Skills:
  - sound{s=ambient.underwater.enter;v=5} @PIR{r=15}
BMist_End_Enhanced:
  Skills:
  - shield{a=670;ma=21000} @caster
BMist_Tick_Enhanced:
  Skills:
  - particles{p=crit_magic;a=25;vspread=0.49;s=0.0125;yoffset=1.16} @origin
BMist_Hit_Enhanced:
  Skills:
  - damage{a=950} 
  - heal{amount=1600} @caster
  - sound{s=block.amethyst_block.place;pitch=0.1;v=3}

Gwen_E:
  Conditions:
  - stance{s=Combat}
  Skills:
  - teleport @RandomThreatTarget
  - delay 8
  - message{m="&f[&9Gwen&f] &eBắt được cưng rồi hihi :>"}
  - potion{type=INCREASE_DAMAGE;lvl=2;duration=60} @self
  - potion{t=SPEED;l=3;duration=60} @self
  - skill{s=Enhanced_E} @target
Enhanced_E:
  TargetConditions:
  - healthpercent{p=<50%} castinstead Empowered_Enhanced_E
  Skills:
  - damage{a=95} @target
  - aura{aura=EnhancedE;os=EE-Start;ot=EE-Tick;oe=EE-End;i=5;d=40} @self
Gwen_E_Enhanced:
  Conditions:
  - stance{s=Combat}
  Skills:
  - teleport @RandomThreatTarget
  - delay 8
  - message{m="&f[&9Gwen&f] &eBắt được cưng rồi hihi :>"}
  - potion{type=INCREASE_DAMAGE;lvl=5;duration=40} @self
  - potion{t=SPEED;l=3;duration=100} @self
  - skill{s=Super_Enhanced_E} @target
Super_Enhanced_E:
  TargetConditions:
  - healthpercent{p=<50%} castinstead Empowered_Enhanced_E
  Skills:
  - damage{a=195} @target
  - aura{aura=SuperEE;os=EE-Start;ot=EE-Tick_Enhanced;oe=EE-End;i=5;d=60} @self
EE-Start:
  Skills:
  - message{m="&f[&9Gwen&f] &eThích cưng quá à <3"} @target
EE-Tick:
  Skills:
  - throw{v=3.5;vy=3.5} @target
  - teleport @target
  - damage{a=60} @target
EE-End:
  Skills:
  - message{m="&f[&9Gwen&f] &eTha cưng ó :3"} @target
  - potion{t=REGENERATION;l=1;d=20} @target
EE-Tick_Enhanced:
  Skills:
  - throw{v=3.5;vy=3.5} @target
  - teleport @target
  - damage{a=190} @target
Empowered_Enhanced_E:
  Skills:
  - message{m="&f[&9Gwen&f] &eCho mọi người xung quanh cưng bằng máu cưng nha :D"} @target
  - delay 10
  - particlering{pts=45;r=3;p=red_dust;c=#1c0cfc;xs=0.93;zs=0.93;yo=0.07;amount=15} @target
  - damagePercent{percent=0.1;ii=true;is=true;pkb=true} @ThreatTable
  - delay 10
  - particlering{pts=45;r=3;p=red_dust;c=#1c0cfc;xs=0.93;zs=0.93;yo=0.07;amount=15} @target
  - damagePercent{percent=0.1;ii=true;is=true;pkb=true} @ThreatTable
  - delay 10
  - particlering{pts=45;r=3;p=red_dust;c=#1c0cfc;xs=0.93;zs=0.93;yo=0.07;amount=15} @target
  - damagePercent{percent=0.1;ii=true;is=true;pkb=true} @ThreatTable
  - delay 10
  - particlering{pts=45;r=3;p=red_dust;c=#1c0cfc;xs=0.93;zs=0.93;yo=0.07;amount=15} @target
  - damagePercent{percent=0.1;ii=true;is=true;pkb=true} @ThreatTable
Super_Empowered_Enhanced_E:
  Skills:
  - message{m="&f[&9Gwen&f] &eCho mọi người xung quanh cưng bằng máu cưng nha :D"} @target
  - delay 10
  - particlering{pts=45;r=3;p=red_dust;c=#1c0cfc;xs=0.93;zs=0.93;yo=0.07;amount=15} @target
  - damagePercent{percent=0.2;ii=true;is=true;pkb=true} @ThreatTable
  - delay 10
  - particlering{pts=45;r=3;p=red_dust;c=#1c0cfc;xs=0.93;zs=0.93;yo=0.07;amount=15} @target
  - damagePercent{percent=0.2;ii=true;is=true;pkb=true} @ThreatTable
  - delay 10
  - particlering{pts=45;r=3;p=red_dust;c=#1c0cfc;xs=0.93;zs=0.93;yo=0.07;amount=15} @target
  - damagePercent{percent=0.2;ii=true;is=true;pkb=true} @ThreatTable
  - delay 10
  - particlering{pts=45;r=3;p=red_dust;c=#1c0cfc;xs=0.93;zs=0.93;yo=0.07;amount=15} @target
  - damagePercent{percent=0.2;ii=true;is=true;pkb=true} @ThreatTable

Gwen-R:
  Conditions:
  - stance{s=Combat}
  Skills:
  - aura{aura=}
GwenOnDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&f&l>><&sp><mob.name>&6<&sp>đã<&sp>bị<&sp>đánh<&sp>bại<&sp>&f&l<<”;subtitle=“&aAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&d&l<trigger.name>”} @Server
  - message{m="&f[&9<mob.name>&f] &9Chưa mà... Tớ hứa đó...."} @PIR{r=100}
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=6}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PIR{r=50}
HVMTSpawn:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=120;title="&c&k&n|&r<&sp>&c><&sp>&7&k&n|&r&6&lHộ<&sp>Vệ<&sp>Mặt<&sp>Trời&e&k&n|&7<&sp>&c&k&n|";subtitle="&R&L[<&sp>&C&LHoàn<&sp>Chỉnh<&sp>&4-<&sp>&d&lĐã<&sp>Được<&sp>Triệu<&sp>Hồi<&sp>&R&L]"} 
  - delay 20
  - effect:lightning @Self
  - command{c="time set 18000"}
  - throw{velocity=50;velocityY=10} @PlayersInRadius{r=6}
  - effect:particles{p=flame;a=5;vS=0.75;hS=0.75} @Ring{radius=4;points=12}
  - sound{s=entity.elder_guardian.curse;v=5} @self
HVMTDeath:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=120;title=“&f&l>><&sp>&6&lＨộ<&sp>Ｖệ<&sp>Ｍặｔ<&sp>Ｔｒờ&c<&sp>đã<&sp>bị<&sp>sụp<&sp>đổ<&sp>&f&l<<”;subtitle=“&dAnh<&sp>hùng<&sp>khiêu<&sp>chiến<&sp><&co><&sp>&c&l<trigger.name>”} 
  - effect:explosion @self
  - effect:particleline{particle=instantSpell;amount=1;s=0;hs=2;sv=0;syo=5} @Ring{radius=3;points=6}
  - throw{velocity=50;velocityY=50} @PlayersInRadius{r=6}
  - effect:itemspray{item=redstone;amount=40;velocity=5;d=50;} @self
  - sound{s=entity.wolf.howl;v=5} @PlayersOnServer
HVMTDamage:
  Skills:
  - damagepercent{p=0.25;ia=true}
  - sound{s=entity.experience_orb.pickup} @PIR{r=40}
  - effect:particles{particle=instantSpell;a=50;hs=0.1;vs=0.1;s=0.2} @target
HVMTSkill1:
  Skills:
  - effect:particlering{particle=bubble;a=500;r=10;vs=5;points=8} @self
  - sound{s=block.water.ambient} @origin
  - delay 5
  - effect:particlering{particle=bubble;a=500;r=10;vs=5;points=8} @self
  - sound{s=block.water.ambient} @origin
  - delay 5
  - effect:particlering{particle=bubble;a=500;r=10;vs=5;points=8} @self
  - sound{s=block.water.ambient} @origin
  - delay 5
  - sound{s=entity.ghast.shoot;v=3} @Self
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=60;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=90;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=120;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=150;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=30;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=0;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=-30;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=-90;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=-60;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=-120;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=-150;sE=false}
  - projectile{onTick=HVMTSkill1-Tick;onHit=HVMTSkill1-Hit;onEnd=HVMTSkill1-End;v=15;i=1;mr=50;syo=6;hO=-180;sE=false}
HVMTSkill1-Tick:
  Skills:
  - effect:particles{p=crit;amount=20;speed=0;hS=0;vS=0} @origin
  - effect:particles{p=flame;amount=20;speed=0;hS=0.5;vS=0.5} @origin
  - effect:particles{p=flame;amount=50;speed=0;hS=0.25;vS=0.5} @origin
  - sound{s=block.water.ambient} @origin
HVMTSkill1-Hit:
  Skills:
  - damagepercent{p=0.45;ia=true}
  - sound{s=block.water.ambient}
HVMTSkill1-End:
  Skills:
  - effect:particles{particle=bubble;amount=50;hS=0.5;vS=1;speed=0.1}
  - sound{s=block.water.ambient}

HVMTSkill2ChiVienKhanCap:
  Skills:
  - sendtitle{fadein=20;fadeout=20;duration=100;title=“&4&l>>&e&k&n|&r<&sp>&5&lChi<&sp>Viện<&sp>&5&lKhẩn<&sp>Cấp<&sp>&r&e&k&n|&4&l<<”;subtitle=“&7[<&sp><mob.name><&sp>&rđã<&sp>&rvào<&sp>&rtrạng<&sp>&rthái<&sp>&eGiảm<&sp>&eSát<&sp>&eThương<&sp>&7]”} @PlayersOnServer
  - effect:particle{p=firework;t=1;d=0.5} @self
  - summon{type=btgMuaHemini_2024;amount=1} @Ring{radius=8;points=8}
  - effect:particle{p=firework;t=1;d=0.5} @self
  - potion{type=RESISTANCE;duration=287;level=3} @self
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - delay 10
  - lightning{d=1500} @Ring{radius=8;points=8}
  - effect:particlering{p=flame;a=1;r=20;y=0.5;points=128} @self
  - effect:particlering{p=instantSpell;a=1;r=2;y=0.5;points=32} @Ring{radius=8;points=8}
  - damagepercent{p=0.5;ia=true} @PIR{r=40}
HVMTSkill3AnhHoangHonTrongBuoiTanThe:
  Skills:
  - prison{material=stained_glass;duration=50;breakable=true} @PIR{r=20}
  - potion{type=BLINDNESS;lvl=4;duration=100} @PIR{r=40}
  - potion{type=HEALTH_BOOST;d=1200;level=50} @self
  - heal{a=250000} @self
  - potion{type=ABSORPTION;d=200;level=6} @PIR{r=40}
  - sound{s=entity.wolf.howl;v=5} @PIR{r=20}
  - sendtitle{fadein=20;fadeout=20;duration=120;title=“&e&l>>&4&k&n|&r<&sp>&5&lÁnh<&sp>&5&lHoàng<&sp>&5&lHôn&e&l<&sp>&5&lTrong<&sp>&5&lBuổi<&sp>&5&lTận<&sp>&5&lThế<&sp>&4&k&n|&r&e&l<<”;subtitle=“&7[<&sp><mob.name><&sp>&d&lđã<&sp>&d&lkích<&sp>&d&lhoạt<&sp>&d&lhình<&sp>&d&lthái<&sp>&d&lcuối<&sp>&d&lcùng<&sp>&7]”} @PlayersOnServer
  - healpercent{m=0.1} @self
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=0;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - delay 5
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=1;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - delay 5
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=2;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - delay 5
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=3;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - delay 5
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=2;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - delay 5
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=1;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=0;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - delay 5
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=1;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - delay 5
  - effect:particlering{particle=reddust;c=#2A6AEB;amount=20;vspread=0.5;hspread=2;speed=0;yoffset=0.3;points=20;radius=2} @Self >0 1
  - pull{velocity=6} @LivingEntitiesInRadius{r=20}
  - sound{s=entity.bat.takeoff}
  - delay 5
  - damagepercent{p=0.75;ia=true} @PIR{r=9}
  - sound{s=entity.creeper.death}
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=1} @Self
  - delay 1
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=2} @Self
  - delay 1
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=3} @Self
  - delay 1
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=4} @Self
  - delay 1
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=5} @Self
  - delay 1
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=6} @Self
  - delay 1
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=7} @Self
  - delay 1
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=8} @Self
  - delay 1
  - effect:particlering{particle=bubble;amount=40;vspread=0.5;hspread=0.5;speed=0;yoffset=0.3;points=20;radius=9} @Self
  - effect:lightning @Ring{radius=1;points=1}
  - delay 30
  - effect:lightning @Ring{radius=2;points=1}
  - delay 30
  - effect:lightning @Ring{radius=3;points=2}
  - delay 30
  - effect:lightning @Ring{radius=4;points=2}
  - delay 30
  - effect:lightning @Ring{radius=5;points=3}
  - delay 30
  - effect:lightning @Ring{radius=6;points=3}
  - delay 100
  - damagepercent{p=0.25;ia=true} @PIR{r=50}
  - lightning{d=45;ignorearmor=true} @PIR{r=50}
  - sound{s=entity.enderdragon.growl;v=1} 
  - delay 10
  - damage{a=75000} @PIR{r=50}
  - lightning{d=45;ignorearmor=true} @PIR{r=50}
  - sound{s=entity.enderdragon.growl;v=1} 
  - delay 10
  - damage{a=75000} @PIR{r=50}
  - lightning{d=45;ignorearmor=true} @PIR{r=50}
  - sound{s=entity.enderdragon.growl;v=1} 
  - delay 10
  - damage{a=75000} @PIR{r=50}
  - lightning{d=45;ignorearmor=true} @PIR{r=50}
  - sound{s=entity.enderdragon.growl;v=1} 
  - delay 10
  - damage{a=75000} @PIR{r=50}
  - sound{s=entity.player.breath;p=1}
  - delay 20 
MuaHeminispawner:
  Skills:
  - delay 120
  - teleport @target
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - teleport @target
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - teleport @target
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - damagepercent{p=0.1;ia=true} @PIR{r=15}
  - delay 20
  - effect:particle{p=firework;t=1;d=0.5} @self
  - delay 2