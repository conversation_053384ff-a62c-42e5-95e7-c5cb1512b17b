treviusGeneral:
  Cooldown: 3
  Conditions:
  - targetinlineofsight{} true
  - incombat true
  Skills: 
  - randomskill{skills=treviusMechanic2,treviusMechanic3}
  
minionGeneral:
  Cooldown: 3
  Conditions:
  - targetinlineofsight{} true
  - incombat true
  Skills: 
  - randomskill{skills=minion<PERSON>echanic1,minionMechanic2}

minion2General:
  Cooldown: 3
  Conditions:
  - targetinlineofsight{} true
  - incombat true
  Skills: 
  - randomskill{skills=minion2Mechanic1,minion2Mechanic2}
  
#SKILLS   
trevius_spawn:
  Skills:
  - effect:sound{s=entity.polar_bear.warning;v=5;p=0} @self
  - effect:sound{s=block.gravel.break;v=5;p=0.1} @self
 
flinch:
  Cooldown: 0.5
  Skills:
  - state{s=flinch} @self
  
treviusMechanic1:
  Cooldown: 2
  Skills:
  - state{mid=Trevius;s=charge;li=3;lo=3} @self ~onAttack
  - effect:blockmask{m=soul_sand;r=5;d=65} @self ~onTimer:1200
  - effect:sound{s=block.fire.extinguish;v=2;p=1;}
  - effect:particles{p=reddust;color=#3e383e;size=50;a=5}
  - damage{a=11;ignorearmor=true;} @PIR{r=10}
  - potion{type=SLOW;d=60;level=3;} @PIR{r=10}
  - effect:sound{s=entity.polar_bear.warning;v=5;p=1} @self
  - effect:sound{s=entity.polar_bear.warning;v=5;p=0.9} @self
  - effect:sound{s=entity.polar_bear.warning;v=5;p=0.8} @self
  - effect:sound{s=entity.polar_bear.warning;v=5;p=0.7} @self
  - delay 20
  - slash{y=3;w=4;h=4;mpd=true;a=150;Pitch=20;oP=[ - effect:particles{p=CRIT} ];roll=50;d=6}
  - slash{y=3;w=4;h=4;mpd=true;a=150;Pitch=30;oP=[ - effect:particles{p=CRIT} ];roll=50;d=6}
  - slash{y=3;w=4;h=4;mpd=true;a=150;Pitch=40;oP=[ - effect:particles{p=CRIT} ];roll=50;d=6}

treviusMechanic2:
  Cooldown: 4
  Skills:
  - state{mid=Trevius;s=left_punch;li=3;lo=3} @self ~onAttack
  - effect:particlebox{particle=flame;amount=200;radius=5} @self
  - damage{a=11;ignorearmor=false;} @PIR{r=10}

treviusMechanic3:
  Cooldown: 4
  Skills:
  - state{mid=Trevius;s=right_punch;li=3;lo=3} @self ~onAttack
  - recoil{r=1;pitch=-5to-5} @self ~onAttack
  - damage{a=11;ignorearmor=false;} @PIR{r=10}
  
treviusMechanic4:
  Cooldown: 4
  Conditions:
  - targetinlineofsight{} true
  - incombat true
  Skills:
  - state{mid=Trevius;s=smash;li=3;lo=3} @self ~onAttack
  - effect:particlebox{particle=flame;amount=200;radius=5} @self
  - damage{a=11;ignorearmor=false;} @PIR{r=10}
  - particlering{p=dustColorTransition;color1=#1b1a1c;color2=#69666b;size=2;a=10;r=8;points=16;hs=1;vs=1;repeat=1;repeatInterval=5} @self
#minions
minion1_spawn:
  Skills:
  - effect:sound{s=entity.polar_bear.warning;v=5;p=0} @self
  - effect:sound{s=block.gravel.break;v=5;p=0.1} @self
  
minionMechanic1:
  Cooldown: 4
  Skills:
  - state{mid=minion1;s=left_hit;li=3;lo=3} @self ~onAttack
  - damage{a=11;ignorearmor=false;} @PIR{r=4}

minionMechanic2:
  Cooldown: 4
  Skills:
  - state{mid=minion1;s=right_hit;li=3;lo=3} @self ~onAttack
  - damage{a=11;ignorearmor=false;} @PIR{r=4}
#minion2
minion2_spawn:
  Skills:
  - effect:sound{s=entity.polar_bear.warning;v=5;p=0} @self
  - effect:sound{s=block.gravel.break;v=5;p=0.1} @self
  
minion2Mechanic1:
  Cooldown: 4
  Skills:
  - state{mid=minion2;s=patada;li=3;lo=3} @self ~onAttack
  - delay 15
  - damage{a=11;ignorearmor=false;} @PIR{r=4}
  
minion2Mechanic2:
  Cooldown: 4
  Skills:
  - state{mid=minion2;s=chargue;li=3;lo=3} @self ~onAttack
  - delay 20
  - damage{a=11;ignorearmor=false;} @PIR{r=4}

###PET

PetTrevius_spawn:
  Skills:
   - effect:particles{particle=fishing;amount=50;hS=.45;vS=.25} @self
   - effect:particles{particle=splash;amount=50;hS=.45;vS=.35} @self
   - delay 20
   - effect:particles{particle=fishing;amount=50;hS=.45;vS=.25} @self
   - effect:particles{particle=splash;amount=50;hS=.45;vS=.35} @self

PetTrevius_Despawn:
  Skills:
   - potion{type=SLOW;duration=200;level=255} @self
   - sound{s=entity.cat.stray_ambient;p=1.8;v=1} @self
   - state{s=despawn;li=0;lo=0} @self
   - delay 19
   - lunge{velocity=0.6;velocityY=1} @Forward{f=0.1;y=1}
   - delay 6
   - effect:particlesphere{particle=EXPLOSION;amount=200;radius=0.6} @ModelPart{m=nocsy_pikachu;p=body;offset=GLOBAL;y=0.05;em=false}
   - effect:particlesphere{particle=EXPLOSION;amount=200;radius=0.3} @ModelPart{m=nocsy_pikachu;p=body;offset=GLOBAL;y=0.05;em=false}
   - remove @self

PetTrevius_interact:
  Cooldown: 6
  Skills:
  - setAI{ai=false} @self
  - sound{s=entity.cat.beg_for_food;p=1.8;v=1} @self
  - state{s=interact} @self
  - delay 57
  - setAI{ai=true} @self