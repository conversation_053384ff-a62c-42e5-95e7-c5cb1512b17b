package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;
import java.util.UUID;

/**
 * Listener cho slash commands
 */
public class SlashListener extends ListenerAdapter {



    @Override
    public void onSlashCommandInteraction(SlashCommandInteractionEvent event) {
        String commandName = event.getName();

        switch (commandName) {
            case "connect":
                handleConnectCommand(event);
                break;
            case "link":
                handleLinkCommand(event);
                break;
            case "unlink":
                handleUnlinkCommand(event);
                break;
            case "storage":
                handleStorageCommand(event);
                break;
            case "sell":
                handleSellCommand(event);
                break;
            case "help":
                handleHelpCommand(event);
                break;
            default:
                event.reply("❌ Unknown command: " + commandName)
                    .setEphemeral(true)
                    .queue();
                break;
        }
    }

    /**
     * <PERSON><PERSON> lý connect command
     */
    private void handleConnectCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String authorId = event.getUser().getId();
            String code = generateCode();

            // Lưu vào bridge
            Library.bridge.write(code, authorId);

            // Tạo response
            String response = String.format(
                "✅ **Mã kết nối của bạn:** `%s`\n\n" +
                "🎮 **Cách sử dụng:**\n" +
                "Sử dụng lệnh `/connect %s` trong game để kết nối tài khoản!\n\n" +
                "⏰ **Thời gian hết hạn:** %d giây\n" +
                "🔒 **Lưu ý:** Mã này chỉ sử dụng được một lần!",
                code, code, Library.config.getCodeTimeout()
            );

            // Gửi response
            event.getHook().editOriginal(response).queue(
                success -> SbMagicHook.info("Đã tạo mã kết nối " + code + " cho Discord user: " + authorId),
                error -> SbMagicHook.error("Lỗi khi gửi response: " + error.getMessage())
            );

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleConnectCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi tạo mã kết nối!").queue();
        }
    }

    /**
     * Xử lý unlink command
     */
    private void handleUnlinkCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String discordId = event.getUser().getId();

            // Kiểm tra xem user đã link chưa
            if (!Library.storage.userConnected(discordId)) {
                event.getHook().editOriginal("❌ Tài khoản của bạn chưa được liên kết!").queue();
                return;
            }

            // Lấy UUID và unlink tài khoản
            UUID minecraftUuid = Library.storage.getMinecraftUUID(discordId);
            String minecraftName = minecraftUuid != null ? minecraftUuid.toString() : "Unknown";
            Library.storage.disconnect(discordId);

            String response = String.format(
                "✅ **Đã hủy liên kết thành công!**\n\n" +
                "🎮 **Tài khoản Minecraft:** `%s`\n" +
                "🔗 **Trạng thái:** Đã hủy liên kết",
                minecraftName
            );

            event.getHook().editOriginal(response).queue();
            SbMagicHook.info("User " + discordId + " đã hủy liên kết với " + minecraftName);

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleUnlinkCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi hủy liên kết!").queue();
        }
    }

    /**
     * Xử lý link command (alias cho connect)
     */
    private void handleLinkCommand(SlashCommandInteractionEvent event) {
        handleConnectCommand(event);
    }

    /**
     * Xử lý storage command
     */
    private void handleStorageCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String discordId = event.getUser().getId();

            // Kiểm tra xem user đã link chưa
            if (!Library.storage.userConnected(discordId)) {
                event.getHook().editOriginal("❌ Bạn cần liên kết tài khoản trước! Sử dụng `/connect`").queue();
                return;
            }

            UUID minecraftUuid = Library.storage.getMinecraftUUID(discordId);

            // Lấy thông tin storage từ ExtraStorage
            if (Library.extraStorageHook != null) {
                try {
                    boolean storageStatus = Library.extraStorageHook.getStorageStatus(minecraftUuid);
                    long totalSpace = Library.extraStorageHook.getStorageSpace(minecraftUuid);
                    long usedSpace = Library.extraStorageHook.getUsedSpace(minecraftUuid);

                    String response = String.format(
                        "� **Thông tin kho của bạn**\n\n" +
                        "🎮 **Tài khoản:** `%s`\n" +
                        "� **Trạng thái kho:** %s\n" +
                        "📊 **Dung lượng:** %,d items\n\n" +
                        "� **Sử dụng:** `/sell <item> [amount]` để bán items",
                        minecraftUuid.toString(),
                        storageStatus ? "✅ Hoạt động" : "❌ Tắt",
                        totalSpace == -1 ? "♾️ Không giới hạn" : String.format("%,d / %,d", usedSpace, totalSpace)
                    );

                    event.getHook().editOriginal(response).queue();
                } catch (Exception e) {
                    event.getHook().editOriginal("❌ Không thể lấy thông tin kho! Hãy thử lại sau.").queue();
                }
            } else {
                event.getHook().editOriginal("❌ ExtraStorage chưa được kết nối!").queue();
            }

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleStorageCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi lấy thông tin kho!").queue();
        }
    }

    /**
     * Xử lý sell command
     */
    private void handleSellCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String discordId = event.getUser().getId();

            // Kiểm tra xem user đã link chưa
            if (!Library.storage.userConnected(discordId)) {
                event.getHook().editOriginal("❌ Bạn cần liên kết tài khoản trước! Sử dụng `/connect`").queue();
                return;
            }

            // TODO: Implement sell logic với options từ slash command
            String response = "🛒 **Tính năng bán hàng**\n\n" +
                "� **Cách sử dụng:**\n" +
                "`/sell <item> [amount]`\n\n" +
                "📝 **Ví dụ:**\n" +
                "• `/sell diamond 64` - Bán 64 kim cương\n" +
                "• `/sell iron_ingot` - Bán tất cả iron ingot\n\n" +
                "⚠️ **Lưu ý:** Tính năng đang được phát triển...";

            event.getHook().editOriginal(response).queue();

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleSellCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi xử lý lệnh bán!").queue();
        }
    }

    /**
     * Xử lý help command
     */
    private void handleHelpCommand(SlashCommandInteractionEvent event) {
        event.deferReply(true).queue();

        try {
            String response = "📚 **Danh sách lệnh SbMagicHook**\n\n" +
                "🔗 **Kết nối tài khoản:**\n" +
                "• `/connect` - Tạo mã kết nối Discord với Minecraft\n" +
                "• `/unlink` - Hủy liên kết tài khoản\n\n" +
                "📦 **Quản lý kho:**\n" +
                "• `/storage` - Xem thông tin kho ExtraStorage\n" +
                "• `/sell <item> [amount]` - Bán items từ kho\n\n" +
                "❓ **Hỗ trợ:**\n" +
                "• `/help` - Hiển thị danh sách lệnh này\n\n" +
                "💡 **Lưu ý:** Cần liên kết tài khoản trước khi sử dụng các lệnh kho!";

            event.getHook().editOriginal(response).queue();

        } catch (Exception e) {
            SbMagicHook.error("Lỗi trong handleHelpCommand: " + e.getMessage());
            event.getHook().editOriginal("❌ Có lỗi xảy ra khi hiển thị help!").queue();
        }
    }

    /**
     * Tạo mã kết nối ngẫu nhiên
     */
    private String generateCode() {
        final char[] CHAR_MAP = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray();
        final int CODE_LENGTH = 6;

        java.util.Random random = new java.util.Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHAR_MAP[random.nextInt(CHAR_MAP.length)]);
        }
        return code.toString();
    }
    
}
