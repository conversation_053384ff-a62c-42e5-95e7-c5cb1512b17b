package shyrcs.extrastoragehook;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import shyrcs.extrastoragehook.bridge.BridgeHook;
import shyrcs.extrastoragehook.bridge.StorageHook;

import java.util.UUID;

/**
 * Test class cho SbMagicHook plugin
 * <PERSON><PERSON><PERSON> tra các tính năng cơ bản
 */
public class SbMagicHookTest {
    
    private BridgeHook bridgeHook;
    private StorageHook storageHook;
    
    @BeforeEach
    void setUp() {
        bridgeHook = new BridgeHook(6000L); // 5 minutes timeout
        storageHook = new StorageHook();
    }
    
    @Test
    @DisplayName("Test BridgeHook - Tạo và xóa mã kết nối")
    void testBridgeHookBasicOperations() {
        String testCode = "TEST123";
        String testDiscordId = "123456789";
        
        // Test write và read
        bridgeHook.write(testCode, testDiscordId);
        assertTrue(bridgeHook.keyExisted(testCode), "Mã kết nối phải tồn tại sau khi write");
        assertEquals(testDiscordId, bridgeHook.getID(testCode), "Discord ID phải khớp");
        
        // Test delete
        bridgeHook.delete(testCode);
        assertFalse(bridgeHook.keyExisted(testCode), "Mã kết nối phải bị xóa sau khi delete");
        assertNull(bridgeHook.getID(testCode), "Không được trả về ID sau khi xóa");
    }
    
    @Test
    @DisplayName("Test BridgeHook - Timeout settings")
    void testBridgeHookTimeout() {
        assertEquals(6000L, bridgeHook.getTimeOut(), "Timeout ticks phải đúng");
        assertEquals(300L, bridgeHook.getTimeOutSeconds(), "Timeout seconds phải đúng");
    }
    
    @Test
    @DisplayName("Test BridgeHook - Generate key")
    void testGenerateKey() {
        char[] charMap = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray();
        String key1 = BridgeHook.generateKey(6, charMap);
        String key2 = BridgeHook.generateKey(6, charMap);
        
        assertEquals(6, key1.length(), "Key phải có độ dài đúng");
        assertEquals(6, key2.length(), "Key phải có độ dài đúng");
        assertNotEquals(key1, key2, "Hai key phải khác nhau");
        
        // Kiểm tra key chỉ chứa ký tự hợp lệ
        for (char c : key1.toCharArray()) {
            assertTrue(new String(charMap).indexOf(c) >= 0, "Key chỉ được chứa ký tự hợp lệ");
        }
    }
    
    @Test
    @DisplayName("Test StorageHook - Kết nối và ngắt kết nối")
    void testStorageHookConnections() {
        String testDiscordId = "987654321";
        UUID testMinecraftUuid = UUID.randomUUID();
        
        // Test connect
        storageHook.connect(testDiscordId, testMinecraftUuid);
        assertTrue(storageHook.userConnected(testDiscordId), "Discord user phải được kết nối");
        assertTrue(storageHook.playerConnected(testMinecraftUuid), "Minecraft player phải được kết nối");
        
        // Test get IDs
        assertEquals(testMinecraftUuid, storageHook.getMinecraftUUID(testDiscordId), "UUID phải khớp");
        assertEquals(testDiscordId, storageHook.getDiscordID(testMinecraftUuid), "Discord ID phải khớp");
        
        // Test disconnect
        storageHook.disconnect(testDiscordId);
        assertFalse(storageHook.userConnected(testDiscordId), "Discord user phải bị ngắt kết nối");
        assertFalse(storageHook.playerConnected(testMinecraftUuid), "Minecraft player phải bị ngắt kết nối");
    }
    
    @Test
    @DisplayName("Test StorageHook - Multiple connections")
    void testStorageHookMultipleConnections() {
        String discordId1 = "111111111";
        String discordId2 = "222222222";
        UUID uuid1 = UUID.randomUUID();
        UUID uuid2 = UUID.randomUUID();
        
        // Kết nối nhiều tài khoản
        storageHook.connect(discordId1, uuid1);
        storageHook.connect(discordId2, uuid2);
        
        assertEquals(2, storageHook.getConnectionCount(), "Phải có 2 kết nối");
        
        // Kiểm tra không bị lẫn lộn
        assertEquals(uuid1, storageHook.getMinecraftUUID(discordId1));
        assertEquals(uuid2, storageHook.getMinecraftUUID(discordId2));
        assertEquals(discordId1, storageHook.getDiscordID(uuid1));
        assertEquals(discordId2, storageHook.getDiscordID(uuid2));
        
        // Test clear all
        storageHook.clearAll();
        assertEquals(0, storageHook.getConnectionCount(), "Tất cả kết nối phải bị xóa");
    }
    
    @Test
    @DisplayName("Test StorageHook - Invalid operations")
    void testStorageHookInvalidOperations() {
        // Test với Discord ID không tồn tại
        assertNull(storageHook.getMinecraftUUID("nonexistent"), "Phải trả về null cho ID không tồn tại");
        assertFalse(storageHook.userConnected("nonexistent"), "Phải trả về false cho ID không tồn tại");
        
        // Test với UUID không tồn tại
        UUID randomUuid = UUID.randomUUID();
        assertNull(storageHook.getDiscordID(randomUuid), "Phải trả về null cho UUID không tồn tại");
        assertFalse(storageHook.playerConnected(randomUuid), "Phải trả về false cho UUID không tồn tại");
    }
    
    @Test
    @DisplayName("Test Color code conversion")
    void testColorCodeConversion() {
        String input = "&aGreen &cRed &6Gold";
        String expected = "§aGreen §cRed §6Gold";
        String result = SbMagicHook.color(input);
        
        assertEquals(expected, result, "Color codes phải được chuyển đổi đúng");
    }
    
    @Test
    @DisplayName("Test Thread safety")
    void testThreadSafety() throws InterruptedException {
        final int threadCount = 10;
        final int operationsPerThread = 100;
        Thread[] threads = new Thread[threadCount];
        
        // Test BridgeHook thread safety
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    String code = "CODE" + threadId + "_" + j;
                    String discordId = "DISCORD" + threadId + "_" + j;
                    
                    bridgeHook.write(code, discordId);
                    assertTrue(bridgeHook.keyExisted(code));
                    assertEquals(discordId, bridgeHook.getID(code));
                    bridgeHook.delete(code);
                    assertFalse(bridgeHook.keyExisted(code));
                }
            });
        }
        
        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }
        
        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Verify final state
        assertEquals(0, bridgeHook.size(), "Tất cả codes phải bị xóa");
    }
}
