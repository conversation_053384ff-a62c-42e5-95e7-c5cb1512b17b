command /chuyendoi <text> [<player>]:
    permission: chuyendoi.use
    trigger:
        set {_type} to arg-1
        
        # determine target player
        if arg-2 is set:
            # command run from console
            set {_target} to arg-2
        else:
            # command run by player
            set {_target} to player
        
        if {_target} is not set:
            send "&cPlayer not found or command must be run by a player!" to console
            stop

        if {_type} is "COAL":
            set {_block_material} to "COAL_BLOCK"
            set {_ingot_material} to "COAL"
            set {_display_name} to "&8Than"
        else if {_type} is "LAPIS":
            set {_block_material} to "LAPIS_BLOCK"
            set {_ingot_material} to "LAPIS_LAZULI"
            set {_display_name} to "&9Lưu Ly"
        else if {_type} is "REDSTONE":
            set {_block_material} to "REDSTONE_BLOCK"
            set {_ingot_material} to "REDSTONE"
            set {_display_name} to "&cĐá Đỏ"
        else if {_type} is "COPPER":
            set {_block_material} to "COPPER_BLOCK"
            set {_ingot_material} to "COPPER_INGOT"
            set {_display_name} to "<#A59616><PERSON><PERSON><PERSON>"
        else if {_type} is "IRON":
            set {_block_material} to "IRON_BLOCK"
            set {_ingot_material} to "IRON_INGOT"
            set {_display_name} to "&fSắt"
        else if {_type} is "GOLD":
            set {_block_material} to "GOLD_BLOCK"
            set {_ingot_material} to "GOLD_INGOT"
            set {_display_name} to "&eVàng"
        else if {_type} is "DIAMOND":
            set {_block_material} to "DIAMOND_BLOCK"
            set {_ingot_material} to "DIAMOND"
            set {_display_name} to "&bKim Cương"
        else if {_type} is "EMERALD":
            set {_block_material} to "EMERALD_BLOCK"
            set {_ingot_material} to "EMERALD"
            set {_display_name} to "&aNgọc Lục Bảo"
        else if {_type} is "NETHERITE":
            set {_block_material} to "NETHERITE_BLOCK"
            set {_ingot_material} to "NETHERITE_INGOT"
            set {_display_name} to "<#5B5A4A>Netherite"
        else:
            send "&cLoại block không hợp lệ!"
            stop

        set {_placeholder} to "exstorage_quantity_%{_block_material}%"
        set {_block_amount} to placeholder {_placeholder} for {_target} parsed as number
        
        if {_block_amount} < 1:
            send "&c%{_target}% không có %{_block_material}% để chuyển đổi!" to console
            send "&cBạn không có %{_block_material}% để chuyển đổi!" to {_target}
            stop

        set {_free_space} to placeholder "exstorage_free_space" for {_target} parsed as number
        
        if {_free_space} is -1:
            set {_blocks_to_convert} to {_block_amount}
            set {_convert_amount} to {_blocks_to_convert} * 9
        else:
            set {_total_ingots} to {_block_amount} * 9
            
            if {_free_space} >= {_total_ingots}:
                set {_blocks_to_convert} to {_block_amount}
                set {_convert_amount} to {_total_ingots}
            else:
                set {_blocks_to_convert} to floor({_free_space} / 9)
                set {_convert_amount} to {_blocks_to_convert} * 9
                
                if {_blocks_to_convert} < 1:
                    send "&c%{_target}% không có đủ slot kho trống để chuyển đổi! Cần ít nhất 9 slot kho trống." to console
                    send "&cBạn không có đủ slot kho trống để chuyển đổi! Cần ít nhất &e9 slot kho trống &cđể chuyển đổi 1 block." to {_target}
                    stop
        
        execute console command "esadmin take %{_block_material}% %{_blocks_to_convert}% %{_target}%"
        execute console command "esadmin add %{_ingot_material}% %{_convert_amount}% %{_target}%"

        if {_free_space} is -1:
            send "&a%{_target}% đã chuyển đổi thành công %{_blocks_to_convert}% Khối %{_display_name}% &athành %{_convert_amount}% %{_display_name}%" to console
            send "&aBạn đã chuyển đổi thành công &e%{_blocks_to_convert}%&a Khối %{_display_name}% &athành &e%{_convert_amount}% %{_display_name}%" to {_target}
        else:
            if {_blocks_to_convert} < {_block_amount}:
                set {_remaining_blocks} to {_block_amount} - {_blocks_to_convert}
                send "&a%{_target}% đã chuyển đổi thành công %{_blocks_to_convert}% Khối %{_display_name}% &athành %{_convert_amount}% %{_display_name}% &7(Còn lại %{_remaining_blocks}% khối do hết slot)" to console
                send "&aBạn đã chuyển đổi thành công &e%{_blocks_to_convert}%&a Khối %{_display_name}% &athành &e%{_convert_amount}% %{_display_name}% &7(Còn lại &e%{_display_name}% &7Khối do hết slot kho)" to {_target}
            else:
                send "&a%{_target}% đã chuyển đổi thành công %{_blocks_to_convert}% Khối %{_display_name}% &athành %{_convert_amount}% %{_display_name}%" to console
                send "&aBạn đã chuyển đổi thành công &e%{_blocks_to_convert}%&a Khối %{_display_name}% &athành &e%{_convert_amount}% %{_display_name}%" to {_target}