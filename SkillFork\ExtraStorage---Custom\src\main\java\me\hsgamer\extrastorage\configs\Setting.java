package me.hsgamer.extrastorage.configs;

import lombok.Getter;
import me.hsgamer.extrastorage.Debug;
import me.hsgamer.extrastorage.configs.types.BukkitConfig;
import me.hsgamer.extrastorage.hooks.economy.*;
import me.hsgamer.extrastorage.util.Digital;
import me.hsgamer.extrastorage.util.ItemUtil;
import me.hsgamer.extrastorage.util.Utils;
import org.bukkit.NamespacedKey;
import org.bukkit.Registry;
import org.bukkit.Sound;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public final class Setting
        extends BukkitConfig {

    private boolean checkForUpdates;

    // Database connection:
    private String DBType, DBDatabase, DBHost, DBUsername, DBPassword, DBTable;
    private int DBPort;

    private String dateFormat;

    private boolean onlyStoreWhenInvFull;

    private int autoUpdateTime;
    private boolean restartOnChange;

    private boolean logSales, logTransfer, logWithdraw;

    private boolean usePriceProvider;

    private EconomyProvider economyProvider;
    private String currency;

    private long maxSpace;
    private boolean blockedMining;

    private boolean autoStoreItem, pickupToStorage;
    private Sound pickupSound;

    private List<String> blacklistWorlds, blacklist, whitelist;
    private Map<String, String> name;

    public Setting() {
        super("config.yml");
    }

    @Override
    public void setup() {
        this.checkForUpdates = config.getBoolean("CheckForUpdates");

        this.DBType = config.getString("Database.Type", "SQLite").toLowerCase();
        this.DBDatabase = config.getString("Database.Database", "database");
        this.DBHost = config.getString("Database.Host", "127.0.0.1");
        this.DBPort = config.getInt("Database.Port", 3306);
        this.DBUsername = config.getString("Database.Username", "root");
        this.DBPassword = config.getString("Database.Password", "");
        this.DBTable = config.getString("Database.Table", "exstorage_data");

        this.dateFormat = config.getString("DateFormat", "MM/dd/yyyy HH:mm:ss");

        this.onlyStoreWhenInvFull = config.getBoolean("OnlyStoreWhenInvFull");

        String economyProvider = config.getString("Economy.Provider", "VAULT").toUpperCase();
        switch (economyProvider) {
            case "SHOPGUIPLUS":
                this.economyProvider = new ShopGuiPlusHook();
                break;
            case "ECONOMYSHOPGUI":
                this.economyProvider = new EconomyShopGuiHook();
                break;
            case "PLAYERPOINTS":
                this.economyProvider = new PlayerPointsHook();
                break;
            case "TOKENMANAGER":
                this.economyProvider = new TokenManagerHook();
                break;
            case "ULTRAECONOMY":
                this.economyProvider = new UltraEconomyHook();
                break;
            case "COINSENGINE":
                this.economyProvider = new CoinsEngineHook();
                break;
            case "VAULT":
                this.economyProvider = new VaultHook();
                break;
            default:
                this.economyProvider = new NoneEconomyHook();
                break;
        }
        if (!this.economyProvider.isHooked()) {
            this.economyProvider = new NoneEconomyHook();
        }
        this.usePriceProvider = config.getBoolean("Economy.UsePriceProvider", false);
        this.currency = config.getString("Economy.Currency", "");

        this.autoUpdateTime = Digital.getBetween(10, Integer.MAX_VALUE, config.getInt("AutoUpdateTime", 30));
        this.restartOnChange = config.getBoolean("RestartOnChange");

        this.logSales = config.getBoolean("Log.Sales");
        this.logTransfer = config.getBoolean("Log.Transfer");
        this.logWithdraw = config.getBoolean("Log.Withdraw");

        this.maxSpace = config.getLong("MaxSpace", 100000);
        this.blockedMining = config.getBoolean("BlockedMining", true);

        this.autoStoreItem = config.getBoolean("AutoStoreItem", true);
        this.pickupToStorage = config.getBoolean("PickupToStorage");
        String soundName = config.getString("PickupSound", "__NO_SOUND__").toUpperCase();
        this.pickupSound = Sound.BLOCK_NOTE_BLOCK_PLING; // Default sound
        
        if (!soundName.equals("__NO_SOUND__")) {
            try {
                try {
                    NamespacedKey soundKey = NamespacedKey.minecraft(soundName.toLowerCase());
                    Sound registrySound = Registry.SOUNDS.get(soundKey);
                    if (registrySound != null) {
                        this.pickupSound = registrySound;
                    } else {
                        if (instance != null && instance.getLogger() != null) {
                            instance.getLogger().warning("Sound not found in registry: " + soundName + ". Using default sound.");
                        }
                    }
                } catch (Exception registryException) {
                    if (instance != null && instance.getLogger() != null) {
                        instance.getLogger().warning("Invalid sound name: " + soundName + ". Using default sound.");
                    }
                }
            } catch (Exception e) {
                if (instance != null && instance.getLogger() != null) {
                    instance.getLogger().warning("Error loading sound: " + soundName + ". Using default sound: " + e.getMessage());
                }
            }
        }

        this.blacklistWorlds = config.getStringList("BlacklistWorlds");
        this.blacklist = config.getStringList("Blacklist")
                .stream()
                .map(string -> {
                    if (!string.contains(":")) return string.concat(":0");
                    String key = string.substring(0, string.indexOf(':'));
                    if (key.matches("(?ium)(I(tems)?A(dder)?|Oraxen)")) return string;
                    return string;
                })
                .map(ItemUtil::normalizeMaterialKey)
                .collect(Collectors.toList());
        this.whitelist = config.getStringList("Whitelist")
                .stream()
                .map(string -> {
                    if (!string.contains(":")) return string.concat(":0");
                    String key = string.substring(0, string.indexOf(':'));
                    if (key.matches("(?ium)(I(tems)?A(dder)?|Oraxen)")) return string;
                    return string;
                })
                .map(ItemUtil::normalizeMaterialKey)
                .filter(key -> (!blacklist.contains(key)))
                .collect(Collectors.toList());

        this.name = new HashMap<>();
        config.getConfigurationSection("FormatName")
                .getKeys(false)
                .forEach(key -> name.put(ItemUtil.normalizeMaterialKey(key), config.getString("FormatName." + key)));

        Debug.enabled = config.getBoolean("Debug");
    }

    public void addToWhitelist(String key) {
        whitelist.add(key);
        this.set("Whitelist", whitelist);
        this.save();
    }

    public void removeFromWhitelist(String key) {
        whitelist.remove(key);
        this.set("Whitelist", whitelist);
        this.save();
    }

    public String getNameFormatted(Object key, boolean colorize) {
        String validKey = ItemUtil.toMaterialKey(key);

        String name = this.name.getOrDefault(validKey, "");
        if (!name.isEmpty()) return (colorize ? name : Utils.stripColor(name));

        ItemUtil.ItemPair pair = ItemUtil.getItem(validKey);
        if (pair.type() != ItemUtil.ItemType.NONE && pair.type() != ItemUtil.ItemType.VANILLA) {
            String finalName = pair.item().getItemMeta().getDisplayName();
            if (!colorize) Utils.stripColor(finalName);
            return finalName;
        }

        String formatName = Utils.formatName(validKey);
        return (colorize ? Utils.colorize("&f" + formatName) : formatName);
    }

    public String getDBType() { return DBType; }
    public String getDBDatabase() { return DBDatabase; }
    public String getDBHost() { return DBHost; }
    public String getDBUsername() { return DBUsername; }
    public String getDBPassword() { return DBPassword; }
    public String getDBTable() { return DBTable; }
    public int getDBPort() { return DBPort; }
    public int getAutoUpdateTime() { return autoUpdateTime; }
    public boolean isLogSales() { return logSales; }
    public boolean isLogTransfer() { return logTransfer; }
    public boolean isLogWithdraw() { return logWithdraw; }
    public boolean isUsePriceProvider() { return usePriceProvider; }
    public boolean isCheckForUpdates() { return checkForUpdates; }
    public String getDateFormat() { return dateFormat; }
    public boolean isOnlyStoreWhenInvFull() { return onlyStoreWhenInvFull; }
    public boolean isRestartOnChange() { return restartOnChange; }
    public EconomyProvider getEconomyProvider() { return economyProvider; }
    public String getCurrency() { return currency; }
    public long getMaxSpace() { return maxSpace; }
    public boolean isBlockedMining() { return blockedMining; }
    public boolean isAutoStoreItem() { return autoStoreItem; }
    public boolean isPickupToStorage() { return pickupToStorage; }
    public Sound getPickupSound() { return pickupSound; }
    public List<String> getBlacklistWorlds() { return blacklistWorlds; }
    public List<String> getBlacklist() { return blacklist; }
    public List<String> getWhitelist() { return whitelist; }
    public Map<String, String> getName() { return name; }

}
