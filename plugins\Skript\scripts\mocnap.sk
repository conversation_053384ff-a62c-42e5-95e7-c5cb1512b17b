options:
    prefix: &6&lMỐC NẠP &8» &f

command /nhanqua:
    aliases: /mocnap, /claim, /rewards
    trigger:
        send "{@prefix}&fĐang kiểm tra các mốc nạp của bạn, vui lòng đợi..."
        check_milestones(player)

command /mocnapadmin [<text>] [<player>]:
    aliases: /mna
    permission: mocnap.admin
    permission message: &cBạn không có quyền sử dụng lệnh này.
    trigger:
        if argument 1 is "check" or "kiemtra":
            if argument 2 is not set:
                send "{@prefix}&cSai cú pháp. Dùng: &e/mna kiemtra <người chơi>"
                stop
            set {_tong_nap_formatted} to placeholder "simppay_total_formatted" from argument 2
            send "{@prefix}Tổng nạp của &e%argument 2%&f (theo Simp<PERSON>ay) là: &a&l%{_tong_nap_formatted}%"
        else if argument 1 is "reset" or "datlai":
            if argument 2 is set:
                delete {mocnap::claimed::%uuid of argument 2%::*}
                send "{@prefix}Đã xóa trạng thái nhận quà của &e%argument 2%&f. <PERSON> có thể nhận lại các mốc quà đã đạt."
            else:
                send "{@prefix}&cSai cú pháp. Dùng: &e/mna reset <người chơi>"
        else:
            send "&7&m------------------&r &6&lMốc Nạp Admin &7&m------------------"
            send "&e/mna kiemtra <người chơi> &f- Xem tổng nạp của người chơi qua SimpPay."
            send "&e/mna reset <người chơi> &f- Reset trạng thái nhận quà của họ."
            send "&7&m-----------------------------------------------------"


function check_milestones(p: player):
    if {_p} is not set:
        stop
    set {_uuid} to uuid of {_p}
    set {_total_string} to placeholder "simppay_total" from {_p}
    if {_total_string} is not set:
        send "{@prefix}&cKhông thể lấy dữ liệu nạp của bạn từ SimpPay. Vui lòng thử lại hoặc liên hệ Admin." to {_p}
        stop
    # Convert string to number
    set {_total} to {_total_string} parsed as number
    if {_total} is not set:
        send "{@prefix}&cDữ liệu nạp không hợp lệ: %{_total_string}%. Vui lòng liên hệ Admin." to {_p}
        stop
    send "{@prefix}&7Debug: Tổng nạp của bạn là %{_total}%" to {_p}
    set {_had_unclaimed} to false
    if {_total} >= 50000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 50000:
            send "{@prefix}&aChúc mừng bạn đã đạt &lMốc nạp 50,000 VNĐ&a!" to {_p}
            make console execute "eco give %{_p}% 8"
            make console execute "give %{_p}% emerald 8"
            make console execute "lp user %{_p}% permission settemp essentials.fly true 3h"
            make console execute "crates givekey %{_p}% donate 1"
            add 50000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 100000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 100000:
            send "{@prefix}&aChúc mừng bạn đã đạt &lMốc nạp 100,000 VNĐ&a!" to {_p}
            make console execute "eco give %{_p}% 16"
            make console execute "give %{_p}% emerald 16"
            make console execute "give %{_p}% netherite_ingot 5"
            make console execute "lp user %{_p}% permission settemp essentials.fly true 6h"
            make console execute "crates givekey %{_p}% donate 2"
            add 100000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 200000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 200000:
            send "{@prefix}&aChúc mừng bạn đã đạt &lMốc nạp 200,000 VNĐ&a!" to {_p}
            make console execute "eco give %{_p}% 24"
            make console execute "give %{_p}% emerald 24"
            make console execute "give %{_p}% netherite_ingot 10"
            make console execute "lp user %{_p}% permission settemp essentials.fly true 9h"
            make console execute "kit cuptrian_200k %{_p}%"
            make console execute "crates givekey %{_p}% donate 3"
            add 200000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 500000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 500000:
            send "{@prefix}&bChúc mừng bạn đã đạt &lMốc nạp 500,000 VNĐ&b!" to {_p}
            make console execute "eco give %{_p}% 32"
            make console execute "give %{_p}% emerald 32"
            make console execute "give %{_p}% netherite_ingot 12"
            make console execute "lp user %{_p}% permission settemp essentials.fly true 12h"
            make console execute "kit kiemtrian_500k %{_p}%"
            make console execute "crates givekey %{_p}% donate 5"
            add 500000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 1000000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 1000000:
            send "{@prefix}&bChúc mừng bạn đã đạt &lMốc nạp 1,000,000 VNĐ&b!" to {_p}
            make console execute "eco give %{_p}% 48"
            make console execute "give %{_p}% emerald 48"
            make console execute "give %{_p}% netherite_ingot 15"
            make console execute "lp user %{_p}% permission settemp essentials.fly true 24h"
            make console execute "kit giaptrian_1m %{_p}%"
            make console execute "crates givekey %{_p}% donate 7"
            add 1000000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 5000000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 5000000:
            send "{@prefix}&dChúc mừng bạn đã đạt &lMốc nạp 5,000,000 VNĐ&d!" to {_p}
            make console execute "eco give %{_p}% 64"
            make console execute "give %{_p}% emerald 64"
            make console execute "give %{_p}% netherite_ingot 20"
            make console execute "lp user %{_p}% permission settemp essentials.fly true 48h"
            make console execute "crates givekey %{_p}% donate 12"
            send "{@prefix} &c&lLIÊN HỆ ADMIN ĐỂ NHẬN KIẾM TRI ÂN TÙY CHỈNH!" to {_p}
            add 5000000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 7500000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 7500000:
            send "{@prefix}&dChúc mừng bạn đã đạt &lMốc nạp 7,500,000 VNĐ&d!" to {_p}
            make console execute "mi give MATERIAL PHIEU_NL_BAC_CAO %{_p}% 7"
            make console execute "give %{_p}% netherite_ingot 25"
            make console execute "lp user %{_p}% parent settemp trialrank 1d"
            make console execute "crates givekey %{_p}% donate 15"
            send "{@prefix} &c&lLIÊN HỆ ADMIN ĐỂ NHẬN KIẾM TRI ÂN TÙY CHỈNH!" to {_p}
            add 7500000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 10000000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 10000000:
            send "{@prefix}&cChúc mừng bạn đã đạt &lMốc nạp 10,000,000 VNĐ&c!" to {_p}
            make console execute "mi give MATERIAL PHIEU_NL_BAC_CAO %{_p}% 14"
            make console execute "give %{_p}% netherite_ingot 30"
            make console execute "lp user %{_p}% parent settemp trialrank 3d"
            make console execute "kit giaptrian_10m %{_p}%"
            make console execute "crates givekey %{_p}% donate 17"
            add 10000000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 15000000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 15000000:
            send "{@prefix}&cChúc mừng bạn đã đạt &lMốc nạp 15,000,000 VNĐ&c!" to {_p}
            make console execute "mi give MATERIAL PHIEU_NL_BAC_CAO %{_p}% 18"
            make console execute "give %{_p}% netherite_ingot 35"
            make console execute "lp user %{_p}% parent settemp trialrank 7d"
            make console execute "crates givekey %{_p}% donate 20"
            send "{@prefix} &c&lLIÊN HỆ ADMIN ĐỂ NHẬN KIẾM TRI ÂN TÙY CHỈNH!" to {_p}
            add 15000000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 20000000:
        if {mocnap::claimed::%{_uuid}%::*} does not contain 20000000:
            send "{@prefix}&cChúc mừng bạn đã đạt &lMốc nạp 20,000,000 VNĐ&c!" to {_p}
            make console execute "mi give MATERIAL PHIEU_NL_BAC_CAO %{_p}% 25"
            make console execute "give %{_p}% netherite_ingot 40"
            make console execute "lp user %{_p}% parent settemp trialrank 14d"
            make console execute "crates givekey %{_p}% donate 25"
            send "{@prefix} &c&lLIÊN HỆ ADMIN ĐỂ NHẬN KIẾM TRI ÂN TÙY CHỈNH!" to {_p}
            add 20000000 to {mocnap::claimed::%{_uuid}%::*}
            set {_had_unclaimed} to true
    if {_total} >= 30000000:
        if {muctieunap::server_claimed::*} does not contain 30000000:
            broadcast "{@prefix}&5&lVĨ ĐẠI! &fServer đã đạt &e&lMốc Nạp 30,000,000 VNĐ&f!"
            add 30000000 to {muctieunap::server_claimed::*}
            loop all players:
                make console execute "mi give MATERIAL PHIEU_NL_BAC_CAO %loop-player% 25"
                make console execute "give %loop-player% netherite_ingot 45"
                make console execute "lp user %loop-player% parent settemp trialrank 28d"
                make console execute "kit giaptrian_30m %loop-player%"
                make console execute "crates givekey %loop-player% donate 25"
                send "&5Bạn đã nhận được quà từ mốc nạp server!" to loop-player
    if {_total} >= 50000000:
        if {muctieunap::server_claimed::*} does not contain 50000000:
            broadcast "{@prefix}&5&lHUYỀN THOẠI! &fServer đã đạt &e&lMốc Nạp 50,000,000 VNĐ&f!"
            broadcast "{@prefix}&cPhần thưởng đặc biệt của mốc này (kiếm/cúp/cung) cần liên hệ Admin để lựa chọn và nhận!"
            add 50000000 to {muctieunap::server_claimed::*}
            loop all players:
                make console execute "mi give MATERIAL PHIEU_NL_BAC_CAO %loop-player% 30"
                make console execute "give %loop-player% netherite_ingot 50"
                make console execute "lp user %loop-player% parent settemp trialrank 30d"
                make console execute "crates givekey %loop-player% donate 30"
                send "&5Bạn đã nhận được quà từ mốc nạp server! LH Admin nhận quà đặc biệt." to loop-player

    if {_had_unclaimed} is false:
        send "{@prefix}&eKhông có mốc nạp nào để nhận hoặc bạn đã nhận hết rồi!" to {_p}