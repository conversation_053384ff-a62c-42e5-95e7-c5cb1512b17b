package shyrcs.extrastoragehook.discord.commands;
import net.dv8tion.jda.api.EmbedBuilder;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.MessageEmbed;
import net.dv8tion.jda.api.entities.channel.middleman.MessageChannel;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.application.PluginBoot;
import shyrcs.extrastoragehook.executor.DiscordExecutor;
import shyrcs.extrastoragehook.SbMagicHook;

import java.awt.*;
import java.text.DecimalFormat;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * Discord command để xem thông tin kho ExtraStorage
 */
public class CommandStorage extends DiscordExecutor {
    
    private static final DecimalFormat formatter = new DecimalFormat("#,###");
    
    public CommandStorage() {
        super("storage", Library.config.getCommand("storage"));
    }
    
    @Override
    public void onSlashCommand(SlashCommandInteractionEvent event) {
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        if (!Library.storage.userConnected(authorId)) {
            event.reply(Library.config.getMessage("not-connected"))
                .setEphemeral(true).queue();
            return;
        }
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    UUID playerUuid = Library.storage.getMinecraftUUID(authorId);
                    if (playerUuid == null) {
                        event.reply(Library.config.getMessage("not-connected"))
                            .setEphemeral(true).queue();
                        return;
                    }
                    
                    OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
                    Object storage = Library.extraStorageHook.getStorage(playerUuid);

                    if (storage == null) {
                        event.getHook().editOriginal("❌ Không thể truy cập kho của bạn!").queue();
                        return;
                    }

                    MessageEmbed embed = buildStorageEmbed(player, playerUuid);
                    event.getHook().editOriginalEmbeds(embed).queue();
                    
                } catch (Exception e) {
                    SbMagicHook.error("Lỗi khi xem thông tin kho: " + e.getMessage());
                    event.getHook().editOriginal(Library.config.getMessage("error")).queue();
                }
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    @Override
    public void onChatCommand(MessageReceivedEvent event) {
        final MessageChannel channel = event.getChannel();
        final Message message = event.getMessage();
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        if (!Library.storage.userConnected(authorId)) {
            channel.sendMessage(Library.config.getMessage("not-connected"))
                .setMessageReference(message).queue();
            return;
        }
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    UUID playerUuid = Library.storage.getMinecraftUUID(authorId);
                    if (playerUuid == null) {
                        channel.sendMessage(Library.config.getMessage("not-connected"))
                            .setMessageReference(message).queue();
                        return;
                    }
                    
                    OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
                    Object storage = Library.extraStorageHook.getStorage(playerUuid);

                    if (storage == null) {
                        channel.sendMessage("❌ Không thể truy cập kho của bạn!")
                            .setMessageReference(message).queue();
                        return;
                    }

                    MessageEmbed embed = buildStorageEmbed(player, playerUuid);
                    channel.sendMessageEmbeds(embed).setMessageReference(message).queue();
                    
                } catch (Exception e) {
                    SbMagicHook.error("Lỗi khi xem thông tin kho: " + e.getMessage());
                    channel.sendMessage(Library.config.getMessage("error"))
                        .setMessageReference(message).queue();
                }
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    /**
     * Tạo embed hiển thị thông tin kho
     */
    private MessageEmbed buildStorageEmbed(OfflinePlayer player, UUID playerUuid) {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("📦 Thông tin kho ExtraStorage");
        embed.setColor(Color.BLUE);

        // Thông tin người chơi
        embed.addField("👤 Người chơi", player.getName(), true);
        boolean isActive = Library.extraStorageHook.getStorageStatus(playerUuid);
        embed.addField("🔄 Trạng thái", isActive ? "✅ Hoạt động" : "❌ Tắt", true);

        // Thông tin không gian
        long totalSpace = Library.extraStorageHook.getStorageSpace(playerUuid);
        long usedSpace = Library.extraStorageHook.getUsedSpace(playerUuid);
        long freeSpace = Library.extraStorageHook.getFreeSpace(playerUuid);

        if (totalSpace == -1) {
            embed.addField("💾 Không gian", "♾️ Không giới hạn", true);
        } else {
            String spaceInfo = String.format("**%s** / **%s** (%s còn trống)",
                formatter.format(usedSpace),
                formatter.format(totalSpace),
                formatter.format(freeSpace)
            );
            embed.addField("💾 Không gian", spaceInfo, true);
        }

        // Danh sách items
        Map<String, Object> items = Library.extraStorageHook.getAllItems(playerUuid);
        if (items == null || items.isEmpty()) {
            embed.addField("📋 Items", Library.config.getMessage("no-items"), false);
        } else {
            StringBuilder itemList = new StringBuilder();
            int count = 0;
            int totalItems = 0;

            for (Map.Entry<String, Object> entry : items.entrySet()) {
                String materialKey = entry.getKey();

                try {
                    // Lấy quantity từ Item object thông qua reflection
                    long quantity = Library.extraStorageHook.getItemAmount(playerUuid, materialKey);

                    if (quantity > 0) {
                        totalItems++;
                        if (count < 10) { // Giới hạn 10 items để tránh embed quá dài
                            String emote = Library.config.getEmote(materialKey);
                            String displayName = formatMaterialName(materialKey);
                            itemList.append(emote).append(" **").append(displayName).append("**: ")
                                .append(formatter.format(quantity)).append("\n");
                            count++;
                        }
                    }
                } catch (Exception e) {
                    // Bỏ qua item có lỗi
                    continue;
                }
            }

            if (count > 0 && totalItems > 10) {
                itemList.append("*... và ").append(totalItems - 10).append(" items khác*");
            }

            if (itemList.length() == 0) {
                embed.addField("📋 Items", Library.config.getMessage("no-items"), false);
            } else {
                embed.addField("📋 Items (" + totalItems + ")", itemList.toString(), false);
            }
        }

        embed.setFooter("SbMagicHook • ExtraStorage Integration");
        embed.setTimestamp(java.time.Instant.now());

        return embed.build();
    }
    
    /**
     * Format tên material để hiển thị đẹp hơn
     */
    private String formatMaterialName(String materialKey) {
        String[] words = materialKey.toLowerCase().replace("_", " ").split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1))
                      .append(" ");
            }
        }
        return result.toString().trim();
    }
}
