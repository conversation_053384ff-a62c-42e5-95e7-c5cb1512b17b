ManiacTestifying:
  Skills:
  - message{m="&f[&dSparkle&f] &cLên nòng thôi!"} @Self
  - delay 20
  - message{m="&f[&dSparkle&f] &cKhai hỏa!"} @Self
  - randomskill{s=RouletteOne,RouletteTwo,Rou<PERSON><PERSON>hree,Rou<PERSON>F<PERSON>,RouletteFive,FailRoulette}
RouletteOne:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
RouletteTwo:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
RouletteThree:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
RouletteFour:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
RouletteFive:
  Skills:
  - message{m="&f[&dSparkle&f] &cĐạn lép hoy à! Trả dame nè. Ehe~"} @Self
  - delay 20
  - damagepercent{p=0.2;ia=true} @PIR{r=20}
  - damage{d=300000;ia=true} @MIR{r=100}
FailRoulette:
  Skills:
  - message{m="&f[&dSparkle&f] &cÍ ẹ... Kẹo đồng thật..."} @Self
  - delay 20
  - suicide @self

# JotPr Skills
Ether_Aptitude:
  Conditions:
  - score{obj=EtherAuraCheck;value=1} cast Ether_End
  Skills:
  #- message{m="&bAura started!"} @self
  - aura{aura=Ether_Screen;os=Ether_Start;ot=Ether_Tick;oe=Ether_End;i=8;d=400;cancelontakedamage=true;cancelonchangeworld=true;cancelonquit=true} @self
Ether_Start:
  Skills:
  - modifytargetscore{obj=EtherAuraCheck;action=SET;value=1} @self
Ether_Tick:
  Skills:
  - modifytargetscore{obj=EtherRing;action=ADD;value=1} @self
  - modifytargetscore{obj=EtherAuraCheck;action=SET;value=1} @self
  - particlering{points=(<caster.score.EtherRing>*8);radius=(<caster.score.EtherRing>/10);p=snowflake;a=3;viewdistance=15;yoffset=0.67} @self
  - potion{t=SLOW;duration=10;l=2;hasicon=true} @EIR{r=5}
  - bloodyscreen{d=5} @PIR{r=5}
  - damage{a=2;preventknockback=true;is=true;ii=true;ia=true;ie=true;ieff=true;ir=true} @EIR{r=5}
Ether_End:
  Skills:
  #- message{m="&bAura ended!"} @self
  - modifytargetscore{obj=EtherRing;action=SET;v=0} @self
  - modifytargetscore{obj=EtherAuraCheck;action=SET;value=0} @self


Aura_Check:
  Conditions:
  - score{obj=EtherAuraCheck;value=1} orElseCast Aura_Fail
  Skills:
  - message{m="&bAura exists!"} @PIR{r=5}
Aura_Fail:
  Skills:
  - message{m="&bAura no exists!"} @PIR{r=5}
Perfected_Symphony:
  Cooldown: <modifier.cooldown>
  Conditions:
  - score{obj=EtherAuraCheck;value=1} orelsecast Broken_Symphony
  Skills:
  - message{m="&b>> &7(&eSkills&7) &3Khởi động&f - <#0E00FA>G<#1B00FA>i<#2700FB>a<#3400FB>i <#4D00FB>Đ<#5A00FC>i<#6600FC>ệ<#7300FC>u <#8C00FD>c<#9900FD>ủ<#A500FD>a <#BF00FE>P<#CB00FE>r<#D800FE>o<#E400FF>x<#F100FF>y&f. &b<<"} @caster
  - message{m="&3Khởi động&f - <#0E00FA>G<#1B00FA>i<#2700FB>a<#3400FB>i <#4D00FB>Đ<#5A00FC>i<#6600FC>ệ<#7300FC>u <#8C00FD>c<#9900FD>ủ<#A500FD>a <#BF00FE>P<#CB00FE>r<#D800FE>o<#E400FF>x<#F100FF>y&f."} @PIR{r=5}
  - orbital{r=2;p=16;rotate=true;os=Symphony_Start;ot=Symphony_Tick;oh=Symphony_Hit;oe=Symphony_End;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @origin
  - delay 40
  - orbital{r=4;p=32;rotate=true;reversed=true;os=Symphony_Start;ot=Symphony_Tick;oh=Symphony_Hit;oe=Symphony_End;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.3} @origin
  - delay 40
  - orbital{r=6;p=48;rotate=true;os=Symphony_Start;ot=Symphony_Tick;oh=Symphony_Hit;oe=Symphony_End;i=1;sourceisorigin=true;d=100;hnp=true;hitradius=1.} @origin
Symphony_Tick:
  Skills:
  - particles{p=crit_magic;a=25;vspread=0 .08;s=0.0125} @origin
Symphony_Hit:
  Skills:
  - damage{a=25} 
  - heal{amount=10} @caster
  - sound{s=block.amethyst_block.place;pitch=0.1;v=3}
Symphony_Start:
  Skills:
  - sound{s=ambient.underwater.enter;v=5} @PIR{r=15}
Symphony_End:
  Skills:
  - shield{a=10;ma=30} @caster

Broken_Symphony:
  Cooldown: <modifier.cooldown>
  Skills:
  - message{m="&b>> &7(&eSkills&7) &3Khởi động&f - <#0E00FA>G<#1B00FA>i<#2700FB>a<#3400FB>i <#4D00FB>Đ<#5A00FC>i<#6600FC>ệ<#7300FC>u <#8C00FD>c<#9900FD>ủ<#A500FD>a <#BF00FE>P<#CB00FE>r<#D800FE>o<#E400FF>x<#F100FF>y&f. &b<<"} @caster
  - message{m="&3Khởi động&f - <#0E00FA>G<#1B00FA>i<#2700FB>a<#3400FB>i <#4D00FB>Đ<#5A00FC>i<#6600FC>ệ<#7300FC>u <#8C00FD>c<#9900FD>ủ<#A500FD>a <#BF00FE>P<#CB00FE>r<#D800FE>o<#E400FF>x<#F100FF>y&f."} @PIR{r=5}
  - delay 30
  - message{m="&b>> &7(&eSkills&7) &3... &b<<"} @caster
  - message{m="&b>> &7(&eSkills&7) &dKhông thành công. Kẻ địch đã bị suy yếu!. &b<<"} @PIR{r=5}
  - potion{t=WEAKNESS;d=80;l=1} @caster
  - delay 30
  - message{m="&b>> &7(&eSkills&7) &4Không thành công. Sử dụng phương án dự phòng. &b<<"} @caster
  - orbital{r=2;p=16;rotate=true;os=Symphony_Start;ot=Symphony_Tick;oh=Symphony_Hit;oe=Symphony_End;i=1;sourceisorigin=true;d=100;hitnonplayers=true;hitradius=1.3} @origin

Super_Heal:
  Cooldown: <modifier.cooldown>
  Skills:
  - heal{a=80} @self
  - message{m="&cHealed!"} @self
Cooldown_Skill:
  Skills:
  - message{m="&7(&eSkills&7) &cSkill hiện tại đang hồi chiêu!"}

Enigmatic:
  Cooldown: <modifier.cooldown>
  Skills:
  - message{m="&9&oBạn cảm thấy sợi tơ linh hồn quấn quanh bạn..."} @self
  - damage{ia=true;ii=true;is=true;ieff=true;ir=true;pkb=true;amount=100} @self
  - onattack{onattackskill=Enigmatic_Hit;cE=true;damagemultiplier=<modifier.amplifier>;aura=EnigmaticBuff;d=(<modifier.duration>*20);oe=Enigmatic_End} @self
Enigmatic_Hit:
  Skills:
  - sound{s=entity.experience_orb.pickup;p=0.2;v=3} @self
Enigmatic_End:
  Skills:
  - message{m="&c&oSức mạnh linh hồn dần tan biến dần..."} @self

Self_Damage:
  Skills:
  - damage{amount=100} @self