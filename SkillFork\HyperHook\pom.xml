<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>me.stella</groupId>
  <artifactId>HyperHook</artifactId>
  <version>0.1-dev03-2023</version>
  <name>HyperHook</name>
  <description>A lightweight hook between PreventHopper and Discord.</description>
  
   <build>
      <plugins>
          <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-compiler-plugin</artifactId>
              <configuration>
                  <source>1.8</source>
                  <target>1.8</target>
              </configuration>
          </plugin>
      </plugins>
   </build>
   
   <repositories>
      <repository>
         <id>spigot-repo</id>
         <url>https://hub.spigotmc.org/nexus/content/repositories/public/</url>
       </repository>
	  <repository>
	      <id>jitpack.io</id>
	      <url>https://jitpack.io</url>
	  </repository>
      <repository>
          <id>placeholderapi</id>
          <url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
      </repository>
   </repositories>
   
   <dependencies>
       <dependency>
           <groupId>org.spigotmc</groupId>
           <artifactId>spigot-api</artifactId>
           <version>1.12.2-R0.1-SNAPSHOT</version>
           <type>jar</type>
           <scope>provided</scope>
       </dependency>
	   <dependency>
	      <groupId>com.github.MilkBowl</groupId>
	      <artifactId>VaultAPI</artifactId>
	      <version>1.7</version>
	      <scope>provided</scope>
	   </dependency>
       <dependency>
          <groupId>me.clip</groupId>
          <artifactId>placeholderapi</artifactId>
          <version>2.10.4</version>
          <scope>provided</scope>
       </dependency>
   </dependencies>
  
</project>