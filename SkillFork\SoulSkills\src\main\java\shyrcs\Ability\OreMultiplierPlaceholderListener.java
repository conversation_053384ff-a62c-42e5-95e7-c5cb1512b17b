package shyrcs.Ability;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.inventory.ItemStack;

public class OreMultiplierPlaceholderListener implements Listener {
    
    /**
     * Xử lý khi player cầm item mới
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        try {
            ItemStack item = event.getPlayer().getInventory().getItem(event.getNewSlot());
            if (item != null && OreMultiplierPlaceholderUtil.hasPlaceholders(item) && 
                OreMultiplierPlaceholderUtil.hasOreMultiplierStats(item)) {
                
                // Xử lý placeholder với delay nhỏ để tránh conflict
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("SoulSkills"), 
                    () -> {
                        ItemStack processedItem = OreMultiplierPlaceholderUtil.processPlaceholders(item);
                        event.getPlayer().getInventory().setItem(event.getNewSlot(), processedItem);
                    }, 
                    1L
                );
            }
        } catch (Exception e) {
            // Ignore errors để tránh spam console
        }
    }
    
    /**
     * Xử lý khi player click vào item trong inventory
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onInventoryClick(InventoryClickEvent event) {
        try {
            ItemStack item = event.getCurrentItem();
            if (item != null && OreMultiplierPlaceholderUtil.hasPlaceholders(item) && 
                OreMultiplierPlaceholderUtil.hasOreMultiplierStats(item)) {
                
                // Xử lý placeholder với delay nhỏ
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("SoulSkills"), 
                    () -> {
                        ItemStack processedItem = OreMultiplierPlaceholderUtil.processPlaceholders(item);
                        event.setCurrentItem(processedItem);
                    }, 
                    1L
                );
            }
        } catch (Exception e) {
            // Ignore errors
        }
    }
    
    /**
     * Xử lý khi player mở inventory
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onInventoryOpen(InventoryOpenEvent event) {
        try {
            // Xử lý tất cả items trong inventory với delay
            org.bukkit.Bukkit.getScheduler().runTaskLater(
                org.bukkit.Bukkit.getPluginManager().getPlugin("SoulSkills"), 
                () -> {
                    for (int i = 0; i < event.getInventory().getSize(); i++) {
                        ItemStack item = event.getInventory().getItem(i);
                        if (item != null && OreMultiplierPlaceholderUtil.hasPlaceholders(item) && 
                            OreMultiplierPlaceholderUtil.hasOreMultiplierStats(item)) {
                            
                            ItemStack processedItem = OreMultiplierPlaceholderUtil.processPlaceholders(item);
                            event.getInventory().setItem(i, processedItem);
                        }
                    }
                }, 
                2L
            );
        } catch (Exception e) {
            // Ignore errors
        }
    }
}
