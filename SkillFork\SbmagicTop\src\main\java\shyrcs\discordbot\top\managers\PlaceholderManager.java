package shyrcs.discordbot.top.managers;

import me.clip.placeholderapi.PlaceholderAPI;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import shyrcs.discordbot.top.SbmagicTopPlugin;
import shyrcs.discordbot.top.utils.MessageUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PlaceholderManager {
    
    private final SbmagicTopPlugin plugin;
    private final Logger logger;
    private final Map<String, String> placeholderCache;
    private final Pattern placeholderPattern;
    private long lastCacheUpdate;
    private final long cacheExpireTime;
    
    public PlaceholderManager(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.placeholderCache = new ConcurrentHashMap<>();
        this.placeholderPattern = Pattern.compile("%([^%]+)%");
        this.lastCacheUpdate = 0;
        this.cacheExpireTime = plugin.getConfig().getLong("settings.cache-duration", 30) * 1000; // Convert to milliseconds
    }
    
    /**
     * Parse tất cả placeholder trong text
     */
    public String parsePlaceholders(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        // Kiểm tra cache có hết hạn không
        if (System.currentTimeMillis() - lastCacheUpdate > cacheExpireTime) {
            clearCache();
        }
        
        Matcher matcher = placeholderPattern.matcher(text);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String placeholder = matcher.group(1);
            String value = getPlaceholderValue(placeholder);
            matcher.appendReplacement(result, Matcher.quoteReplacement(value));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * Lấy giá trị của placeholder
     */
    private String getPlaceholderValue(String placeholder) {
        // Kiểm tra cache trước
        if (placeholderCache.containsKey(placeholder)) {
            return placeholderCache.get(placeholder);
        }
        
        String value = "";
        
        try {
            // Sử dụng PlaceholderAPI để parse
            if (Bukkit.getPluginManager().isPluginEnabled("PlaceholderAPI")) {
                // Tạo một offline player null để parse placeholder không cần player
                OfflinePlayer nullPlayer = null;
                value = PlaceholderAPI.setPlaceholders(nullPlayer, "%" + placeholder + "%");
                
                // Nếu placeholder không được parse (vẫn có %), thử parse trực tiếp
                if (value.equals("%" + placeholder + "%")) {
                    value = parsePlaceholderDirect(placeholder);
                }
            } else {
                value = parsePlaceholderDirect(placeholder);
            }
            
            // Nếu vẫn không có giá trị, trả về placeholder gốc
            if (value.isEmpty() || value.equals("%" + placeholder + "%")) {
                value = "N/A";
            }
            
        } catch (Exception e) {
            logger.warning("Error parsing placeholder '" + placeholder + "': " + e.getMessage());
            value = "Error";
        }
        
        // Lưu vào cache
        placeholderCache.put(placeholder, value);
        return value;
    }
    
    /**
     * Parse placeholder trực tiếp (fallback method)
     */
    private String parsePlaceholderDirect(String placeholder) {
        // Xử lý các placeholder đặc biệt
        if (placeholder.startsWith("ajlb_")) {
            return parseAjLeaderboardsPlaceholder(placeholder);
        }
        
        if (placeholder.startsWith("img_")) {
            return parseImagePlaceholder(placeholder);
        }
        
        return "N/A";
    }
    
    /**
     * Parse placeholder của AjLeaderboards
     */
    private String parseAjLeaderboardsPlaceholder(String placeholder) {
        // Ví dụ: ajlb_lb_vault_eco_balance_1_alltime_name
        // Ví dụ: ajlb_lb_vault_eco_balance_1_alltime_value
        
        try {
            if (Bukkit.getPluginManager().isPluginEnabled("AjLeaderboards")) {
                // Thử sử dụng PlaceholderAPI với AjLeaderboards
                String result = PlaceholderAPI.setPlaceholders(null, "%" + placeholder + "%");
                if (!result.equals("%" + placeholder + "%")) {
                    return result;
                }
            }
            
            // Fallback: parse thông tin từ placeholder name
            if (placeholder.contains("_name")) {
                return "Player" + extractRankFromPlaceholder(placeholder);
            } else if (placeholder.contains("_value")) {
                return formatValue(1000000 - (extractRankFromPlaceholder(placeholder) * 100000));
            }
            
        } catch (Exception e) {
            logger.warning("Error parsing AjLeaderboards placeholder: " + e.getMessage());
        }
        
        return "N/A";
    }
    
    /**
     * Parse placeholder hình ảnh/emoji
     */
    private String parseImagePlaceholder(String placeholder) {
        switch (placeholder.toLowerCase()) {
            case "img_coins":
                return "💰";
            case "img_diamond":
                return "💎";
            case "img_star":
                return "⭐";
            case "img_trophy":
                return "🏆";
            default:
                return "";
        }
    }
    
    /**
     * Trích xuất rank từ placeholder
     */
    private int extractRankFromPlaceholder(String placeholder) {
        Pattern rankPattern = Pattern.compile("_(\\d+)_");
        Matcher matcher = rankPattern.matcher(placeholder);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return 1;
    }
    
    /**
     * Format giá trị số
     */
    private String formatValue(long value) {
        return MessageUtils.formatNumber(value);
    }
    
    /**
     * Xóa cache
     */
    public void clearCache() {
        placeholderCache.clear();
        lastCacheUpdate = System.currentTimeMillis();

    }
    
    /**
     * Chuyển đổi Minecraft color codes thành Discord markdown
     */
    public String convertMinecraftToDiscord(String text) {
        if (text == null) return "";
        return MessageUtils.convertToDiscordMarkdown(text);
    }
}
