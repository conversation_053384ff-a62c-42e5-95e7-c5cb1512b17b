CancelEvent:
  Conditions:
  - incombat true
  Skills:
  - CancelEvent @self

abyssal_knight_overhead:
  Cooldown: 6
  Conditions:
  - incombat true
  - targetnotwithin{d=3} true
  - targetwithin{d=6} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=overhead;duration=50} @self
  - state{model=abyssal_knight;state=overhead} @self
  - delay 10
  - lockmodel @self
  - delay 5
  - bodyclamp{mh=0;mb=0} @self
  - lunge{v=1} @ModelPart{mid=abyssal_knight;pid=front}
  - delay 4
  - sound{s=custom.metal_impact1;v=3;p=1} @self
  - aura{auraName=overheadD;duration=5;oT=overhead_damage} @self
  - delay 2
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=abyssal_impact1} ?hasaura{n=phase2}
  - setAI{ai=false} self
  - delay 9
  - bodyclamp{mh=0;mb=0} @self
  - skill{s=[ - totem{md=2;oS=[ - skill{s=abyssal_knight_OIBS} ];hnp=true;hp=true} @ENO{r=3;conditions=[ - target{a=>0} true ];ignore=samefaction} ]} @ModelPart{mid=abyssal_knight;pid=right_back}
  - delay 20
  - setAI{ai=true} @self ?!hasaura{n=OIBS}
  - lockmodel{l=false} @self ?!hasaura{n=OIBS}
  - setspeed{speed=1;type=WALKING} @self ?!hasaura{n=OIBS}
  - skill{s=[ - totem{md=2;oS=[ - skill{s=abyssal_knight_splashing} ];hnp=true;hp=true} @ENO{r=3;conditions=[ - target{a=>0} true ];ignore=samefaction} ]} @ModelPart{mid=abyssal_knight;pid=left_back} ?!hasaura{n=OIBS}

overhead_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_OIBS:
  Conditions:
  - incombat true
  - chance{c=0.75} true
  - hasaura{n=overhead} true
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=OIBS;duration=60} @self
  - lockmodel @self
  - state{model=abyssal_knight;state=overhead_into_back_sweep} @self
  - setAI{ai=true} @self
  - delay 20
  - aura{auraName=OIBSD;duration=8;oT=OIBS_damage} @self
  - sound{s=custom.slash1;v=3;p=1} @self
  - delay 1
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - delay 39
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self
  
OIBS_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_overhead2:
  Cooldown: 6
  Conditions:
  - incombat true
  - targetnotwithin{d=3} true
  - targetwithin{d=6} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=overhead2;duration=50} @self
  - state{model=abyssal_knight;state=overhead} @self
  - delay 10
  - lockmodel @self
  - delay 5
  - bodyclamp{mh=0;mb=0} @self
  - lunge{v=1} @ModelPart{mid=abyssal_knight;pid=front}
  - delay 4
  - aura{auraName=overhead2D;duration=5;oT=overhead2_damage} @self
  - sound{s=custom.metal_impact1;v=3;p=1} @self
  - delay 2
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=abyssal_impact1} ?hasaura{n=phase2}
  - setAI{ai=false} self
  - delay 9
  - bodyclamp{mh=0;mb=0} @self
  - skill{s=[ - totem{md=2;oS=[ - skill{s=abyssal_knight_OIRS} ];hnp=true;hp=true} @ENO{r=3;conditions=[ - target{a=>0} true ];ignore=samefaction} ]} @ModelPart{mid=abyssal_knight;pid=abyssal_impact1}
  - delay 20
  - setAI{ai=true} @self ?!hasaura{n=OIRS}
  - lockmodel{l=false} @self ?!hasaura{n=OIRS}
  - setspeed{speed=1;type=WALKING} @self ?!hasaura{n=OIRS}
  - skill{s=[ - totem{md=2;oS=[ - skill{s=abyssal_knight_splashing} ];hnp=true;hp=true} @ENO{r=3;conditions=[ - target{a=>0} true ]} ]} @ModelPart{mid=abyssal_knight;pid=left_back} ?!hasaura{n=OIRS}

overhead2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_OIRS:
  Conditions:
  - incombat true
  - chance{c=0.5} true
  - targetwithin{d=4} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} true
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=OIRS;duration=50} @self
  - lockmodel @self
  - state{model=abyssal_knight;state=overhead_into_right_sweep} @self
  - setAI{ai=true} @self
  - delay 17
  - aura{auraName=OIRSD;duration=8;oT=OIRS_damage} @self
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - sound{s=custom.slash2;v=3;p=1} @self
  - delay 43
  - skill{s=[ - totem{md=2;oS=[ - skill{s=abyssal_knight_splashing} ];hnp=true;hp=true} @ENO{r=3;conditions=[ - target{a=>0} true ]} ]} @ModelPart{mid=abyssal_knight;pid=left_back}
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self
  
OIRS_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_stab:
  Cooldown: 6
  Conditions:
  - incombat true
  - targetwithin{d=4} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=stabbing;duration=65} @self
  - lockmodel @self
  - state{model=abyssal_knight;state=stab} @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 31
  - lunge{v=1} @ModelPart{mid=abyssal_knight;pid=front}
  - sound{s=custom.slash1;v=3;p=1} @self
  - delay 1
  - aura{auraName=stabD;duration=4;oT=stab_damage} @self
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - delay 33
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

stab_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_right_sweep:
  Cooldown: 6
  Conditions:
  - incombat true
  - targetwithin{d=4} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=Rsweeping;duration=45} @self
  - lockmodel @self
  - state{model=abyssal_knight;state=right_sweep} @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 11
  - aura{auraName=RsweepD;duration=6;oT=right_sweep_damage} @self
  - sound{s=custom.slash2;v=3;p=1} @self
  - delay 1
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - delay 12
  - skill{s=abyssal_knight_RSILS}
  - delay 21
  - lockmodel{l=false} @self ?!hasaura{n=RSILS}
  - setspeed{speed=1;type=WALKING} @self ?!hasaura{n=RSILS}
  - skill{s=[ - totem{md=2;oS=[ - skill{s=abyssal_knight_splashing} ];hnp=true;hp=true} @ENO{r=3;conditions=[ - target{a=>0} true ]} ]} @ModelPart{mid=abyssal_knight;pid=left_back} ?!hasaura{n=RSILS}

right_sweep_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_RSILS:
  Conditions:
  - incombat true
  - chance{c=0.5} true
  - targetwithin{d=4} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} true
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=RSILS;duration=50} @self
  - lockmodel @self
  - state{model=abyssal_knight;state=right_sweep_into_left_sweep} @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 16
  - aura{auraName=RSILSD;duration=6;oT=RSILS_damage} @self
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - delay 1
  - sound{s=custom.slash1;v=3;p=1} @self
  - delay 33
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

RSILS_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_left_sweep:
  Cooldown: 6
  Conditions:
  - incombat true
  - targetwithin{d=5} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=Lsweeping;duration=50} @self
  - lockmodel @self
  - state{model=abyssal_knight;state=left_sweep} @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 15
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - delay 1
  - aura{auraName=LsweepD;duration=6;oT=left_sweep_damage} @self
  - delay 1
  - sound{s=custom.slash1;v=3;p=1} @self
  - delay 33
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

left_sweep_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_leap_smash:
  Cooldown: 20
  Conditions:
  - incombat true
  - targetnotwithin{d=5} true
  - targetwithin{d=10} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=leaping;duration=115} @self
  - state{model=abyssal_knight;state=leap_smash} @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 27
  - sound{s=custom.rock_impact;v=3;p=1} @self
  - particlering{particle=cloud;r=1;p=10;amount=25;hS=1;offset=0.3} @self
  - delay 3
  - lockmodel @self
  - potion{type=LEVITATION;duration=3;l=10;p=false} @self
  - potion{type=LEVITATION;duration=13;l=3;p=false} @self
  - projectile{sE=false;syo=0;tyo=0;vO=7;g=1.55;d=40;v=30;onTick=[ - teleport @ProjectileForward{f=-1.2} ];hnp=true} @forward{f=7;lockpitch=true}
  - delay 42
  - sound{s=custom.metal_impact3;v=3;p=1} @self
  - sound{s=entity.iron_golem.damage;v=10;p=1.2} @self
  - sound{s=entity.player.attack.crit;v=10;p=2} @self
  - delay 2
  - sound{s=custom.rock_impact;v=3;p=1} @self
  - delay 2
  - skill{s=leap_damage} @self
  - delay 2
  - summon{type=abyssal_puddle;amount=1} @RLNC{a=10;r=3} ?hasaura{n=phase2}
  - summon{type=abyssal_puddle;amount=1} @RLNC{a=10;minr=3;r=5} ?hasaura{n=phase2}
  - delay 37
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self
  
leap_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=25;is=true} ];hnp=true;hp=true;hR=2.5;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=32;is=true} ];hnp=true;hp=true;hR=2.5;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_front_flip:
  Cooldown: 15
  Conditions:
  - incombat true
  - targetwithin{d=5} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  TargetConditions:
  - inlineofsight true
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=flipping;duration=50} @self
  - lockmodel @self
  - state{model=abyssal_knight;state=front_flip} @self 
  - bodyclamp{mh=0;mb=0} @self
  - delay 8
  - potion{type=LEVITATION;duration=12;l=2;p=false} @self
  - projectile{sE=false;syo=0;tyo=0;vO=1;g=1.4;d=14;v=10;onTick=[ - teleport @ProjectileForward{f=-1.2} ];hnp=true} @forward{f=6}
  - delay 14
  - aura{auraName=flipD1;duration=6;oT=flip_damage1} @self
  - sound{s=custom.metal_impact2;v=3;p=1} @self
  - delay 4
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=abyssal_impact1} ?hasaura{n=phase2}
  - delay 4
  - skill{s=abyssal_knight_front_flip2}
  - delay 25
  - lockmodel{l=false} @self ?!hasaura{n=flipping2}
  - setspeed{speed=1;type=WALKING} @self ?!hasaura{n=flipping2}
  
flip_damage1:
  Skills:
  - totem{md=2;oH=[ - damage{a=20;is=true} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=30;is=true} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_front_flip2:
  Conditions:
  - incombat true
  - chance{c=0.4} true
  - targetwithin{d=5} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} true
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=flipping2;duration=55} @self
  - state{model=abyssal_knight;state=front_flip_into_front_flip} @self
  - lockmodel{l=false} @self
  - delay 3
  - lockmodel @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 9
  - potion{type=LEVITATION;duration=12;l=2;p=false} @self
  - projectile{sE=false;syo=0;tyo=0;vO=1;g=1.4;d=15;v=10;onTick=[ - teleport @ProjectileForward{f=-1.2} ];hnp=true} @forward{f=6}
  - delay 16
  - sound{s=custom.metal_impact3;v=3;p=1} @self
  - delay 1
  - aura{auraName=flipD2;duration=5;oT=flip_damage2} @self
  - delay 2
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=abyssal_impact1} ?hasaura{n=phase2}
  - delay 3
  - skill{s=abyssal_knight_front_flip3}
  - delay 21
  - lockmodel{l=false} @self ?!hasaura{n=flipping3}
  - setspeed{speed=1;type=WALKING} @self ?!hasaura{n=flipping3}
  
flip_damage2:
  Skills:
  - totem{md=2;oH=[ - damage{a=20;is=true} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=30;is=true} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_front_flip3:
  Conditions:
  - incombat true
  - chance{c=0.3} true
  - targetwithin{d=5} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} true
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=flipping3;duration=55} @self
  - state{model=abyssal_knight;state=front_flip_into_front_flip} @self
  - lockmodel{l=false} @self
  - delay 3
  - lockmodel @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 9
  - potion{type=LEVITATION;duration=12;l=2;p=false} @self
  - projectile{sE=false;syo=0;tyo=0;vO=1;g=1.4;d=15;v=10;onTick=[ - teleport @ProjectileForward{f=-1.2} ];hnp=true} @forward{f=6}
  - delay 16
  - sound{s=custom.metal_impact3;v=3;p=1} @self
  - delay 1
  - aura{auraName=flipD3;duration=7;oT=flip_damage3} @self
  - delay 2
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=abyssal_impact1} ?hasaura{n=phase2}
  - delay 24
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self
  
flip_damage3:
  Skills:
  - totem{md=2;oH=[ - damage{a=20;is=true} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=30;is=true} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_scream:
  Cooldown: 15
  Conditions:
  - incombat true
  - targetnotwithin{d=5} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=screaming;duration=50} @self
  - state{model=abyssal_knight;state=scream} @self
  - lockmodel @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 10
  - sound{s=custom.monster_scream1;v=3;p=1} @self
  - sound{s=custom.monster_scream2;v=3;p=1.5} @self
  - delay 40
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self
  - randomskill{skills=abyssal_knight_stab,abyssal_knight_overhead,abyssal_knight_overhead2,abyssal_knight_right_sweep,abyssal_knight_left_sweep} @self ?targetwithin{d=4}
  - randomskill{skills=abyssal_knight_stab_dash,abyssal_knight_leap_smash,abyssal_knight_front_flip} @self ?targetnotwithin{d=4}

abyssal_knight_stab_dash:
  Cooldown: 12
  Conditions:
  - incombat true
  - targetnotwithin{d=5} true
  - targetwithin{d=15} true
  - hasaura{n=phase2} false
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=Sdashing;duration=60} @self
  - state{model=abyssal_knight;state=stab_dash} @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 15
  - lockmodel @self
  - projectile{sE=false;syo=0;tyo=0;d=18;v=16;onTick=[ - teleport @ProjectileForward{f=-1.2} ]} @forward{f=10;lockpitch=true}
  - delay 2
  - aura{auraName=SdashD;duration=19;oT=stab_dash_damage} @self
  - delay 14
  - sound{s=custom.slash2;v=3;p=1} @self
  - delay 29
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

stab_dash_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox}

abyssal_knight_stab_dash_ph2:
  Cooldown: 6
  Conditions:
  - incombat true
  - targetnotwithin{d=5} true
  - targetwithin{d=15} true
  - hasaura{n=phase2} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=Sdashing;duration=60} @self
  - state{model=abyssal_knight;state=stab_dash} @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 15
  - lockmodel @self
  - projectile{sE=false;syo=0;tyo=0;d=18;v=16;onTick=[ - teleport @ProjectileForward{f=-1.2} ]} @forward{f=10;lockpitch=true}
  - aura{auraName=splash_summon;oT=summon_splash;duration=17} @ModelPart{mid=abyssal_knight;pid=hurtbox}
  - delay 2
  - aura{auraName=SdashD_ph2;duration=19;oT=stab_dash_damage_ph2} @self
  - delay 14
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - sound{s=custom.slash2;v=3;p=1} @self
  - delay 29
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

summon_splash:
  Skills:
  - summon{type=abyssal_puddle;amount=1} @self

stab_dash_damage_ph2:
  Skills:
  - totem{md=2;oH=[ - damage{a=30} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox}

abyssal_knight_backstep:
  Cooldown: 10
  Conditions:
  - incombat true
  - chance{c=0.5} true
  - targetwithin{d=3} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false 
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=backstepping;duration=25} @self
  - state{model=abyssal_knight;state=backstep} @self
  - lockmodel @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 8
  - sound{s=entity.player.small_fall;v=3;p=0.6} @self
  - lunge{v=1;repeat=3;repeatInterval=1} @forward{f=-1}
  - delay 17
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

abyssal_knight_charge:
  Cooldown: 60
  Conditions:
  - incombat true
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=Sdashing} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - stun{duration=130} @self
  - aura{auraName=Acharging;duration=130} @self
  - state{model=abyssal_knight;state=abyssal_charge} @self
  - lockmodel @self
  - bodyclamp{mh=0;mb=0} @self
  - summon{type=abyssal_charge;amount=1} @self
  - setNoDamageTicks{ticks=130} @self
  - delay 50
  - sound{s=entity.warden.sonic_charge;v=3;p=0.8} @self
  - delay 40
  - partvisibility{model=abyssal_knight;part=dark_auras;v=true;child=true} @self ?!hasaura{n=phase2}
  - partvisibility{model=abyssal_knight;part=abyssal_impact;v=true;child=true} @self ?!hasaura{n=phase2}
  - partvisibility{model=abyssal_knight;part=dark_energy;v=true;child=true} @self ?!hasaura{n=phase2}
  - partvisibility{model=abyssal_knight;part=splashes_ph2;v=true;child=true} @self ?!hasaura{n=phase2}
  - sound{s=custom.monster_scream1;v=3;p=1} @self
  - sound{s=custom.monster_scream1;v=3;p=1.5} @self
  - sound{s=entity.warden.sonic_boom;v=3;p=1} @self
  - delay 8
  - damage{a=100;is=true} @LivingInRadius{r=7}
  - delay 32
  - aura{auraName=phase2;duration=999999} @self
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

abyssal_knight_splashing:
  Cooldown: 6
  Conditions:
  - incombat true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=Asplashing;duration=40} @self
  - state{model=abyssal_knight;state=abyssal_splash} @self
  - lockmodel @self
  - delay 8
  - aura{auraName=splash1D;oT=splash1_damage;duration=5} @self
  - delay 1
  - aura{auraName=splash2D;oT=splash2_damage;duration=6} @self
  - aura{auraName=splash3D;oT=splash3_damage;duration=6} @self
  - aura{auraName=splash4D;oT=splash4_damage;duration=6} @self
  - delay 1
  - aura{auraName=splash5D;oT=splash5_damage;duration=6} @self
  - aura{auraName=splash6D;oT=splash6_damage;duration=6} @self
  - delay 1
  - aura{auraName=splash7D;oT=splash7_damage;duration=6} @self
  - aura{auraName=splash8D;oT=splash8_damage;duration=6} @self
  - delay 2
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash1}
  - sound{s=entity.witch.drink;v=3;p=0.6} @ModelPart{mid=abyssal_knight;pid=splash1}
  - delay 1
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash2}
  - sound{s=entity.witch.drink;v=3;p=0.6} @ModelPart{mid=abyssal_knight;pid=splash2}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash3}
  - sound{s=entity.witch.drink;v=3;p=0.6} @ModelPart{mid=abyssal_knight;pid=splash3}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash4}
  - sound{s=entity.witch.drink;v=3;p=0.6} @ModelPart{mid=abyssal_knight;pid=splash4}
  - delay 1
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash5}
  - sound{s=entity.witch.drink;v=3;p=0.6} @ModelPart{mid=abyssal_knight;pid=splash5}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash6}
  - sound{s=entity.witch.drink;v=3;p=0.6} @ModelPart{mid=abyssal_knight;pid=splash6}
  - delay 1
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash7}
  - sound{s=entity.witch.drink;v=3;p=0.6} @ModelPart{mid=abyssal_knight;pid=splash7}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash8}
  - sound{s=entity.witch.drink;v=3;p=0.6} @ModelPart{mid=abyssal_knight;pid=splash8}
  - delay 24
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

splash1_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=4;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash1} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash1} ?hasaura{n=phase2}

splash2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=4;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash2} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash2} ?hasaura{n=phase2}

splash3_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=4;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash3} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash3} ?hasaura{n=phase2}

splash4_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=4;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash4} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash4} ?hasaura{n=phase2}

splash5_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=4;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash5} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash5} ?hasaura{n=phase2}

splash6_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=4;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash6} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash6} ?hasaura{n=phase2}

splash7_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=4;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash7} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash7} ?hasaura{n=phase2}

splash8_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=4;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash8} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash8} ?hasaura{n=phase2}

abyssal_knight_splashing_ph2:
  Conditions:
  - incombat true
  - hasaura{n=phase2} true
  Skills:
  - setspeed{speed=0;type=WALKING} @self
  - lockmodel @self
  - aura{auraName=splash1_ph2D;oT=splash1_ph2_damage;duration=5} @self
  - delay 1
  - aura{auraName=splash2_ph2D;oT=splash2_ph2_damage;duration=6} @self ?!hasaura{n=stabbing}
  - aura{auraName=splash3_ph2D;oT=splash3_ph2_damage;duration=6} @self ?!hasaura{n=stabbing}
  - aura{auraName=splash4_ph2D;oT=splash4_ph2_damage;duration=6} @self
  - delay 1
  - aura{auraName=splash5_ph2D;oT=splash5_ph2_damage;duration=6} @self ?!hasaura{n=stabbing}
  - aura{auraName=splash6_ph2D;oT=splash6_ph2_damage;duration=6} @self ?!hasaura{n=stabbing}
  - delay 1
  - aura{auraName=splash7_ph2D;oT=splash7_ph2_damage;duration=6} @self
  - aura{auraName=splash8_ph2D;oT=splash8_ph2_damage;duration=6} @self ?!hasaura{n=stabbing}
  - delay 2
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash1_ph2} 
  - delay 1
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash2_ph2} ?!hasaura{n=stabbing}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash3_ph2} ?!hasaura{n=stabbing}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash4_ph2}
  - delay 1
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash5_ph2} ?!hasaura{n=stabbing}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash6_ph2} ?!hasaura{n=stabbing}
  - delay 1
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash7_ph2}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash8_ph2} ?!hasaura{n=stabbing}
  - delay 24
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

splash1_ph2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash1_ph2}

splash2_ph2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash2_ph2}

splash3_ph2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash3_ph2}

splash4_ph2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash4_ph2}

splash5_ph2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash5}

splash6_ph2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash6}

splash7_ph2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash7_ph2}

splash8_ph2_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=8;ia=true};
                    - potion{type=DARKNESS;duration=110;l=0} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=splash8_ph2}

abyssal_knight_spin_sweep:
  Cooldown: 10
  Conditions:
  - incombat true
  - targetwithin{d=4} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false 
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=Ssweeping;duration=55} @self
  - state{model=abyssal_knight;state=spin_sweep} @self
  - lockmodel @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 13
  - aura{auraName=SsweepD;oT=spin_sweep_damage;duration=22} @self
  - delay 2
  - sound{s=custom.slash1;v=3;p=1} @self
  - delay 11
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - delay 1
  - sound{s=custom.slash2;v=3;p=1} @self
  - delay 28
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

spin_sweep_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_sweep_jump:
  Cooldown: 15
  Conditions:
  - incombat true
  - targetwithin{d=4} true
  - hasaura{n=overhead} false
  - hasaura{n=overhead2} false
  - hasaura{n=OIBS} false
  - hasaura{n=OIRS} false
  - hasaura{n=stabbing} false
  - hasaura{n=Rsweeping} false
  - hasaura{n=RSILS} false
  - hasaura{n=Lsweeping} false
  - hasaura{n=leaping} false
  - hasaura{n=flipping} false
  - hasaura{n=flipping2} false
  - hasaura{n=flipping3} false
  - hasaura{n=screaming} false
  - hasaura{n=Sdashing} false
  - hasaura{n=backstepping} false
  - hasaura{n=Acharging} false
  - hasaura{n=Asplashing} false
  - hasaura{n=Ssweeping} false 
  - hasaura{n=Sjump} false
  Skills:
  - delay 1
  - CancelEvent @self
  - setspeed{speed=0;type=WALKING} @self
  - aura{auraName=Sjump;duration=80} @self
  - state{model=abyssal_knight;state=sweep_jump} @self
  - lockmodel @self
  - bodyclamp{mh=0;mb=0} @self
  - delay 12
  - aura{auraName=SjumpD;oT=sweep_jump_damage;duration=6} @self
  - skill{s=abyssal_knight_splashing_ph2} @self ?hasaura{n=phase2}
  - sound{s=custom.slash2;v=3;p=1} @self
  - delay 8
  - projectile{sE=false;syo=0;tyo=0;vO=10;g=1.4;d=16;v=10;onTick=[ - teleport @ProjectileForward{f=-1.2} ];hnp=true} @forward{f=3;lockpitch=true}
  - delay 16
  - aura{auraName=SjumpD2;oT=sweep_jump_damage2;duration=8} @self
  - delay 2
  - sound{s=custom.metal_impact3;v=3;p=1} @self
  - delay 4
  - summon{type=abyssal_puddle;amount=1} @RLNC{a=4;r=2} ?hasaura{n=phase2}
  - summon{type=abyssal_puddle;amount=1} @RLO{a=6;minr=2;r=3} ?hasaura{n=phase2}
  - delay 38
  - lockmodel{l=false} @self
  - setspeed{speed=1;type=WALKING} @self

sweep_jump_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=15} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=24} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

sweep_jump_damage2:
  Skills:
  - totem{md=2;oH=[ - damage{a=20;is=true} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?!hasaura{n=phase2}
  - totem{md=2;oH=[ - damage{a=30;is=true} ];hnp=true;hp=true;hR=2;vR=2;i=1;drawhitbox=false} @ModelPart{mid=abyssal_knight;pid=hurtbox} ?hasaura{n=phase2}

abyssal_knight_reset:
  Conditions:
  - playersinradius{a=0;r=32} true
  Skills:
  - goToSpawn{max=32} @self
  - heal{a=1000} @self 
  - auraremove{aura=phase2} @self 
  - partvisibility{model=abyssal_knight;part=dark_auras;v=false;child=true} @self
  - partvisibility{model=abyssal_knight;part=dark_energy;v=false;child=true} @self 
  - partvisibility{model=abyssal_knight;part=abyssal_impact;v=false;child=true} @self 
  - partvisibility{model=abyssal_knight;part=splashes_ph2;v=false;child=true} @self 


abyssal_knight_death:
  Skills:
  - delay 4
  - sound{s=custom.monster_scream2;v=3;p=1} @self
  - delay 18
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash1}
  - delay 5
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash2}
  - delay 6
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash3}
  - delay 2
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash4}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash5}
  - delay 3
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash6}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash7}
  - summon{type=abyssal_puddle;amount=1} @ModelPart{mid=abyssal_knight;pid=splash8}