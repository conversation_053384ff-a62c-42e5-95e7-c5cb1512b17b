package shyrcs.Ability;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

public class OreMultiplierListener implements Listener {

    private final Random random = new Random();
    
    // Danh sách các lo<PERSON>i ore
    private static final List<Material> ORE_BLOCKS = Arrays.asList(
        Material.COAL_ORE, Material.DEEPSLATE_COAL_ORE,
        Material.IRON_ORE, Material.DEEPSLATE_IRON_ORE,
        Material.COPPER_ORE, Material.DEEPSLATE_COPPER_ORE,
        Material.GOLD_ORE, Material.DEEPSLATE_GOLD_ORE,
        Material.REDSTONE_ORE, Material.DEEPSLATE_REDSTONE_ORE,
        Material.LAPIS_ORE, Material.DEEPSLATE_LAPIS_ORE,
        Material.DIAMOND_ORE, Material.DEEPSLATE_DIAMOND_ORE,
        Material.EMERALD_ORE, Material.DEEPSLATE_EMERALD_ORE,
        Material.NETHER_GOLD_ORE, Material.NETHER_QUARTZ_ORE,
        Material.ANCIENT_DEBRIS
    );
    
    @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
    public void onBlockBreak(BlockBreakEvent event) {
        // ignoreCancelled = true handles cancelled events automatically
        
        Player player = event.getPlayer();
        Material blockType = event.getBlock().getType();
        
        // Kiểm tra GameMode
        GameMode gameMode = player.getGameMode();
        if (gameMode == GameMode.CREATIVE || gameMode == GameMode.SPECTATOR) {
            return;
        }
        
        // Kiểm tra xem block có phải là ore không
        if (!ORE_BLOCKS.contains(blockType)) return;

        // Lấy tool trong tay player
        ItemStack tool = player.getInventory().getItemInMainHand();
        if (tool == null || tool.getType() == Material.AIR) return;

        // Kiểm tra xem tool có phải là MMOItem không và có OreMultiplier stat không
        NBTItem nbtItem = NBTItem.get(tool);
        if (!nbtItem.hasTag("MMOITEMS_ORE_MULTIPLIER")) return;

        double oreMultiplierValue = nbtItem.getDouble("MMOITEMS_ORE_MULTIPLIER");
        if (oreMultiplierValue <= 0) return;

        // Lấy multiplier amount (mặc định là 2 nếu không có)
        double multiplierAmount = 2.0; // Default nhân đôi
        if (nbtItem.hasTag("MMOITEMS_ORE_MULTIPLIER_AMOUNT")) {
            multiplierAmount = nbtItem.getDouble("MMOITEMS_ORE_MULTIPLIER_AMOUNT");
            if (multiplierAmount < 1) multiplierAmount = 2.0; // Fallback
        }
        
        // Tính toán tỷ lệ kích hoạt
        double chance = oreMultiplierValue; // Tỷ lệ % kích hoạt
        if (random.nextDouble() * 100 > chance) return; // Không kích hoạt

        // Lấy drop gốc của ore
        ItemStack originalDrop = getOreDrop(blockType);
        if (originalDrop == null) return;

        // Tạo bản sao để thêm vào storage với multiplier amount
        ItemStack multipliedDrop = originalDrop.clone();
        int baseAmount = originalDrop.getAmount();
        int multipliedAmount = (int) Math.round(baseAmount * (multiplierAmount - 1)); // Số lượng được nhân thêm
        multipliedDrop.setAmount(multipliedAmount);

        boolean addedToStorage = false;

        // Thử hook với ExtraStorage bằng reflection
        addedToStorage = tryAddToExtraStorage(player, multipliedDrop, multipliedAmount, multiplierAmount);
        
        // Nếu không thể thêm vào storage, drop ra ground
        if (!addedToStorage) {
            event.getBlock().getWorld().dropItemNaturally(event.getBlock().getLocation(), multipliedDrop);
            // player.sendMessage("§a[Ore Multiplier] §7Đã nhân §a" + String.format("%.1f", multiplierAmount) + "x §e" + multipliedAmount + "x " +
            //                   getItemDisplayName(multipliedDrop) + "§7!");
        }

        // Gọi custom event
        OreMultiplierEvent oreMultiplierEvent = new OreMultiplierEvent(
            player, event.getBlock(), oreMultiplierValue, originalDrop, multipliedAmount, addedToStorage, multiplierAmount
        );
        Bukkit.getPluginManager().callEvent(oreMultiplierEvent);
    }
    
    /**
     * Lấy drop gốc của ore
     */
    private ItemStack getOreDrop(Material ore) {
        switch (ore) {
            case COAL_ORE:
            case DEEPSLATE_COAL_ORE:
                return new ItemStack(Material.COAL, 1);
            case IRON_ORE:
            case DEEPSLATE_IRON_ORE:
                return new ItemStack(Material.RAW_IRON, 1);
            case COPPER_ORE:
            case DEEPSLATE_COPPER_ORE:
                return new ItemStack(Material.RAW_COPPER, 2 + random.nextInt(4)); // 2-5
            case GOLD_ORE:
            case DEEPSLATE_GOLD_ORE:
                return new ItemStack(Material.RAW_GOLD, 1);
            case REDSTONE_ORE:
            case DEEPSLATE_REDSTONE_ORE:
                return new ItemStack(Material.REDSTONE, 4 + random.nextInt(2)); // 4-5
            case LAPIS_ORE:
            case DEEPSLATE_LAPIS_ORE:
                return new ItemStack(Material.LAPIS_LAZULI, 4 + random.nextInt(5)); // 4-8
            case DIAMOND_ORE:
            case DEEPSLATE_DIAMOND_ORE:
                return new ItemStack(Material.DIAMOND, 1);
            case EMERALD_ORE:
            case DEEPSLATE_EMERALD_ORE:
                return new ItemStack(Material.EMERALD, 1);
            case NETHER_GOLD_ORE:
                return new ItemStack(Material.GOLD_NUGGET, 2 + random.nextInt(4)); // 2-5
            case NETHER_QUARTZ_ORE:
                return new ItemStack(Material.QUARTZ, 1);
            case ANCIENT_DEBRIS:
                return new ItemStack(Material.ANCIENT_DEBRIS, 1);
            default:
                return null;
        }
    }
    
    /**
     * Thử thêm item vào ExtraStorage bằng reflection
     */
    private boolean tryAddToExtraStorage(Player player, ItemStack item, int amount, double multiplierAmount) {
        try {
            // Kiểm tra xem ExtraStorage plugin có tồn tại không
            if (Bukkit.getPluginManager().getPlugin("ExtraStorage") == null) {
                return false;
            }

            // Lấy StorageAPI class
            Class<?> storageAPIClass = Class.forName("me.hsgamer.extrastorage.api.StorageAPI");
            Method getInstanceMethod = storageAPIClass.getMethod("getInstance");
            Object storageAPI = getInstanceMethod.invoke(null);

            if (storageAPI == null) return false;

            // Lấy User
            Method getUserMethod = storageAPIClass.getMethod("getUser", org.bukkit.OfflinePlayer.class);
            Object user = getUserMethod.invoke(storageAPI, player);

            if (user == null) return false;

            // Lấy Storage
            Class<?> userClass = Class.forName("me.hsgamer.extrastorage.api.user.User");
            Method getStorageMethod = userClass.getMethod("getStorage");
            Object storage = getStorageMethod.invoke(user);

            if (storage == null) return false;

            // Kiểm tra storage status và canStore
            Class<?> storageClass = Class.forName("me.hsgamer.extrastorage.api.storage.Storage");
            Method getStatusMethod = storageClass.getMethod("getStatus");
            Method canStoreMethod = storageClass.getMethod("canStore", Object.class);
            Method isMaxSpaceMethod = storageClass.getMethod("isMaxSpace");

            boolean status = (Boolean) getStatusMethod.invoke(storage);
            boolean canStore = (Boolean) canStoreMethod.invoke(storage, item);
            boolean isMaxSpace = (Boolean) isMaxSpaceMethod.invoke(storage);

            if (!status || isMaxSpace) return false;

            if (!canStore) {
                // Thử thêm item mới vào filter trước
                try {
                    Method addNewItemMethod = storageClass.getMethod("addNewItem", Object.class);
                    addNewItemMethod.invoke(storage, item);

                    // Kiểm tra lại canStore sau khi thêm item mới
                    canStore = (Boolean) canStoreMethod.invoke(storage, item);
                    if (!canStore) return false;
                } catch (Exception e) {
                    return false;
                }
            }

            // Thêm vào storage
            Method addMethod = storageClass.getMethod("add", Object.class, int.class);
            addMethod.invoke(storage, item, amount);

            // Thông báo cho player (uncomment nếu muốn)
            // player.sendMessage("§a[Ore Multiplier] §7Đã thêm §a" + String.format("%.1f", multiplierAmount) + "x §e" + amount + "x " +
            //                   getItemDisplayName(item) + " §7vào storage!");

            return true;

        } catch (Exception e) {
            // Nếu ExtraStorage không có hoặc lỗi, return false
            return false;
        }
    }

    /**
     * Lấy tên hiển thị của item
     */
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            try {
                // Try modern approach first
                return item.getItemMeta().displayName().toString();
            } catch (Exception e) {
                // Fallback to legacy method
                return item.getItemMeta().getDisplayName();
            }
        }
        return item.getType().name().toLowerCase().replace("_", " ");
    }
}
