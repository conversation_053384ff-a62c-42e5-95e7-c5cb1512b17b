CancelEvent:
  Conditions:
  - incombat true
  Skills:
  - CancelEvent @self
  
charge_summoned:
  Skills:
  - CancelEvent @self
  - state{model=abyssal_charge;state=spawn} @self
  - delay 98
  - lockmodel @self
  - aura{auraName=exploding;oT=explode_damage;duration=20} @self
  - delay 32
  - remove @self
  
explode_damage:
  Skills:
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot1}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot2}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot3}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot4}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot5}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot6}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot7}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot8}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot9}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot10}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot11}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot12}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot13}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot14}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot15}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot16}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot17}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot18}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot19}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot20}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot21}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot22}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot23}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot24}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot25}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot26}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot27}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot28}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot29}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot30}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot31}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot32}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot33}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot34}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot35}
  - totem{md=2;oH=[ - damage{a=20} ];hnp=true;hp=true;hR=1.5;vR=1.5;i=1;drawhitbox=false} @ModelPart{mid=abyssal_charge;pid=explosion_shot36}