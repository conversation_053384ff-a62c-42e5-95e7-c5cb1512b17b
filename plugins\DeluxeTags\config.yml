# DeluxeTags version: 1.8.2-Release Main Configuration
#
# Create your tags using the following format:
#
# deluxetags:
#   VIP: 
#     order: 1
#     tag: '&7[&eVIP&7]'
#     description: 'This tag is awarded by getting VIP'
#
# Placeholders for your chat plugin that supports PlaceholderAPI (Including DeluxeChat)
#
# %deluxetags_identifier% - display the players active tag identifier
# %deluxetags_tag% - display the players active tag
# %deluxetags_description% - display the players active tag description
# %deluxetags_amount% - display the amount of tags a player has access to
#
# Placeholders for your essentials/chat handling formats config:
#
# {deluxetags_identifier} - display the players active tag identifier
# {deluxetags_tag} - display the players active tag
# {deluxetags_description} - display the players active tag description
# {deluxetags_amount} - display the amount of tags a player has access to

force_tags: false
check_updates: true
legacy_hex: false
papi_chat: true
format_chat:
  enabled: false
  format: '{deluxetags_tag} <%1$s> %2$s'
load_tag_on_join: true
gui:
  name: '&6Available tags&f: &6%deluxetags_amount%'
  tag_select_item:
    material: NAME_TAG
    data: 0
    displayname: '&6Tag&f: &6%deluxetags_identifier%'
    lore:
    - '%deluxetags_tag%'
    - '%deluxetags_description%'
  divider_item:
    material: BLACK_STAINED_GLASS_PANE
    data: 0
    displayname: ''
    lore: []
  has_tag_item:
    material: PLAYER_HEAD
    data: 0
    displayname: '&eCurrent tag&f: &6%deluxetags_identifier%'
    lore:
    - '%deluxetags_tag%'
    - Click to remove your current tag
  no_tag_item:
    material: PLAYER_HEAD
    data: 0
    displayname: '&cYou don''t have a tag set!'
    lore:
    - '&7Click a tag above to select one!'
  exit_item:
    material: IRON_DOOR
    data: 0
    displayname: '&cClick to exit'
    lore:
    - '&7Exit the tags menu'
  next_page:
    material: PAPER
    data: 0
    displayname: '&6Next page: %page%'
    lore:
    - '&7Move to the next page'
  previous_page:
    material: PAPER
    data: 0
    displayname: '&6Previous page: %page%'
    lore:
    - '&7Move to the previous page'
deluxetags:
  # 🤪 Silly & Playful - For players who love goofy vibes
  derp_master:
    order: 1
    tag: '#FFD700[ᴅᴇʀᴘ ᴍᴀsᴛᴇʀ]'
    description: '&8• &7Professional derping since 2023'
    permission: deluxetags.tag.derp_master
  potato_overlord:
    order: 2
    tag: '#D35400[ᴘᴏᴛᴀᴛᴏ ᴏᴠᴇʀʟᴏʀᴅ]'
    description: '&8• &7All hail the starchy ruler'
    permission: deluxetags.tag.potato_overlord
  creeper_hugger:
    order: 3
    tag: '#2ECC71[ᴄʀᴇᴇᴘᴇʀ ʜᴜɢɢᴇʀ]'
    description: '&8• &7SSSSshhh... just accept love'
    permission: deluxetags.tag.creeper_hugger
  noob_supreme:
    order: 4
    tag: '#95A5A6[ɴᴏᴏʙ sᴜᴘʀᴇᴍᴇ]'
    description: '&8• &7Proudly placing dirt houses'
    permission: deluxetags.tag.noob_supreme
  blockhead:
    order: 5
    tag: '#8E44AD[ʙʟᴏᴄᴋʜᴇᴀᴅ]'
    description: '&8• &7Literally square-brained'
    permission: deluxetags.tag.blockhead
  
  # 🎮 Gaming & Vibe - Gamer culture and competitive spirit
  pvp_sweat:
    order: 6
    tag: '#E74C3C[ᴘᴠᴘ sᴡᴇᴀᴛ]'
    description: '&8• &7Tryhard mode: activated'
    permission: deluxetags.tag.pvp_sweat
  speedrun_fail:
    order: 7
    tag: '#3498DB[sᴘᴇᴇᴅʀᴜɴ ғᴀɪʟ]'
    description: '&8• &7World record: 0% completion'
    permission: deluxetags.tag.speedrun_fail
  lag_champion:
    order: 8
    tag: '#F39C12[ʟᴀɢ ᴄʜᴀᴍᴘ]'
    description: '&8• &7Winning at 2 FPS'
    permission: deluxetags.tag.lag_champion
  respawn_king:
    order: 9
    tag: '#1ABC9C[ʀᴇsᴘᴀᴡɴ ᴋɪɴɢ]'
    description: '&8• &7Death is my commute'
    permission: deluxetags.tag.respawn_king
  ctrl_z_life:
    order: 10
    tag: '#7F8C8D[ᴄᴛʀʟ+ᴢ ʟɪғᴇ]'
    description: '&8• &7Undoing bad decisions since 2011'
    permission: deluxetags.tag.ctrl_z_life
  
  # 🌿 Nature & Vibe - Earthy and peaceful nature lovers
  tree_whisperer:
    order: 11
    tag: '#2ECC71[ᴛʀᴇᴇ ᴡʜɪsᴘᴇʀᴇʀ]'
    description: '&8• &7In a relationship with oak logs'
    permission: deluxetags.tag.tree_whisperer
  sunflower_soul:
    order: 12
    tag: '#F1C40F[sᴜɴғʟᴏᴡᴇʀ sᴏᴜʟ]'
    description: '&8• &7Always facing the bright side'
    permission: deluxetags.tag.sunflower_soul
  mossy_vibes:
    order: 13
    tag: '#16A085[ᴍᴏssʏ ᴠɪʙᴇs]'
    description: '&8• &7Grows on you (literally)'
    permission: deluxetags.tag.mossy_vibes
  river_drifter:
    order: 14
    tag: '#5DADE2[ʀɪᴠᴇʀ ᴅʀɪғᴛᴇʀ]'
    description: '&8• &7Going with the flow'
    permission: deluxetags.tag.river_drifter
  cloud_gazer:
    order: 15
    tag: '#ECF0F1[ᴄʟᴏᴜᴅ ɢᴀᴢᴇʀ]'
    description: '&8• &7Head in the blocks'
    permission: deluxetags.tag.cloud_gazer
  
  # 🔥 Hype & Energy - Intense and adrenaline-pumping
  hype_train:
    order: 16
    tag: '#E67E22[ʜʏᴘᴇ ᴛʀᴀɪɴ]'
    description: '&8• &7Choo choo! All aboard!'
    permission: deluxetags.tag.hype_train
  clutch_god:
    order: 17
    tag: '#E74C3C[ᴄʟᴜᴛᴄʜ ɢᴏᴅ]'
    description: '&8• &71 HP = 100% confidence'
    permission: deluxetags.tag.clutch_god
  rage_quit:
    order: 18
    tag: '#C0392B[ʀᴀɢᴇ ǫᴜɪᴛ]'
    description: '&8• &7Alt+F4 is my signature move'
    permission: deluxetags.tag.rage_quit
  gg_ez:
    order: 19
    tag: '#27AE60[ɢɢ ᴇᴢ]'
    description: '&8• &7Toxic positivity'
    permission: deluxetags.tag.gg_ez
  crit_machine:
    order: 20
    tag: '#9B59B6[ᴄʀɪᴛ ᴍᴀᴄʜɪɴᴇ]'
    description: '&8• &7RNGesus loves me'
    permission: deluxetags.tag.crit_machine
  
  # 👑 Royal & Elegant - For players with refined tastes
  diamond_duke:
    order: 21
    tag: '#3498DB[ᴅɪᴀᴍᴏɴᴅ ᴅᴜᴋᴇ]'
    description: '&8• &7Blue blood runs in my veins'
    permission: deluxetags.tag.diamond_duke
  netherite_noble:
    order: 22
    tag: '#2C3E50[ɴᴇᴛʜᴇʀɪᴛᴇ ɴᴏʙʟᴇ]'
    description: '&8• &7Ancient debris is my inheritance'
    permission: deluxetags.tag.netherite_noble
  golden_royalty:
    order: 23
    tag: '#F1C40F[ɢᴏʟᴅᴇɴ ʀᴏʏᴀʟᴛʏ]'
    description: '&8• &7Piglins bow to me'
    permission: deluxetags.tag.golden_royalty
  emerald_empress:
    order: 24
    tag: '#2ECC71[ᴇᴍᴇʀᴀʟᴅ ᴇᴍᴘʀᴇss]'
    description: '&8• &7Villagers fear my trades'
    permission: deluxetags.tag.emerald_empress
  enchanted_king:
    order: 25
    tag: '#9B59B6[ᴇɴᴄʜᴀɴᴛᴇᴅ ᴋɪɴɢ]'
    description: '&8• &7Mending is my birthright'
    permission: deluxetags.tag.enchanted_king
  
  # 💀 Dark & Edgy - Spooky and mysterious vibes
  phantom_whisper:
    order: 26
    tag: '#5D6D7E[ᴘʜᴀɴᴛᴏᴍ ᴡʜɪsᴘᴇʀ]'
    description: '&8• &7You hear voices too?'
    permission: deluxetags.tag.phantom_whisper
  soul_stealer:
    order: 27
    tag: '#6C3483[sᴏᴜʟ sᴛᴇᴀʟᴇʀ]'
    description: '&8• &7Your soul looks tasty'
    permission: deluxetags.tag.soul_stealer
  void_walker:
    order: 28
    tag: '#1B2631[ᴠᴏɪᴅ ᴡᴀʟᴋᴇʀ]'
    description: '&8• &7The abyss stares back'
    permission: deluxetags.tag.void_walker
  nether_spawn:
    order: 29
    tag: '#922B21[ɴᴇᴛʜᴇʀ sᴘᴀᴡɴ]'
    description: '&8• &7Born in fire and screams'
    permission: deluxetags.tag.nether_spawn
  shadow_dancer:
    order: 30
    tag: '#283747[sʜᴀᴅᴏᴡ ᴅᴀɴᴄᴇʀ]'
    description: '&8• &7Seen but not remembered'
    permission: deluxetags.tag.shadow_dancer
  
  # ✨ Magical & Enchanted - Fantasy and wizardry
  spell_slinger:
    order: 31
    tag: '#6C3483[sᴘᴇʟʟ sʟɪɴɢᴇʀ]'
    description: '&8• &7Watch the magic missile!'
    permission: deluxetags.tag.spell_slinger
  potion_brewer:
    order: 32
    tag: '#3498DB[ᴘᴏᴛɪᴏɴ ʙʀᴇᴡᴇʀ]'
    description: '&8• &7One sip might kill you'
    permission: deluxetags.tag.potion_brewer
  rune_carver:
    order: 33
    tag: '#1F618D[ʀᴜɴᴇ ᴄᴀʀᴠᴇʀ]'
    description: '&8• &7Ancient symbols glow'
    permission: deluxetags.tag.rune_carver
  arcane_jester:
    order: 34
    tag: '#9B59B6[ᴀʀᴄᴀɴᴇ ᴊᴇsᴛᴇʀ]'
    description: '&8• &7Magic is my punchline'
    permission: deluxetags.tag.arcane_jester
  enchanter_prime:
    order: 35
    tag: '#8E44AD[ᴇɴᴄʜᴀɴᴛᴇʀ ᴘʀɪᴍᴇ]'
    description: '&8• &7Mending V is real'
    permission: deluxetags.tag.enchanter_prime
  
  # 🏆 Competitive & Pro - For the tryhards
  elite_gamer:
    order: 36
    tag: '#27AE60[ᴇʟɪᴛᴇ ɢᴀᴍᴇʀ]'
    description: '&8• &7Git gud scrub'
    permission: deluxetags.tag.elite_gamer
  combo_king:
    order: 37
    tag: '#E74C3C[ᴄᴏᴍʙᴏ ᴋɪɴɢ]'
    description: '&8• &7100-hit air combos'
    permission: deluxetags.tag.combo_king
  parkour_god:
    order: 38
    tag: '#3498DB[ᴘᴀʀᴋᴏᴜʀ ɢᴏᴅ]'
    description: '&8• &7Gravity is optional'
    permission: deluxetags.tag.parkour_god
  redstone_savant:
    order: 39
    tag: '#C0392B[ʀᴇᴅsᴛᴏɴᴇ sᴀᴠᴀɴᴛ]'
    description: '&8• &7I speak in repeaters'
    permission: deluxetags.tag.redstone_savant
  aimbot_eyes:
    order: 40
    tag: '#1ABC9C[ᴀɪᴍʙᴏᴛ ᴇʏᴇs]'
    description: '&8• &7360 no scope legit'
    permission: deluxetags.tag.aimbot_eyes
  
  # 🛸 Sci-Fi & Futuristic - Space and tech themes
  quantum_being:
    order: 41
    tag: '#5DADE2[ǫᴜᴀɴᴛᴜᴍ ʙᴇɪɴɢ]'
    description: '&8• &7Schrödinger player'
    permission: deluxetags.tag.quantum_being
  netherite_tech:
    order: 42
    tag: '#566573[ɴᴇᴛʜᴇʀɪᴛᴇ ᴛᴇᴄʜ]'
    description: '&8• &7Upgraded to v2.0'
    permission: deluxetags.tag.netherite_tech
  enderman_ai:
    order: 43
    tag: '#8E44AD[ᴇɴᴅᴇʀᴍᴀɴ ᴀɪ]'
    description: '&8• &7Error: Block missing'
    permission: deluxetags.tag.enderman_ai
  space_cowboy:
    order: 44
    tag: '#3498DB[sᴘᴀᴄᴇ ᴄᴏᴡʙᴏʏ]'
    description: '&8• &7See you space cowboy...'
    permission: deluxetags.tag.space_cowboy
  cyber_ghost:
    order: 45
    tag: '#AED6F1[ᴄʏʙᴇʀ ɢʜᴏsᴛ]'
    description: '&8• &7Firewall? What firewall?'
    permission: deluxetags.tag.cyber_ghost
  
  # 🍕 Foodie & Hungry - Delicious tags
  pizza_devourer:
    order: 46
    tag: '#E74C3C[ᴘɪᴢᴢᴀ ᴅᴇᴠᴏᴜʀᴇʀ]'
    description: '&8• &7No slice left behind'
    permission: deluxetags.tag.pizza_devourer
  golden_carrot:
    order: 47
    tag: '#F39C12[ɢᴏʟᴅᴇɴ ᴄᴀʀʀᴏᴛ]'
    description: '&8• &7Nutritionally bankrupt'
    permission: deluxetags.tag.golden_carrot
  suspicious_stew:
    order: 48
    tag: '#D35400[sᴜsᴘɪᴄɪᴏᴜs sᴛᴇᴡ]'
    description: '&8• &7May contain shrooms'
    permission: deluxetags.tag.suspicious_stew
  cake_liar:
    order: 49
    tag: '#FADBD8[ᴄᴀᴋᴇ ʟɪᴀʀ]'
    description: '&8• &7The cake was a lie'
    permission: deluxetags.tag.cake_liar
  ramen_slurper:
    order: 50
    tag: '#F5B041[ʀᴀᴍᴇɴ sʟᴜʀᴘᴇʀ]'
    description: '&8• &7*noisy eating sounds*'
    permission: deluxetags.tag.ramen_slurper
  
  # 🎨 Creative & Artistic - For builders and designers
  block_artist:
    order: 51
    tag: '#E74C3C[ʙʟᴏᴄᴋ ᴀʀᴛɪsᴛ]'
    description: '&8• &7World is my canvas'
    permission: deluxetags.tag.block_artist
  color_sorcerer:
    order: 52
    tag: '#9B59B6[ᴄᴏʟᴏʀ sᴏʀᴄᴇʀᴇʀ]'
    description: '&8• &7Concrete is my medium'
    permission: deluxetags.tag.color_sorcerer
  terraformer:
    order: 53
    tag: '#2ECC71[ᴛᴇʀʀᴀғᴏʀᴍᴇʀ]'
    description: '&8• &7Landscapes bow to me'
    permission: deluxetags.tag.terraformer
  glowstone_designer:
    order: 54
    tag: '#F1C40F[ɢʟᴏᴡsᴛᴏɴᴇ ᴅᴇsɪɢɴᴇʀ]'
    description: '&8• &7Lighting engineer'
    permission: deluxetags.tag.glowstone_designer
  palette_pro:
    order: 55
    tag: '#3498DB[ᴘᴀʟᴇᴛᴛᴇ ᴘʀᴏ]'
    description: '&8• &7RGB is my language'
    permission: deluxetags.tag.palette_pro
  
  # 😎 Chill & Relaxed - Laidback players
  cozy_camper:
    order: 56
    tag: '#D35400[ᴄᴏᴢʏ ᴄᴀᴍᴘᴇʀ]'
    description: '&8• &7Campfire connoisseur'
    permission: deluxetags.tag.cozy_camper
  tea_sipper:
    order: 57
    tag: '#1ABC9C[ᴛᴇᴀ sɪᴘᴘᴇʀ]'
    description: '&8• &7*gentle slurping*'
    permission: deluxetags.tag.tea_sipper
  hammock_hanger:
    order: 58
    tag: '#27AE60[ʜᴀᴍᴍᴏᴄᴋ ʜᴀɴɢᴇʀ]'
    description: '&8• &7Professional relaxer'
    permission: deluxetags.tag.hammock_hanger
  sunset_gazer:
    order: 59
    tag: '#E67E22[sᴜɴsᴇᴛ ɢᴀᴢᴇʀ]'
    description: '&8• &7Best view in Minecraft'
    permission: deluxetags.tag.sunset_gazer
  afk_legend:
    order: 60
    tag: '#95A5A6[ᴀғᴋ ʟᴇɢᴇɴᴅ]'
    description: '&8• &7Still here... probably'
    permission: deluxetags.tag.afk_legend
  
  # ⚔️ Warrior & Battle - Combat specialists
  sword_saint:
    order: 61
    tag: '#C0392B[sᴡᴏʀᴅ sᴀɪɴᴛ]'
    description: '&8• &7Blade sings death'
    permission: deluxetags.tag.sword_saint
  arrow_rain:
    order: 62
    tag: '#7F8C8D[ᴀʀʀᴏᴡ ʀᴀɪɴ]'
    description: '&8• &7Pointy skyfall'
    permission: deluxetags.tag.arrow_rain
  shield_maiden:
    order: 63
    tag: '#2980B9[sʜɪᴇʟᴅ ᴍᴀɪᴅᴇɴ]'
    description: '&8• &7Unyielding defense'
    permission: deluxetags.tag.shield_maiden
  totem_cheater:
    order: 64
    tag: '#F1C40F[ᴛᴏᴛᴇᴍ ᴄʜᴇᴀᴛᴇʀ]'
    description: '&8• &7Death? Never heard of it'
    permission: deluxetags.tag.totem_cheater
  raid_leader:
    order: 65
    tag: '#8E44AD[ʀᴀɪᴅ ʟᴇᴀᴅᴇʀ]'
    description: '&8• &7Pillagers fear me'
    permission: deluxetags.tag.raid_leader
  
  # 🐾 Animal Lover - Pet enthusiasts
  wolf_tamer:
    order: 66
    tag: '#566573[ᴡᴏʟғ ᴛᴀᴍᴇʀ]'
    description: '&8• &7Good boy army'
    permission: deluxetags.tag.wolf_tamer
  cat_whisperer:
    order: 67
    tag: '#E74C3C[ᴄᴀᴛ ᴡʜɪsᴘᴇʀᴇʀ]'
    description: '&8• &7Purrfect companion'
    permission: deluxetags.tag.cat_whisperer
  axolotl_hugger:
    order: 68
    tag: '#D2B4DE[ᴀxᴏʟᴏᴛʟ ʜᴜɢɢᴇʀ]'
    description: '&8• &7Squishy friend'
    permission: deluxetags.tag.axolotl_hugger
  bee_saver:
    order: 69
    tag: '#F39C12[ʙᴇᴇ sᴀᴠᴇʀ]'
    description: '&8• &7Not the flowers!'
    permission: deluxetags.tag.bee_saver
  horse_hoarder:
    order: 70
    tag: '#A569BD[ʜᴏʀsᴇ ʜᴏᴀʀᴅᴇʀ]'
    description: '&8• &7Just one more...'
    permission: deluxetags.tag.horse_hoarder
  
  # 💎 Rich & Luxe - Wealth flaunters
  diamond_tycoon:
    order: 71
    tag: '#3498DB[ᴅɪᴀᴍᴏɴᴅ ᴛʏᴄᴏᴏɴ]'
    description: '&8• &7Stacks on stacks'
    permission: deluxetags.tag.diamond_tycoon
  emerald_kingpin:
    order: 72
    tag: '#2ECC71[ᴇᴍᴇʀᴀʟᴅ ᴋɪɴɢᴘɪɴ]'
    description: '&8• &7Villager overlord'
    permission: deluxetags.tag.emerald_kingpin
  netherite_boss:
    order: 73
    tag: '#4A235A[ɴᴇᴛʜᴇʀɪᴛᴇ ʙᴏss]'
    description: '&8• &7Flexing in black'
    permission: deluxetags.tag.netherite_boss
  gold_digger:
    order: 74
    tag: '#F1C40F[ɢᴏʟᴅ ᴅɪɢɢᴇʀ]'
    description: '&8• &7Piglins love me'
    permission: deluxetags.tag.gold_digger
  beacon_beautifier:
    order: 75
    tag: '#5DADE2[ʙᴇᴀᴄᴏɴ ʙᴇᴀᴜᴛɪғɪᴇʀ]'
    description: '&8• &7My sky laser'
    permission: deluxetags.tag.beacon_beautifier
  
  # 🚀 Speed & Motion - Fast and agile
  elytra_ace:
    order: 76
    tag: '#AAB7B8[ᴇʟʏᴛʀᴀ ᴀᴄᴇ]'
    description: '&8• &7Jetpack joyride'
    permission: deluxetags.tag.elytra_ace
  sprint_master:
    order: 77
    tag: '#E74C3C[sᴘʀɪɴᴛ ᴍᴀsᴛᴇʀ]'
    description: '&8• &7CTRL is my friend'
    permission: deluxetags.tag.sprint_master
  boat_drifter:
    order: 78
    tag: '#3498DB[ʙᴏᴀᴛ ᴅʀɪғᴛᴇʀ]'
    description: '&8• &7Ice road racer'
    permission: deluxetags.tag.boat_drifter
  trident_rider:
    order: 79
    tag: '#1ABC9C[ᴛʀɪᴅᴇɴᴛ ʀɪᴅᴇʀ]'
    description: '&8• &7Weeeee!'
    permission: deluxetags.tag.trident_rider
  ice_pathfinder:
    order: 80
    tag: '#AED6F1[ɪᴄᴇ ᴘᴀᴛʜғɪɴᴅᴇʀ]'
    description: '&8• &7Slippery when wet'
    permission: deluxetags.tag.ice_pathfinder
  
  # 🎶 Music & Rhythm - For melodic players
  note_blocker:
    order: 81
    tag: '#9B59B6[ɴᴏᴛᴇ ʙʟᴏᴄᴋᴇʀ]'
    description: '&8• &7Dropping beats'
    permission: deluxetags.tag.note_blocker
  jukebox_junkie:
    order: 82
    tag: '#E67E22[ᴊᴜᴋᴇʙᴏx ᴊᴜɴᴋɪᴇ]'
    description: '&8• &7Need more discs'
    permission: deluxetags.tag.jukebox_junkie
  doot_dooter:
    order: 83
    tag: '#F1C40F[ᴅᴏᴏᴛ ᴅᴏᴏᴛᴇʀ]'
    description: '&8• &7Spooky scary'
    permission: deluxetags.tag.doot_dooter
  redstone_remix:
    order: 84
    tag: '#C0392B[ʀᴇᴅsᴛᴏɴᴇ ʀᴇᴍɪx]'
    description: '&8• &7Bzzzt bop bzzzt'
    permission: deluxetags.tag.redstone_remix
  chorus_singer:
    order: 85
    tag: '#8E44AD[ᴄʜᴏʀᴜs sɪɴɢᴇʀ]'
    description: '&8• &7Echo-y melodies'
    permission: deluxetags.tag.chorus_singer
  
  # 📚 Lore & Story - RPG and story lovers
  ancient_scribe:
    order: 86
    tag: '#795548[ᴀɴᴄɪᴇɴᴛ sᴄʀɪʙᴇ]'
    description: '&8• &7Ink and parchment'
    permission: deluxetags.tag.ancient_scribe
  quest_giver:
    order: 87
    tag: '#D35400[ǫᴜᴇsᴛ ɢɪᴠᴇʀ]'
    description: '&8• &7I have a mission for you'
    permission: deluxetags.tag.quest_giver
  ruin_explorer:
    order: 88
    tag: '#95A5A6[ʀᴜɪɴ ᴇxᴘʟᴏʀᴇʀ]'
    description: '&8• &7Dusty discoveries'
    permission: deluxetags.tag.ruin_explorer
  lore_keeper:
    order: 89
    tag: '#5D6D7E[ʟᴏʀᴇ ᴋᴇᴇᴘᴇʀ]'
    description: '&8• &7Keeper of stories'
    permission: deluxetags.tag.lore_keeper
  villager_historian:
    order: 90
    tag: '#16A085[ᴠɪʟʟᴀɢᴇʀ ʜɪsᴛᴏʀɪᴀɴ]'
    description: '&8• &7Hmm... interesting!'
    permission: deluxetags.tag.villager_historian
  
  # 🎲 Random & Wacky - Totally unpredictable
  block_bender:
    order: 91
    tag: '#27AE60[ʙʟᴏᴄᴋ ʙᴇɴᴅᴇʀ]'
    description: '&8• &7Reality is glitchy'
    permission: deluxetags.tag.block_bender
  gravity_hater:
    order: 92
    tag: '#7F8C8D[ɢʀᴀᴠɪᴛʏ ʜᴀᴛᴇʀ]'
    description: '&8• &7What goes up... stays up'
    permission: deluxetags.tag.gravity_hater
  invisible_friend:
    order: 93
    tag: '#BDC3C7[ɪɴᴠɪsɪʙʟᴇ ғʀɪᴇɴᴅ]'
    description: '&8• &7Trust me, they exist'
    permission: deluxetags.tag.invisible_friend
  backwards_talker:
    order: 94
    tag: '#F39C12[ʙᴀᴄᴋᴡᴀʀᴅs ᴛᴀʟᴋᴇʀ]'
    description: '&8• &7?siht ekil sdrow yM'
    permission: deluxetags.tag.backwards_talker
  glitch_walker:
    order: 95
    tag: '#8E44AD[ɢʟɪᴛᴄʜ ᴡᴀʟᴋᴇʀ]'
    description: '&8• &7404: Player not found'
    permission: deluxetags.tag.glitch_walker
  time_traveler:
    order: 96
    tag: '#1ABC9C[ᴛɪᴍᴇ ᴛʀᴀᴠᴇʟᴇʀ]'
    description: '&8• &7From beta 1.7.3'
    permission: deluxetags.tag.time_traveler
  bedrock_breaker:
    order: 97
    tag: '#34495E[ʙᴇᴅʀᴏᴄᴋ ʙʀᴇᴀᴋᴇʀ]'
    description: '&8• &7Secret techniques'
    permission: deluxetags.tag.bedrock_breaker
  chunk_error:
    order: 98
    tag: '#E74C3C[ᴄʜᴜɴᴋ ᴇʀʀᴏʀ]'
    description: '&8• &7Missingno cousin'
    permission: deluxetags.tag.chunk_error
  herobrine_seer:
    order: 99
    tag: '#95A5A6[ʜᴇʀᴏʙʀɪɴᴇ sᴇᴇʀ]'
    description: '&8• &7I saw him once...'
    permission: deluxetags.tag.herobrine_seer
  update_waiting:
    order: 100
    tag: '#F1C40F[ᴜᴘᴅᴀᴛᴇ ᴡᴀɪᴛᴇʀ]'
    description: '&8• &7Soon™'
    permission: deluxetags.tag.update_waiting
  nolife:
    order: 101
    tag: '#2E2A2A[No-Life]'
    description: '&f'
    permission: deluxetags.tag.nolife
  test:
    order: 102
    tag: '&d[Tester]'
    description: '&f'
    permission: deluxetags.tag.test
  loli:
    order: 103
    tag: '#FBFAB1[ʟ#FCFCCBᴏ#FEFDE5ʟ#FFFFFFɪ]'
    description: '&f'
    permission: deluxetags.tag.loli
  owo:
    order: 104
    tag: '&8[#1DF7E0O#CAFF00w#1DF7E0O&8]'
    description: '&f'
    permission: deluxetags.tag.owo
  ultimate:
    order: 105
    tag: '&6⌜&9&l◜&b╭&e&n&k|&d☯&e&n&k|&b╯&9&l◞&6⌟ &8[#1DF7E0&lU#36F8C0&lL#4EF9A0&lT#67FA80&lI#80FC60&lM#99FD40&lA#B1FE20&lT#CAFF00&lE&8]
      &6⌞&9&l◟&b╰&e&n&k|&d☯&e&n&k|&b╮&9&l◝&6⌝'
    description: '&f'
    permission: deluxetags.tag.ultimate
  tag1:
    order: 106
    tag: '&b◝&6&kl&c⊙&6&kl&b◜ &l#15FFBE𝐀𝐭𝐡#FDA805𝐞𝐢𝐬𝐭 &b◝&6&kl&c⊙&6&kl&b◜'
    description: '&f'
    permission: deluxetags.tag.tag1
  tag2:
    order: 110
    tag: '&b&k|&d⚛&b&k| #FE15FF𝗪𝗜𝗭#05DDFD𝗔𝗥𝗗'
    description: '&f'
    permission: deluxetags.tag.tag2
  tag3:
    order: 111
    tag: '&b&k| #FF7F15#FDDA05𝚅ô Đạ𝚘 𝙱ấ𝚝 𝙻ươ𝚗𝚐 &b&k|'
    description: '&f'
    permission: deluxetags.tag.tag3
  '&6&k|&a&l♪&6&k|':
    order: 113
    tag: '#158BFFFBLUE#D057CBERRY &6&k|&a&l♪&6&k|'
    description: '&f'
    permission: deluxetags.tag.&6&k|&a&l♪&6&k|
  tag5:
    order: 112
    tag: '&6&k|&a&l🝏&6&k| #158BFFBLUEBERRY &6&k|&a&l🝏&6&k|'
    description: '&f'
    permission: deluxetags.tag.tag5
  tag6:
    order: 114
    tag: '&6╯&b&k|&6╰ #16FF15uɐW pǝƃ#A6F6CB#FDD005uɐH ǝɥꓕ &6╯&b&k|&6╰'
    description: '&f'
    permission: deluxetags.tag.tag6
  tag7:
    order: 115
    tag: '&d◝&e╯&c&k|&6&l❖&c&k|&e╰&d◜ #FF5015𝗙𝗢𝗫𝗬#FD9E05𝗡𝗘𝗦𝗧 &d◝&e╯&c&k|&6&l❖&c&k|&e╰&d◜'
    description: '&f'
    permission: deluxetags.tag.tag7
  themoon:
    order: 116
    tag: '&3◝&e&k|&d╯&5☽&d╰&e&k|&3◜ #15FF7C𝗧𝗵𝗲 #9705FD𝗠𝗼𝗼𝗻 &3◝&e&k|&d╯&5☾&d╰&e&k|&3◜'
    description: '&f'
    permission: deluxetags.tag.themoon
  sunny:
    order: 117
    tag: '&a◝&c⌞&b&k|&e☀&b&k|&c⌝&a◜#DDFF15𝗦𝘂𝗻#FDA805𝗻𝘆 &a◝&c⌞&b&k|&e☀&b&k|&c⌝&a◜'
    description: '&f'
    permission: deluxetags.tag.sunny
  koth:
    order: 118
    tag: '&c&l⚔ &8[#DD0808&lK#E42929&lO#EB4949&lT#F16A6A&lH #FFABAB&lS#F88A8A&lL#F16A6A&lA#EB4949&lY#E42929&lE#DD0808&lR&8]
      &c&l⚔'
    description: '&f'
    permission: deluxetags.tag.koth
  staff:
    order: 119
    tag: '&e𐑪&b╯&d&k|&6᯽&d&k|&b╰&e𐑩 #15BBFF𝗦#13C0FF𝗧#11C5FF𝗔#10CAFE𝗙#0ECFFE𝗙
      &e𐑪&b╯&d&k|&6᯽&d&k|&b╰&e𐑩'
    description: '&f'
    permission: deluxetags.tag.staff
  island_1sao:
    order: 120
    tag: '&a🏝 &8[#20FF00&lI#4DF600&lS#79ED00&lL#A6E300&lA#D2DA00&lN#FFD100&lD&8]
      &8[&e★&8] &a🏝'
    description: '&f'
    permission: deluxetags.tag.island_1sao
  island_2sao:
    order: 121
    tag: '&a🏝 &8[#20FF00&lI#4DF600&lS#79ED00&lL#A6E300&lA#D2DA00&lN#FFD100&lD&8]
      &8[&e★★&8] &a🏝'
    description: '&f'
    permission: deluxetags.tag.island_2sao
  island_3sao:
    order: 122
    tag: '&a🏝 &8[#20FF00&lI#4DF600&lS#79ED00&lL#A6E300&lA#D2DA00&lN#FFD100&lD&8]
      &8[&e★★★&8] &a🏝'
    description: '&f'
    permission: deluxetags.tag.island_3sao
  donator_1sao:
    order: 107
    tag: '&b&lミ&8[&x&1&D&F&7&E&0&lD&x&5&8&F&A&E&9&lO&x&9&3&F&C&F&1&lN&x&C&E&F&F&F&A&lA&x&9&3&F&C&F&1&lT&x&5&8&F&A&E&9&lO&x&1&D&F&7&E&0&lR&8]
      &8[&e★&8] &b&l彡'
    description: '&f'
    permission: deluxetags.tag.donator_1sao
  donator_2sao:
    order: 108
    tag: '&b&lミ&8[&x&1&D&F&7&E&0&lD&x&5&8&F&A&E&9&lO&x&9&3&F&C&F&1&lN&x&C&E&F&F&F&A&lA&x&9&3&F&C&F&1&lT&x&5&8&F&A&E&9&lO&x&1&D&F&7&E&0&lR&8]
      &8[&e★★&8] &b&l彡'
    description: '&f'
    permission: deluxetags.tag.donator_2sao
  donator_3sao:
    order: 109
    tag: '&b&lミ&8[&x&1&D&F&7&E&0&lD&x&5&8&F&A&E&9&lO&x&9&3&F&C&F&1&lN&x&C&E&F&F&F&A&lA&x&9&3&F&C&F&1&lT&x&5&8&F&A&E&9&lO&x&1&D&F&7&E&0&lR&8]
      &8[&e★★★&8] &b&l彡'
    description: '&f'
    permission: deluxetags.tag.donator_3sao
  crystal_1sao:
    order: 123
    tag: '#00FFE6&l⟢ &8(#007DFF&lC#0093FB&lR#00A8F7&lY#00BEF3&lS#00D4EE&lT#00E9EA&lA#00FFE6&lL&8)
      [&e★&8] #00FFE6&l⟣'
    description: '&f'
    permission: deluxetags.tag.crystal_1sao
  crystal_2sao:
    order: 124
    tag: '#00FFE6&l⟢ &8(#007DFF&lC#0093FB&lR#00A8F7&lY#00BEF3&lS#00D4EE&lT#00E9EA&lA#00FFE6&lL&8)
      [&e★★&8] #00FFE6&l⟣'
    description: '&f'
    permission: deluxetags.tag.crystal_2sao
  crystal_3sao:
    order: 125
    tag: '#00FFE6&l⟢ &8(#007DFF&lC#0093FB&lR#00A8F7&lY#00BEF3&lS#00D4EE&lT#00E9EA&lA#00FFE6&lL&8)
      [&e★★★&8] #00FFE6&l⟣'
    description: '&f'
    permission: deluxetags.tag.crystal_3sao
  toxic:
    order: 126
    tag: '&c&k|&2☢&c&k| #1AFF62&lT#55FF3B&lo#8FFF13&lx#55F819&li#1AF01E&lc &c&k|&2☢&c&k|'
    description: '&f'
    permission: deluxetags.tag.toxic
  disturb:
    order: 127
    tag: '&3𐑩&b⟆&d&k|&c∅&d&k|&b⟅&3𐑪 #FA2E9B#F70E0E𝗗𝗼 𝗻𝗼𝘁 𝗱𝗶𝘀𝘁𝘂𝗿𝗯 &3𐑩&b⟆&d&k|&c∅&d&k|&b⟅&3𐑪'
    description: '&f'
    permission: deluxetags.tag.disturb
  money_1sao:
    order: 128
    tag: '&e⛃ &8(#FFF002&lM#FFF312&lO#FFF622&lN#FFF832&lE#FFFB42&lY&8) &8[&e★&8] &e⛃'
    description: '&f'
    permission: deluxetags.tag.money_1sao
  money_2sao:
    order: 129
    tag: '&e⛃ &8(#FFF002&lM#FFF312&lO#FFF622&lN#FFF832&lE#FFFB42&lY&8) &8[&e★★&8]
      &e⛃'
    description: '&f'
    permission: deluxetags.tag.money_2sao
  money_3sao:
    order: 130
    tag: '&e⛃ &8(#FFF002&lM#FFF312&lO#FFF622&lN#FFF832&lE#FFFB42&lY&8) &8[&e★★★&8]
      &e⛃'
    description: '&f'
    permission: deluxetags.tag.money_3sao
  member:
    order: 131
    tag: '#0373D1[Member]'
    description: '&f'
    permission: deluxetags.tag.member
  IamMeiBae:
    order: 132
    tag: '&x&f&c&0&b&e&b&l🔮 &x&d&5&0&c&e&e&l𝙇&x&c&2&0&c&f&0&l𝙖&x&a&e&0&d&f&1&l𝙙&x&9&b&0&d&f&3&l𝙮 &x&7&4&0&e&f&6&l⥌&x&6&1&0&f&f&7&l✦&x&4&e&0&f&f&9&l⥍ &x&2&7&1&0&f&c&l𝙎&x&1&3&1&1&f&d&l𝙤&x&0&0&1&1&f&f&l𝙪&x&1&2&1&2&e&e&l𝙡 &x&3&6&1&5&c&c&l🔮'