package shyrcs.extrastoragehook.storage;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import shyrcs.extrastoragehook.SbMagicHook;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Class để lưu trữ kết nối Discord-Minecraft vào file
 * Đảm bảo kết nối không bị mất khi restart server
 */
public class ConnectionStorage {
    
    private final File storageFile;
    private FileConfiguration storage;
    
    public ConnectionStorage(File dataFolder) {
        this.storageFile = new File(dataFolder, "connections.yml");
        loadStorage();
    }
    
    /**
     * Tải storage từ file
     */
    private void loadStorage() {
        try {
            if (!storageFile.exists()) {
                storageFile.getParentFile().mkdirs();
                storageFile.createNewFile();
            }
            this.storage = YamlConfiguration.loadConfiguration(storageFile);
        } catch (IOException e) {
            SbMagicHook.error("Không thể tải connection storage: " + e.getMessage());
        }
    }
    
    /**
     * Lưu storage vào file
     */
    public void saveStorage() {
        try {
            storage.save(storageFile);
        } catch (IOException e) {
            SbMagicHook.error("Không thể lưu connection storage: " + e.getMessage());
        }
    }
    
    /**
     * Lưu kết nối
     */
    public void saveConnection(String discordId, UUID minecraftUuid) {
        storage.set("connections." + discordId, minecraftUuid.toString());
        saveStorage();
    }
    
    /**
     * Xóa kết nối
     */
    public void removeConnection(String discordId) {
        storage.set("connections." + discordId, null);
        saveStorage();
    }
    
    /**
     * Lấy Minecraft UUID từ Discord ID
     */
    public UUID getMinecraftUUID(String discordId) {
        String uuidString = storage.getString("connections." + discordId);
        if (uuidString != null) {
            try {
                return UUID.fromString(uuidString);
            } catch (IllegalArgumentException e) {
                SbMagicHook.warn("UUID không hợp lệ cho Discord ID " + discordId + ": " + uuidString);
                removeConnection(discordId);
            }
        }
        return null;
    }
    
    /**
     * Lấy Discord ID từ Minecraft UUID
     */
    public String getDiscordID(UUID minecraftUuid) {
        String uuidString = minecraftUuid.toString();
        for (String discordId : storage.getConfigurationSection("connections").getKeys(false)) {
            if (uuidString.equals(storage.getString("connections." + discordId))) {
                return discordId;
            }
        }
        return null;
    }
    
    /**
     * Kiểm tra Discord user đã kết nối chưa
     */
    public boolean isConnected(String discordId) {
        return storage.contains("connections." + discordId);
    }
    
    /**
     * Kiểm tra Minecraft player đã kết nối chưa
     */
    public boolean isConnected(UUID minecraftUuid) {
        return getDiscordID(minecraftUuid) != null;
    }
    
    /**
     * Lấy tất cả kết nối
     */
    public Map<String, UUID> getAllConnections() {
        Map<String, UUID> connections = new HashMap<>();
        if (storage.getConfigurationSection("connections") != null) {
            for (String discordId : storage.getConfigurationSection("connections").getKeys(false)) {
                UUID uuid = getMinecraftUUID(discordId);
                if (uuid != null) {
                    connections.put(discordId, uuid);
                }
            }
        }
        return connections;
    }
    
    /**
     * Lấy số lượng kết nối
     */
    public int getConnectionCount() {
        return storage.getConfigurationSection("connections") != null ? 
            storage.getConfigurationSection("connections").getKeys(false).size() : 0;
    }
    
    /**
     * Xóa tất cả kết nối
     */
    public void clearAll() {
        storage.set("connections", null);
        saveStorage();
    }
}
