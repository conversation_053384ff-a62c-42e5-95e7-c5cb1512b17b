package shyrcs.discordbot.top.models;

import java.util.List;

public class TopConfig {
    
    private final String id;
    private final String name;
    private final String description;
    private final int color;
    private final String thumbnail;
    private final List<String> content;
    
    public TopConfig(String id, String name, String description, int color, String thumbnail, List<String> content) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.color = color;
        this.thumbnail = thumbnail;
        this.content = content;
    }
    
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public int getColor() {
        return color;
    }
    
    public String getThumbnail() {
        return thumbnail;
    }
    
    public List<String> getContent() {
        return content;
    }
}
