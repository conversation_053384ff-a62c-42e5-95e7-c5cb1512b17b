menu_title: '&b<PERSON><PERSON><PERSON> diện chế tạo &fIngredients'
# open_command:
# - qc
# - quickcraft
size: 54
items:
  #====================================================================#
  #                           KHUNG GIAO DIỆN                          #
  #====================================================================#
  'Fill':
    material: GRAY_STAINED_GLASS_PANE
    display_name: '&f'
    slots:
    - 0-53
  'Fill-B':
    material: WHITE_STAINED_GLASS_PANE
    display_name: '&f'
    slots: [9,18,27,36,45,17,26,35,44,53]
  'U-Bar':
    material: BLUE_STAINED_GLASS_PANE
    display_name: '&f'
    slots:
    - 0-8
    - 45-53

  'close':
    material: BARRIER
    slot: 54
    display_name: '&cĐóng'
    left_click_commands:
    - '[close]'

  #====================================================================#
  #                        CÁC VẬT PHẨM RÚT                            #
  #====================================================================#
  'rut_than':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvYmRhN2RmMGM2NzYxNjMwNWNlZTI5ZTRlYTJlMzQwNjE1NDYzNjY5NWVmZWY5ZmY3MzVlZmQwMDc0YjAwMjg0YSJ9fX0=
    slot: 20
    display_name: '&7[&fIngredients&7] &8Than Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &8Than Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &8Than Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &8Than Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &8Than Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &8Than Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_COAL%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_COAL% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take COAL 128 %player_name%'
    - '[console] mi give MATERIAL COAL_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_COAL% >= 4096 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take COAL 4096 %player_name%'
    - '[console] mi give MATERIAL COAL_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_COAL% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take COAL 8192 %player_name%'
    - '[console] mi give MATERIAL COAL_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_COAL% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take COAL 73728 %player_name%'
    - '[console] mi give MATERIAL COAL_TINH_KHIET %player_name% 576'
    - '[refresh]'


  'rut_luuly':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNTUxODliMzc5YTc4ODBmZjlhNGJiZDU4OGFkYjRlMWI3YjljMzM0MWRlN2Q2ZDAwNmQzNjJhZTU0NTBkYTk4NiJ9fX0=
    slot: 21
    display_name: '&7[&fIngredients&7] &9Lưu Ly Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &9Lưu Ly Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &9Lưu Ly Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &9Lưu Ly Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &9Lưu Ly Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &9Lưu Ly Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_LAPIS_LAZULI%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_LAPIS_LAZULI% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take LAPIS_LAZULI 128 %player_name%'
    - '[console] mi give MATERIAL LAPIS_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_LAPIS_LAZULI% >= 4096 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take LAPIS_LAZULI 4096 %player_name%'
    - '[console] mi give MATERIAL LAPIS_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_LAPIS_LAZULI% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take LAPIS_LAZULI 8192 %player_name%'
    - '[console] mi give MATERIAL LAPIS_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_LAPIS_LAZULI% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take LAPIS_LAZULI 73728 %player_name%'
    - '[console] mi give MATERIAL LAPIS_TINH_KHIET %player_name% 576'
    - '[refresh]'

  'rut_dado':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvODg5ODg1MjNmMjYzMWRlNWNiMDFmZGVhMzg3MDViNjRlYjkwNjY2N2Q4ZDk5YmNiODU5YTBhMTZkYjU5MWE3OCJ9fX0=
    slot: 22
    display_name: '&7[&fIngredients&7] &cĐá Đỏ Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &cĐá Đỏ Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &cĐá Đỏ Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &cĐá Đỏ Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &cĐá Đỏ Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &cĐá Đỏ Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_REDSTONE%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_REDSTONE% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take REDSTONE 128 %player_name%'
    - '[console] mi give MATERIAL REDSTONE_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_REDSTONE% >= 4096 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take REDSTONE 4096 %player_name%'
    - '[console] mi give MATERIAL REDSTONE_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_REDSTONE% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take REDSTONE 8192 %player_name%'
    - '[console] mi give MATERIAL REDSTONE_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_REDSTONE% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take REDSTONE 73728 %player_name%'
    - '[console] mi give MATERIAL REDSTONE_TINH_KHIET %player_name% 576'
    - '[refresh]'
  
  'rut_dong':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZTI4YmMyZGYzZGZmNWVjM2QxNDNhMmNlZWNiOTM4MjE4MzUxNmU5ZmFkZDdlNDA5YTVlYjQxNTUxNTc0YTI1ZiJ9fX0=
    slot: 23
    display_name: '&7[&fIngredients&7] &#A59616Đồng Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &#A59616Đồng Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &#A59616Đồng Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &#A59616Đồng Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &#A59616Đồng Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &#A59616Đồng Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_COPPER_INGOT%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_COPPER_INGOT% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take COPPER_INGOT 128 %player_name%'
    - '[console] mi give MATERIAL COPPER_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_COPPER_INGOT% >= 4096 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take COPPER_INGOT 4096 %player_name%'
    - '[console] mi give MATERIAL COPPER_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_COPPER_INGOT% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take COPPER_INGOT 8192 %player_name%'
    - '[console] mi give MATERIAL COPPER_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_COPPER_INGOT% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take COPPER_INGOT 73728 %player_name%'
    - '[console] mi give MATERIAL COPPER_TINH_KHIET %player_name% 576'
    - '[refresh]'
  
  'rut_sat':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvYTYzYjg3ZWU2YTU1ZjJiZjAxMzViMjZiZDk2ZWMyNzllZGVkMTc1Zjk0OGMwMzZkODhhNjA3MjVlMTI3MzcxYyJ9fX0=
    slot: 24
    display_name: '&7[&fIngredients&7] &fSắt Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &fSắt Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &fSắt Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &fSắt Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &fSắt Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &fSắt Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_IRON_INGOT%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_IRON_INGOT% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take IRON_INGOT 128 %player_name%'
    - '[console] mi give MATERIAL IRON_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_IRON_INGOT% >= 4096 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take IRON_INGOT 4096 %player_name%'
    - '[console] mi give MATERIAL IRON_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_IRON_INGOT% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take IRON_INGOT 8192 %player_name%'
    - '[console] mi give MATERIAL IRON_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_IRON_INGOT% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take IRON_INGOT 73728 %player_name%'
    - '[console] mi give MATERIAL IRON_TINH_KHIET %player_name% 576'
    - '[refresh]'

  'rut_vang':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNDFiNWZkZWZmZmZmYTcwODM4MWU3MGYzNTAzYTI3NTc3MmI0NTI5NmNmOWYxNjI1YTg3ZWRjNmI2MjU0OWVmNiJ9fX0=
    slot: 29
    display_name: '&7[&fIngredients&7] &eVàng Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &eVàng Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &eVàng Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &eVàng Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &eVàng Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &eVàng Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_GOLD_INGOT%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_GOLD_INGOT% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take GOLD_INGOT 128 %player_name%'
    - '[console] mi give MATERIAL GOLD_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_GOLD_INGOT% >= 2048 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take GOLD_INGOT 2048 %player_name%'
    - '[console] mi give MATERIAL GOLD_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_GOLD_INGOT% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take GOLD_INGOT 8192 %player_name%'
    - '[console] mi give MATERIAL GOLD_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_GOLD_INGOT% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take GOLD_INGOT 73728 %player_name%'
    - '[console] mi give MATERIAL GOLD_TINH_KHIET %player_name% 576'
    - '[refresh]'

  'rut_kimcuong':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNzYxNjVhZjg0MmQ3MWRjNTg4MjRjNzZhZjEwNDE2MzE2YTU1YWNlZTVhYzY5YWUxMWE4MWE2NWQ0ZDQ1Mzk2MSJ9fX0=
    slot: 30
    display_name: '&7[&fIngredients&7] &bKim Cương Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &bKim Cương Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &bKim Cương Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &bKim Cương Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &bKim Cương Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &bKim Cương Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_DIAMOND%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_DIAMOND% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take DIAMOND 128 %player_name%'
    - '[console] mi give MATERIAL DIAMOND_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_DIAMOND% >= 2048 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take DIAMOND 2048 %player_name%'
    - '[console] mi give MATERIAL DIAMOND_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_DIAMOND% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take DIAMOND 8192 %player_name%'
    - '[console] mi give MATERIAL DIAMOND_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_DIAMOND% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take DIAMOND 73728 %player_name%'
    - '[console] mi give MATERIAL DIAMOND_TINH_KHIET %player_name% 576'
    - '[refresh]'
  
  'rut_ngoclucbao':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvOTk4NWU5NjEyMjgzOTNhNGNkNTdmYWFlMTdiZmIwN2JkMGZkYTA4NTMxNmZkOGJlNTg0NTk4NzRkZWNjM2JjOCJ9fX0=
    slot: 31
    display_name: '&7[&fIngredients&7] &aLục Bảo Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &aLục Bảo Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &aLục Bảo Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &aLục Bảo Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &aLục Bảo Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &aLục Bảo Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_EMERALD%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_EMERALD% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take EMERALD 128 %player_name%'
    - '[console] mi give MATERIAL EMERALD_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_EMERALD% >= 2048 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take EMERALD 2048 %player_name%'
    - '[console] mi give MATERIAL EMERALD_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_EMERALD% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take EMERALD 8192 %player_name%'
    - '[console] mi give MATERIAL EMERALD_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_EMERALD% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take EMERALD 73728 %player_name%'
    - '[console] mi give MATERIAL EMERALD_TINH_KHIET %player_name% 576'
    - '[refresh]'
  
  'rut_netherite':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvZTViMzE3MjM2MDNjYjA3YzE1MGUzNGQ2NWJlODhlNWE2MWFhNDY0NGFhNjI2Y2RlNTI0ZjNiZDg2MTRmY2QyOSJ9fX0=
    slot: 32
    display_name: '&7[&fIngredients&7] &#5B5A4ANetherite Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &#5B5A4ANetherite Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &#5B5A4ANetherite Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &#5B5A4ANetherite Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &#5B5A4ANetherite Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &#5B5A4ANetherite Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_NETHERITE_INGOT%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_NETHERITE_INGOT% >= 32 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take NETHERITE_INGOT 32 %player_name%'
    - '[console] mi give MATERIAL NETHERITE_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_NETHERITE_INGOT% >= 1024 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take NETHERITE_INGOT 1024 %player_name%'
    - '[console] mi give MATERIAL NETHERITE_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_NETHERITE_INGOT% >= 2048 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take NETHERITE_INGOT 2048 %player_name%'
    - '[console] mi give MATERIAL NETHERITE_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_NETHERITE_INGOT% >= 18432 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take NETHERITE_INGOT 18432 %player_name%'
    - '[console] mi give MATERIAL NETHERITE_TINH_KHIET %player_name% 576'
    - '[refresh]'

  'rut_amethyst':
    material: basehead-eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNjVhZGVhODk4NDUyYmZhNzJkOTE4ZGEyMTVlOTVhYWI2MWNiOWVhM2M4ZTI2NDFjZjA3ZjAzMzY1ZDEzN2M4ZSJ9fX0=
    slot: 33
    display_name: '&7[&fIngredients&7] &dThạch Anh Tinh Khiết'
    lore:
    - ''
    - '&7Chế Tạo &dThạch Anh Tinh Khiết'
    - '&b[Chuột-Trái] &eChế Tạo 1 &dThạch Anh Tinh Khiết.'
    - '&b[Chuột-Phải] &eChế Tạo 32 &dThạch Anh Tinh Khiết.'
    - '&b[Shift Chuột-Trái] &eChế Tạo 1 Stack &dThạch Anh Tinh Khiết.'
    - '&b[Shift Chuột-Phải] &eChế Tạo 9 Stack &dThạch Anh Tinh Khiết.'
    - ''
    - '&7Khoáng sản của bạn: &a%exstorage_quantity_AMETHYST_BLOCK%'
    - '&7Slot trống: &a%player_empty_slots%'
    - ''
    - '&a▶ Nhấn để chế tạo'
    
    left_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_AMETHYST_BLOCK% >= 128 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    left_click_commands:
    - '[console] esadmin take AMETHYST_BLOCK 128 %player_name%'
    - '[console] mi give MATERIAL AMETHYST_TINH_KHIET %player_name% 1'
    - '[refresh]'

    right_click_requirement:
      requirements:
        javascript:
          type: 'javascript'
          expression: '%exstorage_quantity_AMETHYST_BLOCK% >= 2048 && %player_empty_slots% >= 1'
          deny_commands:
          - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
          - '[sound] BLOCK_ANVIL_LAND'
    right_click_commands:
    - '[console] esadmin take AMETHYST_BLOCK 2048 %player_name%'
    - '[console] mi give MATERIAL AMETHYST_TINH_KHIET %player_name% 32'
    - '[refresh]'
    
    shift_left_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_AMETHYST_BLOCK% >= 8192 && %player_empty_slots% >= 1'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_left_click_commands:
    - '[console] esadmin take AMETHYST_BLOCK 8192 %player_name%'
    - '[console] mi give MATERIAL AMETHYST_TINH_KHIET %player_name% 64'
    - '[refresh]'

    shift_right_click_requirement:
        requirements:
          javascript:
            type: 'javascript'
            expression: '%exstorage_quantity_AMETHYST_BLOCK% >= 73728 && %player_empty_slots% >= 9'
            deny_commands:
            - '[message] &8◇ &5Tiện &fích &f⇨ &cBạn không đủ khoáng sản hoặc slot trống để rút!'
            - '[sound] BLOCK_ANVIL_LAND'
    shift_right_click_commands:
    - '[console] esadmin take AMETHYST_BLOCK 73728 %player_name%'
    - '[console] mi give MATERIAL AMETHYST_TINH_KHIET %player_name% 576'
    - '[refresh]'