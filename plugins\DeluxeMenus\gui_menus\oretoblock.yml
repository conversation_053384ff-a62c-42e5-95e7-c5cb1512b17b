menu_title: '&nĐổɪ Qᴜặɴɢ ꜱᴀɴɢ ᴋʜốɪ'
open_sound: ENTITY_PLAYER_LEVELUP
size: 54
items:
  'glass':
    material: orange_stained_glass_pane
    display_name: '&7 '
    slots:
    - 0
    - 1
    - 2
    - 3
    - 9
    - 18
    - 26
    - 27
    - 35
    - 44
    - 50
    - 51
    - 52
    - 53
    - 4
    - 5
    - 6
    - 7
    - 8
    - 17
    - 36
    - 45
    - 46
    - 47
    - 48
    - 49
  coal:
    amount: 1
    material: COAL_BLOCK
    display_name: '&8[&#B1FFE6ᴛʀᴀᴅᴇ&8] &#A3EDFFĐổi Qᴜặɴɢ ꜱᴀɴɢ ᴋʜốɪ'
    slot: 10
    lore:
    - ''
    - ' &7Dùng ore ở &okho'
    - ' &7để đổi thành khối tương ứng'
    - ' '
    - ' &#FFEDB1○ &fCần&7:&#FFD5B8 9216 &8Coal'
    - ' &#FFEDB1○ &fKho đang có: &#FFD5B8%exstorage_quantity_formatted_coal% &8Coal'
    - ' &#FFEDB1○ &fNhận&7: &#FFD5B8x1024 &8Coal Block'
    - ''
    - '&8[&#FFEDB1✔&8] &#95FF70Nhấn &fvào để đổi'
    click_commands:
    - '[console] extrastorage:esadmin subtract COAL 9216 %player_name%'
    - '[console] extrastorage:esadmin add COAL_BLOCK 1024 %player_name%'
    - '[sound] BLOCK_AMETHYST_BLOCK_BREAK'
    - '[refresh]<delay=-100>'
    click_requirement:
      requirements:
        block:
          type: '>='
          input: '%exstorage_quantity_COAL%'
          output: '9216'
          deny_commands:
          - '[console] titlemanager msg %player_name% &8◆ &c&lTHẤT BẠI &8◆\n&fBạn không đủ nguyên liệu'  
          - '[sound] BLOCK_BEACON_DEACTIVATE'
  lapis:
    amount: 1
    material: LAPIS_BLOCK
    display_name: '&8[&#B1FFE6ᴛʀᴀᴅᴇ&8] &#A3EDFFĐổi Qᴜặɴɢ ꜱᴀɴɢ ᴋʜốɪ'
    slot: 11
    lore:
    - ''
    - ' &7Dùng ore ở &okho'
    - ' &7để đổi thành khối tương ứng'
    - ' '
    - ' &#FFEDB1○ &fCần&7:&#FFD5B8 9216 &bLapis'
    - ' &#FFEDB1○ &fKho đang có: &#FFD5B8%exstorage_quantity_formatted_lapis_lazuli% &blapis'
    - ' &#FFEDB1○ &fNhận&7: &#FFD5B8x1024 &bLapis Block'
    - ''
    - '&8[&#FFEDB1✔&8] &#95FF70Nhấn &fvào để đổi'
    click_commands:
    - '[console] extrastorage:esadmin subtract LAPIS_LAZULI 9216 %player_name%'
    - '[console] extrastorage:esadmin add LAPIS_BLOCK 1024 %player_name%'
    - '[sound] BLOCK_AMETHYST_BLOCK_BREAK'
    - '[refresh]<delay=-100>'
    click_requirement:
      requirements:
        block:
          type: '>='
          input: '%exstorage_quantity_LAPIS_LAZULI%'
          output: '9216'
          deny_commands:
          - '[console] titlemanager msg %player_name% &8◆ &c&lTHẤT BẠI &8◆\n&fBạn không đủ nguyên liệu'  
          - '[sound] BLOCK_BEACON_DEACTIVATE'
  redstone:
    amount: 1
    material: REDSTONE_BLOCK
    display_name: '&8[&#B1FFE6ᴛʀᴀᴅᴇ&8] &#A3EDFFĐổi Qᴜặɴɢ ꜱᴀɴɢ ᴋʜốɪ'
    slot: 12
    lore:
    - ''
    - ' &7Dùng ore ở &okho'
    - ' &7để đổi thành khối tương ứng'
    - ' '
    - ' &#FFEDB1○ &fCần&7:&#FFD5B8 9216 &aRedstone'
    - ' &#FFEDB1○ &fKho đang có: &#FFD5B8%exstorage_quantity_formatted_redstone% &aRedstone'
    - ' &#FFEDB1○ &fNhận&7: &#FFD5B8x1024 &aRedstone Block'
    - ''
    - '&8[&#FFEDB1✔&8] &#95FF70Nhấn &fvào để đổi'
    click_commands:
    - '[console] extrastorage:esadmin subtract REDSTONE 9216 %player_name%'
    - '[console] extrastorage:esadmin add REDSTONE_BLOCK 1024 %player_name%'
    - '[sound] BLOCK_AMETHYST_BLOCK_BREAK'
    - '[refresh]<delay=-100>'
    click_requirement:
      requirements:
        block:
          type: '>='
          input: '%exstorage_quantity_REDSTONE%'
          output: '9216'
          deny_commands:
          - '[console] titlemanager msg %player_name% &8◆ &c&lTHẤT BẠI &8◆\n&fBạn không đủ nguyên liệu'  
          - '[sound] BLOCK_BEACON_DEACTIVATE'
  raw_iron:
    amount: 1
    material: IRON_BLOCK
    display_name: '&8[&#B1FFE6ᴛʀᴀᴅᴇ&8] &#A3EDFFĐổi Qᴜặɴɢ ꜱᴀɴɢ ᴋʜốɪ'
    slot: 13
    lore:
    - ''
    - ' &7Dùng ore ở &okho'
    - ' &7để đổi thành khối tương ứng'
    - ' '
    - ' &#FFEDB1○ &fCần&7:&#FFD5B8 9216 &fIron Ingot'
    - ' &#FFEDB1○ &fKho đang có: &#FFD5B8%exstorage_quantity_formatted_iron_ingot% Iron Ingot'
    - ' &#FFEDB1○ &fNhận&7: &#FFD5B8x1024 &fIron Block'
    - ''
    - '&8[&#FFEDB1✔&8] &#95FF70Nhấn &fvào để đổi'
    click_commands:
    - '[console] extrastorage:esadmin subtract IRON_INGOT 9216 %player_name%'
    - '[console] extrastorage:esadmin add IRON_BLOCK 1024 %player_name%'
    - '[sound] BLOCK_AMETHYST_BLOCK_BREAK'
    - '[refresh]<delay=-100>'
    click_requirement:
      requirements:
        block:
          type: '>='
          input: '%exstorage_quantity_IRON_INGOT%'
          output: '9216'
          deny_commands:
          - '[console] titlemanager msg %player_name% &8◆ &c&lTHẤT BẠI &8◆\n&fBạn không đủ nguyên liệu'  
          - '[sound] BLOCK_BEACON_DEACTIVATE'
  raw_gold:
    amount: 1
    material: GOLD_BLOCK
    display_name: '&8[&#B1FFE6ᴛʀᴀᴅᴇ&8] &#A3EDFFĐổi Qᴜặɴɢ ꜱᴀɴɢ ᴋʜốɪ'
    slot: 14
    lore:
    - ''
    - ' &7Dùng ore ở &okho'
    - ' &7để đổi thành khối tương ứng'
    - ' '
    - ' &#FFEDB1○ &fCần&7:&#FFD5B8 9216 &eRaw Gold'
    - ' &#FFEDB1○ &fKho đang có: &#FFD5B8%exstorage_quantity_formatted_raw_gold% &eRaw Gold'
    - ' &#FFEDB1○ &fNhận&7: &#FFD5B8x1024 &eGold Block'
    - ''
    - '&8[&#FFEDB1✔&8] &#95FF70Nhấn &fvào để đổi'
    click_commands:
    - '[console] extrastorage:esadmin subtract RAW_GOLD 9216 %player_name%'
    - '[console] extrastorage:esadmin add GOLD_BLOCK 1024 %player_name%'
    - '[sound] BLOCK_AMETHYST_BLOCK_BREAK'
    - '[refresh]<delay=-100>'
    click_requirement:
      requirements:
        block:
          type: '>='
          input: '%exstorage_quantity_RAW_GOLD%'
          output: '9216'
          deny_commands:
          - '[console] titlemanager msg %player_name% &8◆ &c&lTHẤT BẠI &8◆\n&fBạn không đủ nguyên liệu'  
          - '[sound] BLOCK_BEACON_DEACTIVATE'
  diamond:
    amount: 1
    material: DIAMOND_BLOCK
    display_name: '&8[&#B1FFE6ᴛʀᴀᴅᴇ&8] &#A3EDFFĐổi Qᴜặɴɢ ꜱᴀɴɢ ᴋʜốɪ'
    slot: 15
    lore:
    - ''
    - ' &7Dùng ore ở &okho'
    - ' &7để đổi thành khối tương ứng'
    - ' '
    - ' &#FFEDB1○ &fCần&7:&#FFD5B8 9216 &bDiamond'
    - ' &#FFEDB1○ &fKho đang có: &#FFD5B8%exstorage_quantity_formatted_diamond% &bDiamond'
    - ' &#FFEDB1○ &fNhận&7: &#FFD5B8x1024 &aDiamond Block'
    - ''
    - '&8[&#FFEDB1✔&8] &#95FF70Nhấn &fvào để đổi'
    click_commands:
    - '[console] extrastorage:esadmin subtract DIAMOND 9216 %player_name%'
    - '[console] extrastorage:esadmin add DIAMOND_BLOCK 1024 %player_name%'
    - '[sound] BLOCK_AMETHYST_BLOCK_BREAK'
    - '[refresh]<delay=-100>'
    click_requirement:
      requirements:
        block:
          type: '>='
          input: '%exstorage_quantity_DIAMOND%'
          output: '9216'
          deny_commands:
          - '[console] titlemanager msg %player_name% &8◆ &c&lTHẤT BẠI &8◆\n&fBạn không đủ nguyên liệu'  
          - '[sound] BLOCK_BEACON_DEACTIVATE'
  emerald:
    amount: 1
    material: EMERALD_BLOCK
    display_name: '&8[&#B1FFE6ᴛʀᴀᴅᴇ&8] &#A3EDFFĐổi Qᴜặɴɢ ꜱᴀɴɢ ᴋʜốɪ'
    slot: 16
    lore:
    - ''
    - ' &7Dùng ore ở &okho'
    - ' &7để đổi thành khối tương ứng'
    - ' '
    - ' &#FFEDB1○ &fCần&7:&#FFD5B8 9216 &aEmerald'
    - ' &#FFEDB1○ &fKho đang có: &#FFD5B8%exstorage_quantity_formatted_emerald% &aEmerald'
    - ' &#FFEDB1○ &fNhận&7: &#FFD5B8x1024 &aEmerald Block'
    - ''
    - '&8[&#FFEDB1✔&8] &#95FF70Nhấn &fvào để đổi'
    click_commands:
    - '[console] extrastorage:esadmin subtract EMERALD 9216 %player_name%'
    - '[console] extrastorage:esadmin add EMERALD_BLOCK 1024 %player_name%'
    - '[sound] BLOCK_AMETHYST_BLOCK_BREAK'
    - '[refresh]<delay=-100>'
    click_requirement:
      requirements:
        block:
          type: '>='
          input: '%exstorage_quantity_EMERALD%'
          output: '9216'
          deny_commands:
          - '[console] titlemanager msg %player_name% &8◆ &c&lTHẤT BẠI &8◆\n&fBạn không đủ nguyên liệu'  
          - '[sound] BLOCK_BEACON_DEACTIVATE'